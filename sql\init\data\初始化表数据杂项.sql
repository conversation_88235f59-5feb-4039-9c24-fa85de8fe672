--修复退款单金额，实际支付金额合系统记录金额不一致
update
  trd_after
set
  refund_amt = 201.09,
  return_order_amt = 201.09,
  pay_amt = 201.09
where
  after_no = 'SH250614110001416032';

update
  trd_supplier_after
set
  sub_refund_amt = sub_refund_amt - sub_refund_fee,
  return_sub_order_amt = return_sub_order_amt - sub_refund_fee
where
  after_no = 'SH250614110001416032';

update
  acc_pay_flow
set
  pay_amt = 201.09,
  refund_amt = 201.09
where
  refund_no = 'TK250614440001448635';



-- 批量修改门店的价格码
update mem_branch set sale_price_code = 1 where sys_code = 615051269697175552;

-- 设置商品来源系统，可以让前端编辑库存
update prdt_spu set source = 'B2B' where  spu_no = '365';

-- 删除已复制的城市管理分类
delete from prdt_area_class  where sys_code = 610330619602468864 and area_id = 115;

-- 批量修改门店货到付款标记，在线收款标记
update mem_branch set hdfk_support = 1, hdfk_max_amt = 1000000, is_pay_online = 0  where t.sys_code = 610330619602468864;

--商品上架导数
select
	concat(p.supplier_id, ',') as 入驻商,
	p.spu_no as 产品编码,
	p.spu_name as 产品名称,
	k.barcode as 产品条码,
	p.brand_id as 品牌名称,
	sc.name as 管理类别,
	i.area_id as 上架城市,
	c.catgory_name as 展示类别,
	i.min_shelf_status as 小单位是否上下架,
	i.mid_shelf_status as 中单位是否上下架,
	i.large_shelf_status as 大单位是否上下架,
	i.shelf_date as 上架时间,
	p.min_unit as 小单位名称,
	p.mid_unit as 中单位名称,
	p.large_unit as 大单位名称,
	k.mark_price as 标准价小单位,
	k.mid_mark_price as 标准价中单位,
	k.large_mark_price as 标准价大单位,
	k.suggest_price as 零售价小单位,
	k.mid_suggest_price as 零售价中单位,
	k.large_suggest_price as 零售价大单位,
	k.expiration_date as 保质期
from
	prdt_area_item i
left join prdt_spu p on
	i.spu_id = p.spu_id
left join prdt_sku k
on
	p.spu_id = k.spu_id
left join prdt_catgory c on
	i.area_class_id = c.catgory_id
left join prdt_sale_class sc on
	i.area_class_id = sc.sale_class_id
where
	i.sys_code = 610330619602468864
	and k.sys_code = i.sys_code
	and k.sys_code = p.sys_code
	and k.sys_code = i.sys_code
	and k.is_delete = 0
	and p.is_delete = 0

--------redis 修数，
-- 1.导出脏数据
    select
      concat('\'', t.sku_id, '\'') skuid,
      t.sale_qty,
      t.synced_qty,
      t.stock
    from
      prdt_sku t
    where
      t.synced_qty > sale_qty;

-- 2.打开跳板机登录redis工具，找到skuid同步库存缓存key更改值:
  sku_stock:skuid:synced_stock_qty

-- 3.重新计算同步数量,直接用sync_stock更新缓存：
    select
      concat('\'', l.sku_id, '\'') as skuid,
      sum(l.total_num - l.cancel_qty) as sale_qty,
      sum(l.sync_stock * l.total_num - l.cancel_qty) as sync_stock,
      sum(l.cancel_qty) as cancel_qty
    from
      trd_supplier_order h,
      trd_supplier_order_dtl l
    where
      h.supplier_order_id = l.supplier_order_id
      and l.sku_id = 618110175470387231
    group by
      l.sku_id;

-- 4.核实sku表同步数量：
    select
      t.stock,
      t.sale_qty,
      t.synced_qty,
      t.*
    from
      prdt_sku t
    where
      t.sku_id = 615861064306163719;

-- 东健取消对接erp，操作订单收货
-- 先删除对接记录
delete from sys_opensource  where merchant_id = 615802236138946560;
-- 备份对接记录
INSERT INTO
  zksr_cloud.sys_opensource (
    `opensource_id`,
    `sys_code`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `merchant_id`,
    `merchant_type`,
    `source_key`,
    `source_secret`,
    `ip_white_list`,
    `token`,
    `send_code`,
    `public_key`,
    `private_key`,
    `send_url`,
    `logistics_info`,
    `order_delay_push_time`,
    `order_auto_push`,
    `visual_master_id`,
    `sync_mark_price`,
    `super_supplier`,
    `is_hdfk_settle`,
    `sync_cost_price`,
    `gift_price_type`,
    `order_merge_flag`,
    `alarm_email`,
    `subscribe_send_email`
  )
VALUES
  (
    '615803464499593216',
    '615051269697175552',
    NULL,
    '2025-05-14 11:11:57.682',
    '',
    '2025-06-28 13:40:06.566',
    '615802236138946560',
    'supplier',
    'ed1a87e1b6344754bdf1930e2a9f4957',
    'QjU1QUMyQURERkE2N0Q3NThCMUZDRUU0NDkyMDg4RjNDRjk3QTYyQU5rTkNSVGszUVRkRk1UZEJRVGN3TmpNNVJVWkVPRFUxTWpFNFJqQTJNa0U9',
    NULL,
    '5f3249f8-0abb-4d13-a108-bae7b3aba855',
    '2000000000000000024:100',
    'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKq2FfKsEUQkLt6tK4RKOqP3tVpyKkcJwzDKmv6pL_wua9f4Bif3vxpxs4_V1kTVk6k9qscA5Zru544ZXrS7T_kCAwEAAQ',
    '',
    'https://saasopen.annto.com/bop/annto/saasB2B',
    '0',
    '1',
    '1',
    '507521519306539008',
    '1',
    '0',
    '0',
    '1',
    '0',
    '0',
    NULL,
    ''
  );

-- 刷供货价（成本价）
update
  prdt_sku
set
  cost_price = 1,
  large_cost_price = 1
where
  spu_id = 634425703961919498;

--------短信配置鹊桥鉴权
delete from `zksr_cloud`.`sys_partner_config` where config_type = '14' and sys_code = 13;
INSERT INTO `zksr_cloud`.`sys_partner_config`
( sys_code, create_by, create_time, update_by, update_time, config_name, config_key, config_value, config_type)
VALUES( 13, 'init', now(), 'init', now(), '短信配置', 'smsConfig.bopClientId', 'c59b6ffdf9c24571bf6115f7b49b9021', '14');
INSERT INTO `zksr_cloud`.`sys_partner_config`
( sys_code, create_by, create_time, update_by, update_time, config_name, config_key, config_value, config_type)
VALUES( 13, 'init', now(), 'init', now(), '短信配置', 'smsConfig.bopClientSecret', '05e20aaddfdc480aa4d323ec6c9fb0ff', '14');
INSERT INTO `zksr_cloud`.`sys_partner_config`
( sys_code, create_by, create_time, update_by, update_time, config_name, config_key, config_value, config_type)
VALUES( 13, 'init', now(), 'init', now(), '短信配置', 'smsConfig.bopAccessToken', 'c9604d78-d035-4c0e-a060-95e710234491', '14');
INSERT INTO `zksr_cloud`.`sys_partner_config`
( sys_code, create_by, create_time, update_by, update_time, config_name, config_key, config_value, config_type)
VALUES( 13, 'init', now(), 'init', now(), '短信配置', 'smsConfig.bopUrl', 'https://saasopen.annto.com/bop/annto/message/sendMessage', '14');
INSERT INTO `zksr_cloud`.`sys_partner_config`
( sys_code, create_by, create_time, update_by, update_time, config_name, config_key, config_value, config_type)
VALUES( 13, 'init', now(), 'init', now(), '短信配置', 'smsConfig.tenantCode', 'b2bmall', '14');
INSERT INTO `zksr_cloud`.`sys_partner_config`
( sys_code, create_by, create_time, update_by, update_time, config_name, config_key, config_value, config_type)
VALUES( 13, 'init', now(), 'init', now(), '短信配置', 'smsConfig.signName', '安得智联', '14');
-- 平台商短信平台配置
INSERT INTO
  zksr_cloud.sys_partner_config (
    `partner_config_id`,
    `sys_code`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `config_name`,
    `config_key`,
    `config_value`,
    `config_type`
  )
VALUES
  (
    '1',
    '13',
    NULL,
    '2025-07-07 15:07:53.953',
    'annto',
    '2025-07-07 16:24:08.423',
    '短信配置',
    'smsConfig.smsPlatform',
    'tencent',
    '14'
  );
-- 模板配置：
INSERT INTO zksr_cloud.sys_sms_template
(sms_template_id, sys_code, create_by, create_time, update_by, update_time, scene, template_code, content, sign_name, halfhour_count, platform, status)
VALUES(5, 13, '18600000000', '2024-08-01 11:41:10.804000000', '18600000000', '2024-08-01 11:41:37.860000000', 0, '111', '您的验证码为${code}，该验证码5分钟有效，请勿泄露他人.', '安得智联', 5, 'tencent', 0);

-- 更新订单状态历史数据
UPDATE `zksr_trade`.trd_supplier_order AS tso
JOIN (
    SELECT supplier_order_id, delivery_state
    FROM `zksr_trade`.trd_supplier_order_dtl
    WHERE supplier_order_dtl_id IN (
        SELECT MIN(supplier_order_dtl_id)
        FROM `zksr_trade`.trd_supplier_order_dtl
        GROUP BY supplier_order_id
    )
) AS first_row
ON tso.supplier_order_id = first_row.supplier_order_id
SET tso.delivery_state = CASE
WHEN first_row.delivery_state = 0 THEN 0
    WHEN first_row.delivery_state = 2 THEN 50
    WHEN first_row.delivery_state = 3 THEN 10
    WHEN first_row.delivery_state = 4 THEN 20
    WHEN first_row.delivery_state = 7 THEN 21
    WHEN first_row.delivery_state = 6 THEN 21
    WHEN first_row.delivery_state = 5 THEN 21
    ELSE tso.delivery_state -- 保持原值不变
END;

-- 按订单明细关系修复入驻商和主单关系
UPDATE `zksr_trade`.trd_supplier_order AS tso
JOIN (
    SELECT d.order_id, h.order_no,d.supplier_order_id
    FROM `zksr_trade`.trd_supplier_order_dtl d, `zksr_trade`.trd_order h
    WHERE d.order_id = h.order_id
    and d.supplier_order_dtl_id IN (
        SELECT MIN(supplier_order_dtl_id)
        FROM `zksr_trade`.trd_supplier_order_dtl
        GROUP BY supplier_order_id
    )
) AS first_row
ON tso.supplier_order_id = first_row.supplier_order_id
SET tso.order_id = first_row.order_id,tso.order_no = first_row.order_no;
