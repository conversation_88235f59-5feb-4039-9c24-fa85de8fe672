package com.zksr.product.api.areaChannelPrice;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.areaChannelPrice.dto.AreaChannelPriceDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        contextId = "remoteAreaChannelPriceApi",
        value = ApiConstants.NAME
)
/**
*
 *  城市价格方案服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface AreaChannelPriceApi {

    String PREFIX = ApiConstants.PREFIX + "/areaChannelPrice";

    @GetMapping(PREFIX + "/getPriceByAreaIdAndChannelId")
    public CommonResult<AreaChannelPriceDTO> getPriceByAreaIdAndChannelId(@RequestParam("areaId") Long areaId,@RequestParam("channelId") Long channelId);

    @GetMapping(PREFIX + "/getPriceByKey")
    public CommonResult<Integer> getPriceByKey(@RequestParam("key") String key);
}
