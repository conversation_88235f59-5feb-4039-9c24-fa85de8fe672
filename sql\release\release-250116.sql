-- 组合商品规格表
CREATE TABLE  `zksr_promotion`.`prm_cb_rule` (
                                                 `cb_rule_id` bigint(20) NOT NULL COMMENT '秒杀规则id',
                                                 `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                 `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                 `activity_id` bigint(20) NOT NULL COMMENT '活动id',
                                                 `spu_combine_id` bigint(20) NOT NULL COMMENT '组合商品id',
                                                 `shelf_class_id` bigint(20) NOT NULL COMMENT '上架展示分类id',
                                                 PRIMARY KEY (`cb_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组合商品规格表';

-- 组合商品表
CREATE TABLE `zksr_product`.`prdt_spu_combine` (
                                    `spu_combine_id` bigint(20) NOT NULL,
                                    `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商编号',
                                    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商ID',
                                    `area_id` bigint(20) DEFAULT NULL COMMENT '区域ID',
                                    `category_id` bigint(20) DEFAULT NULL COMMENT '管理分类ID',
                                    `spu_combine_no` varchar(32) DEFAULT NULL COMMENT '组合商品编号',
                                    `spu_combine_name` varchar(32) DEFAULT NULL COMMENT '组合商品名称',
                                    `thumb` varchar(255) DEFAULT NULL COMMENT '封面图片',
                                    `thumb_video` varchar(255) DEFAULT NULL COMMENT '封面视频',
                                    `images` text COMMENT '详情页轮播图',
                                    `details` text COMMENT '组合商品描述,富文本',
                                    `memo` varchar(128) DEFAULT NULL COMMENT '备注',
                                    `is_delete` int(11) DEFAULT NULL COMMENT '1-删除, 0-正常',
                                    `status` int(11) DEFAULT NULL COMMENT '1-启用,0-停用',
                                    `spec_name` varchar(32) DEFAULT NULL COMMENT '规格名称',
                                    `total_limit` int(11) DEFAULT NULL COMMENT '总限量',
                                    `min_oq` int(11) DEFAULT NULL COMMENT '最新起订',
                                    `jump_oq` int(11) DEFAULT NULL COMMENT '起订组数',
                                    `max_oq` int(11) DEFAULT NULL COMMENT '最大限购',
                                    `mark_price` decimal(12,2) DEFAULT NULL COMMENT '标准价',
                                    `suggest_price` decimal(12,2) DEFAULT NULL COMMENT '建议零售价',
                                    `sale_price1` decimal(12,2) DEFAULT NULL COMMENT '销售价1',
                                    `sale_price2` decimal(12,2) DEFAULT NULL COMMENT '销售价2',
                                    `sale_price3` decimal(12,2) DEFAULT NULL COMMENT '销售价3',
                                    `sale_price4` decimal(12,2) DEFAULT NULL COMMENT '销售价4',
                                    `sale_price5` decimal(12,2) DEFAULT NULL COMMENT '销售价5',
                                    `sale_price6` decimal(12,2) DEFAULT NULL COMMENT '销售价6',
                                    `unit` int(10) DEFAULT NULL COMMENT '单位/字典值',
                                    PRIMARY KEY (`spu_combine_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 组合商品详情
CREATE TABLE `zksr_product`.`prdt_spu_combine_dtl` (
                                        `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                        `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                        `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                        `spu_combine_id` bigint(20) DEFAULT NULL,
                                        `sku_id` bigint(20) DEFAULT NULL,
                                        `sku_unit_type` int(4) DEFAULT NULL COMMENT '商品单位大小',
                                        `qty` int(8) DEFAULT NULL COMMENT '数量',
                                        `gift_flag` tinyint(1) DEFAULT NULL COMMENT '是否为赠品',
                                        `area_item_id` bigint(20) DEFAULT NULL,
                                        `supplier_item_id` bigint(20) DEFAULT NULL,
                                        `spu_combine_dtl_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        PRIMARY KEY (`spu_combine_dtl_id`),
                                        KEY `idx_spu_combine_id` (`spu_combine_id`),
                                        KEY `idx_sku_id` (`sku_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COMMENT='组合商品详情';

-- 组合促销上架表改动
ALTER TABLE `zksr_product`.`prdt_supplier_item`
    ADD COLUMN `item_type` tinyint(1) NULL DEFAULT 0 COMMENT '0-普通商品, 1-组合商品' AFTER `large_shelf_status`,
    ADD COLUMN `activity_start_time` datetime NULL DEFAULT NULL COMMENT '活动开始时间, 活动商品',
    ADD COLUMN `activity_end_time` datetime NULL DEFAULT NULL COMMENT '活动结束时间, 活动商品',
    ADD COLUMN `spu_combine_id` bigint(20) NULL DEFAULT NULL COMMENT '组合商品ID',
    ADD COLUMN `activity_id` bigint(20) NULL DEFAULT NULL COMMENT '活动ID';

ALTER TABLE `zksr_product`.`prdt_area_item`
    ADD COLUMN `item_type` tinyint(1) NULL DEFAULT 0 COMMENT '0-普通商品, 1-组合商品' AFTER `large_shelf_status`,
    ADD COLUMN `activity_start_time` datetime NULL DEFAULT NULL COMMENT '活动开始时间, 活动商品',
    ADD COLUMN `activity_end_time` datetime NULL DEFAULT NULL COMMENT '活动结束时间, 活动商品',
    ADD COLUMN `spu_combine_id` bigint(20) NULL DEFAULT NULL COMMENT '组合商品ID',
    ADD COLUMN `activity_id` bigint(20) NULL DEFAULT NULL COMMENT '活动ID';

ALTER TABLE `zksr_product`.`prdt_area_item`
    ADD INDEX `idx_spu_combine_id`(`spu_combine_id`);

ALTER TABLE `zksr_product`.`prdt_supplier_item`
    ADD INDEX `idx_spu_combine_id`(`spu_combine_id`);

ALTER TABLE `zksr_promotion`.`prm_activity_city_scope`
    ADD INDEX `idx_area_id`(`area_id`) USING BTREE;

-- 补货单数据兼容商品类型
ALTER TABLE `zksr_product`.`prdt_branch_yhdata`
    ADD COLUMN `item_type` tinyint(2) NULL DEFAULT 0 COMMENT '0-普通商品, 1-组合商品' AFTER `del_flag`;

-- 促销类型字典SQL
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('活动类型', 'activity_prm_no', '0', 'zksr', '2025-01-07 10:01:29', '', NULL, NULL);

INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '组合促销', 'CB', 'activity_prm_no', NULL, 'default', 'N', '0', 'zksr', '2025-01-07 10:03:17', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '特价', 'SP', 'activity_prm_no', NULL, 'default', 'N', '0', 'zksr', '2025-01-07 10:03:04', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '满减', 'FD', 'activity_prm_no', NULL, 'default', 'N', '0', 'zksr', '2025-01-07 10:02:39', 'zksr', '2025-01-07 10:02:56', NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '满赠', 'FG', 'activity_prm_no', NULL, 'default', 'N', '0', 'zksr', '2025-01-07 10:02:31', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '买赠', 'BG', 'activity_prm_no', NULL, 'default', 'N', '0', 'zksr', '2025-01-07 10:02:24', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '秒杀', 'SK', 'activity_prm_no', NULL, 'default', 'N', '0', 'zksr', '2025-01-07 10:01:56', '', NULL, NULL);

-- 首页配置增加扩展url字段
ALTER TABLE `zksr_cloud`.`sys_pages_config`
    ADD COLUMN `url_dtl` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拓展数据字段, 限制1024字符';

-- 订单编号改造  涉及对接第三方对接收款单模板更新
-- 安得ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n    \"companyName\": \"${branchName}\",\n    \"companyNo\": \"${branchNo}\",\n    \"companyType\": \"1\",\n    \"receiptType\": 0,\n    \"sheetDate\": \"${sheetDateString}\",\n    \"sheetType\": 0,\n    #if($sheetType == \"XR\")\n    \"changeType\": \"+\",\n    #elseif($sheetType == \"SR\")\n    \"changeType\": \"-\",\n    #else\n    \"changeType\": \"\",\n    #end\n    \"sheetNo\": \"${supplierOrderNo}\",\n     \"subList\": [\n      {\n        \"accountNo\": \"06\",\n        \"sheetAmt\": ${totalReceiptAmt}\n      }\n     ]\n}' WHERE `template_name` = '安得ERP收款单同步';


-- 福商通ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n  \"sheetAmt\":${totalReceiptAmt},       \n  \"actualAmtMB\":${totalReceiptExactAmt},   \n  \"consumerNo\":\"${branchNo}\",\n    \n  \"payMastersB2B\": [\n    #foreach( $sub in $detailList)\n    {\n  #if($sub.sheetType == \"XR\")\n  \"transNo\": \"XS\",  \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SJ\")\n  \"transNo\": \"JS\",\n  \"refundWay\":\"2\",\n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SC\")\n  \"transNo\": \"XY\",\n  \"refundWay\":\"1\",    \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #elseif($sub.sheetType == \"SR\" && $sub.isProceeds == 1)\n  \"transNo\": \"XT\",\n  \"refundWay\":\"3\",   \n  \"sheetNo\":\"${sub.supplierAfterNo}\",      \n  #elseif($sub.sheetType == \"SR\" && $sub.isProceeds == 0)\n  \"transNo\": \"QKSH\",\n  \"refundWay\":\"3\",     \n  \"sheetNo\":\"${sub.supplierOrderNo}\",      \n  #else  \n  \"transNo\": \"\",    \n  #end\n  #if($sub.payWay == \"0\")\n  \"payWay\": \"1\",\n  #elseif($sub.payWay == \"2\")\n  \"payWay\": \"2\",\n  #else  \n  \"payWay\": \"\" ,\n  #end        \n  \"sheetAmt\": \"${sub.receiptAmt}\",\n  \"actualAmtMB\": \"${sub.receiptExactAmt}\"      \n      }#if($foreach.hasNext),#end\n    #end\n    ]\n    }' WHERE `template_name` = '福商通ERP收款单同步';

-- 湘无界ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n    #set($firstItem = $detailList.get(0))\n    #set($payOpenList = $firstItem.payOpenList)\n  \"store_id\":\"${branchNo}\",\n  #if($firstItem.sheetType == \'XR\')\n    \"ori_order_id\": \"${firstItem.supplierOrderNo}\", \n    \"type\":\"1\",\n    #else\n    \"ori_order_id\": \"${firstItem.supplierAfterNo}\", \n    \"yao_ori_order_id\": \"${firstItem.supplierOrderNo}\", \n    \"type\":\"2\",\n    #end  \n    \"pay_list\": [\n        #foreach( $payOpenDTO in $payOpenList)\n        { \n        \"ori_trade_no\":\"${payOpenDTO.sheetTradeNo}\", \n       \"channel\":\"${payOpenDTO.platform}\", \n       \"amount\":${payOpenDTO.payAmt}, \n       \"money\":${payOpenDTO.payAmt},  \n       \"coupon\": 0   \n        }#if($foreach.hasNext),#end\n        #end\n    ]		\n}' WHERE `template_name` = '湘无界ERP收款单同步';

-- 好帮你ERP收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n#set($firstItem = $detailList.get(0))  \n#set($payOpenList = $firstItem.payOpenList)  \n#set($firstPay = $payOpenList.get(0))    \n\"customerCode\": \"${branchNo}\",\n\"businessDate\": \"${firstItem.sheetDateString}\",\n  #if($firstItem.sheetType == \'XR\')\n    \"customerBillNo\": \"${firstItem.supplierOrderNo}\", \n    \"remark\": \"B2B入驻商订单号：${firstItem.supplierOrderNo},ERP订单号：${firstItem.sourceOrderNo}\", \n    #else\n    \"customerBillNo\": \"${firstItem.supplierAfterNo}\",  \n    \"remark\": \"B2B入驻商售后单号：${firstItem.supplierAfterNo},ERP退单号：${firstItem.sourceAfterNo}\",       \n    #end    \n #if(\"$!{$firstPay}\" && \"$!{$firstPay.platform}\")\n     \"bankCode\": \"${firstPay.platform}\",\n  #end      \n\"collectionAmount\": ${totalReceiptAmt}\n}' WHERE `template_name` = '好帮你ERP收款单同步';

-- ERP11.0收款单同步
UPDATE `zksr-cloud`.`visual_setting_template` SET `api_template` = '{\n#set($firstItem = $detailList.get(0))  \n  \n## 1.在线支付订单支付 onlineYhPay \n#if($firstItem.sheetType == \"XR\"  && $firstItem.payWay == 0)   \n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"onlineYhPay\",   \n  \n## 2.货到付款收款 deliveryYhPay   \n#elseif($firstItem.sheetType == \"XR\"  && $firstItem.payWay == 2)   \n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"deliveryYhPay\",   \n  \n## 3.差异出库退款（线上支付、货到付款）  differenceDoPay\n#elseif($firstItem.sheetType == \"SC\")\n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"differenceDoPay\",      \n    \n## 4.拒收退货退款（线上支付、货到付款） rejectDrPay   \n#elseif($firstItem.sheetType == \"SJ\")\n\"sheetNo\": \"${firstItem.sourceOrderNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierOrderNo}\",\n\"type\": \"rejectDrPay\",   \n    \n## 5.售后退货退款（线上支付、货到付款）  afterSales \n#elseif($firstItem.sheetType == \"SR\")\n\"sheetNo\": \"${firstItem.sourceAfterNo}\",\n\"sourceSheetNo\": \"${firstItem.supplierAfterNo}\",\n\"type\": \"afterSales\",   \n#else  \n\"sheetNo\": \"\",\n\"sourceSheetNo\": \"\",\n\"type\": \"\",    \n#end\n\"amt\": ${totalReceiptExactAmt}\n}' WHERE `template_name` = 'ERP11.0收款单同步';


DROP TABLE IF EXISTS `zksr_product`.`prdt_material`;
CREATE TABLE `zksr_product`.`prdt_material`(
                                               `material_id` BIGINT(20) NOT NULL  COMMENT '素材id' ,
                                               `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                               `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                               `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                               `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                               `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                               `name` VARCHAR(32) NOT NULL  COMMENT '素材名称' ,
                                               `img` VARCHAR(255) NOT NULL  COMMENT '素材图片地址' ,
                                               `img_size` VARCHAR(8) NOT NULL  COMMENT '素材图片大小' ,
                                               `status` TINYINT(1)   COMMENT '状态 1-启用 0-停用' ,
                                               PRIMARY KEY (material_id)
)  COMMENT = '素材表';

DROP TABLE IF EXISTS `zksr_product`.`prdt_material_apply`;
CREATE TABLE `zksr_product`.`prdt_material_apply`(
                                                     `material_apply_id` BIGINT(20) NOT NULL  COMMENT '素材应用id' ,
                                                     `sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
                                                     `create_by` VARCHAR(64)   COMMENT '创建人' ,
                                                     `create_time` DATETIME(3)   COMMENT '创建时间' ,
                                                     `update_by` VARCHAR(64)   COMMENT '更新人' ,
                                                     `update_time` DATETIME(3)   COMMENT '更新时间' ,
                                                     `material_id` BIGINT(20) NOT NULL  COMMENT '素材id' ,
                                                     `apply_type` TINYINT(1) NOT NULL  COMMENT '素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品' ,
                                                     `apply_id` BIGINT(20)   COMMENT '素材应用类型id' ,
                                                     `start_time` DATETIME   COMMENT '生效时间' ,
                                                     `end_time` DATETIME   COMMENT '失效时间' ,
                                                     `apply_user_id` BIGINT(20)   COMMENT '操作人' ,
                                                     PRIMARY KEY (material_apply_id)
)  COMMENT = '素材应用表';

-- 素材功能相关菜单信息
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商品素材管理', 2731, 88, 'wLRhLHHlMOCzfYpyRP', 'ZMbSRJAhKvXZFWpIXo', 'materialManagement', 'platform/materialManagement/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-01-08 15:50:35', 'zksr', '2025-01-10 09:57:30', '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材管理', 0, 10, 'ZMbSRJAhKvXZFWpIXo', '0', '/material', NULL, NULL, 1, 0, 'M', '0', '0', '', 'tool', 'zksr', '2025-01-10 09:56:45', 'zksr', '2025-01-10 10:01:04', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材查询', 2730, 0, 'VuNgBsyBqtZADQYdjv', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:query', '#', 'zksr', '2025-01-10 09:59:51', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材新增', 2730, 0, 'FfIGFtQJzLyYvwVDdA', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:add', '#', 'zksr', '2025-01-10 10:00:19', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材修改', 2730, 0, 'dGaUnzoSTZASeGdgCN', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:edit', '#', 'zksr', '2025-01-10 10:00:41', 'zksr', '2025-01-10 10:01:01', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材删除', 2730, 0, 'zpntxFSenwSTKtdSUd', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:remove', '#', 'zksr', '2025-01-10 10:01:33', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材导出', 2730, 0, 'jFSDxhgGeErlBaZAJF', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:export', '#', 'zksr', '2025-01-10 10:01:53', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材打标管理', 2731, 1, 'EjarFEReoGVNppgfqv', 'ZMbSRJAhKvXZFWpIXo', 'materialMarking', 'platform/materialMarking/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-01-10 10:03:04', 'zksr', '2025-01-11 09:05:09', '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用新增', 2737, 0, 'qQXYbvAjKccDshesvZ', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:add', '#', 'zksr', '2025-01-10 10:03:34', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用修改', 2737, 0, 'DjzkvVgRJeRqhazebX', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:edit', '#', 'zksr', '2025-01-10 10:03:58', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用删除', 2737, 0, 'qktGRphwoUjjcKiOCT', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:remove', '#', 'zksr', '2025-01-10 10:04:17', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用导出', 2737, 0, 'uPlIytpKBUvanilvle', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:export', '#', 'zksr', '2025-01-10 10:04:37', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材列表', 2730, 0, 'xIBWotmyTrAmApAAja', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:list', '#', 'zksr', '2025-01-10 10:13:57', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用列表', 2737, 0, 'HGonFLQDnazfVovcnQ', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:list', '#', 'zksr', '2025-01-10 10:14:32', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材停用', 2730, 0, 'BWEKiOGckEJXVGPVTM', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:disable', '#', 'zksr', '2025-01-10 14:47:05', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材启用', 2730, 0, 'EMkuqyReusicNQEvwV', 'wLRhLHHlMOCzfYpyRP', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:material:enable', '#', 'zksr', '2025-01-10 14:47:23', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('素材应用查询', 2737, 0, 'llABIAOLqIavuIfRJb', 'EjarFEReoGVNppgfqv', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:materialApply:query', '#', 'zksr', '2025-01-13 18:03:11', '', NULL, '', 'dc,partner');


-- 入驻商促销调整
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用停用', 2396, 6, 'dQtYzLAUenHkJMZOZw', '2396', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:changeState', '#', 'zksr', '2025-01-14 16:54:40', 'zksr', '2025-01-14 16:59:31', '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('商品列表', 2372, 3, 'iFADUvHcsYUXHGDgit', '2372', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:item:list', '#', 'zksr', '2025-01-13 17:55:10', '', NULL, '', 'dc,supplier');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用停用', 2399, 6, 'NWRpqQhkIiLJMCVoki', '2399', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sk-rule:changeState', '#', 'zksr', '2025-01-14 16:53:44', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用停用', 2401, 6, 'oWQqtKzBCASpbofYzr', '2401', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:changeState', '#', 'zksr', '2025-01-14 16:54:13', '', NULL, '', 'dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('启用停用', 2400, 6, 'qOXcmOUNhBKarAAHiX', '2400', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:activity:sp-rule:changeState', '#', 'zksr', '2025-01-14 16:55:05', 'zksr', '2025-01-14 16:59:40', '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('列表', 2451, 1, 'ZHfEhpsuXkHSFPGrnQ', '2451', '', NULL, NULL, 1, 0, 'F', '0', '0', 'promotion:template-extend:list', '#', 'zksr', '2025-01-14 16:24:29', 'zksr', '2025-01-14 16:28:51', '', 'dc,supplier');


UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'ulXCDHHadsocIcmvmF' WHERE `menu_code` = 'ulXCDHHadsocIcmvmF';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'DXpObNjmIyZdFCkLHQ' WHERE `menu_code` = 'DXpObNjmIyZdFCkLHQ';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'OEqvmfoiGiWytOyNly' WHERE `menu_code` = 'OEqvmfoiGiWytOyNly';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'MtNwhhOLUhYdqoGGaD' WHERE `menu_code` = 'MtNwhhOLUhYdqoGGaD';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'AtcsnWWYvBEyAyvowg' WHERE `menu_code` = 'AtcsnWWYvBEyAyvowg';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'vOhYRdjncQpQpmipyM' WHERE `menu_code` = 'vOhYRdjncQpQpmipyM';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'qgpijUvAmWiWljwYkf' WHERE `menu_code` = 'qgpijUvAmWiWljwYkf';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'lrShaNZIGeAjiApOXK' WHERE `menu_code` = 'lrShaNZIGeAjiApOXK';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'EOQjKhJIlejIQVesLo' WHERE `menu_code` = 'EOQjKhJIlejIQVesLo';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'YPfckXhJfKjxGSZrie' WHERE `menu_code` = 'YPfckXhJfKjxGSZrie';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'ZkcsCinCYcDvsfYGXN' WHERE `menu_code` = 'ZkcsCinCYcDvsfYGXN';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'sEeQlXBpAWDANuMdPq' WHERE `menu_code` = 'sEeQlXBpAWDANuMdPq';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'hNxeEvrbBAMBsUllEr' WHERE `menu_code` = 'hNxeEvrbBAMBsUllEr';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'wsFwBucvaBkgmUXvzX' WHERE `menu_code` = 'wsFwBucvaBkgmUXvzX';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'HOQnodbijJecxrgfdd' WHERE `menu_code` = 'HOQnodbijJecxrgfdd';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'yyDtrdmVSCyNCEKnjs' WHERE `menu_code` = 'yyDtrdmVSCyNCEKnjs';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'HwEbjlYwlpbEWBBVgY' WHERE `menu_code` = 'HwEbjlYwlpbEWBBVgY';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'JZrNLnHDGDaEvhVFZT' WHERE `menu_code` = 'JZrNLnHDGDaEvhVFZT';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'OHpSzVRGsLPqtWVcxR' WHERE `menu_code` = 'OHpSzVRGsLPqtWVcxR';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'ZMBGEFkwTPMdTZSoWe' WHERE `menu_code` = 'ZMBGEFkwTPMdTZSoWe';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'mojyCickfiGCbXVlsQ' WHERE `menu_code` = 'mojyCickfiGCbXVlsQ';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'NTUMYivtoENIOOcMTR' WHERE `menu_code` = 'NTUMYivtoENIOOcMTR';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'NYkKgOhadoknSgZSoq' WHERE `menu_code` = 'NYkKgOhadoknSgZSoq';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'GUkKIchHbWZJstNUuu' WHERE `menu_code` = 'GUkKIchHbWZJstNUuu';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'tcsOVikaNzwytvLaYI' WHERE `menu_code` = 'tcsOVikaNzwytvLaYI';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'iXLjrPsdlKWdndVYKh' WHERE `menu_code` = 'iXLjrPsdlKWdndVYKh';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'hRkxBUOSBSVJnFxAYl' WHERE `menu_code` = 'hRkxBUOSBSVJnFxAYl';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'QsMIeCPjrCgxAhbEVU' WHERE `menu_code` = 'QsMIeCPjrCgxAhbEVU';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'cnGRVLgSHRlDavQddn' WHERE `menu_code` = 'cnGRVLgSHRlDavQddn';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'qlXgWqDcPpfQFgfEmB' WHERE `menu_code` = 'qlXgWqDcPpfQFgfEmB';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'PvXbtpYlKFmgYQFGSM' WHERE `menu_code` = 'PvXbtpYlKFmgYQFGSM';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'yAMwCUOKyCgDOYTEwZ' WHERE `menu_code` = 'yAMwCUOKyCgDOYTEwZ';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'XZQjjwkxfknvgySCcX' WHERE `menu_code` = 'XZQjjwkxfknvgySCcX';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'XEsODZEdbzTHfQAoNA' WHERE `menu_code` = 'XEsODZEdbzTHfQAoNA';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = 'EMPNZlYTHlNHrNfiVt' WHERE `menu_code` = 'EMPNZlYTHlNHrNfiVt';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = 'dNixHYtyqYWPojGvdk' WHERE `menu_code` = 'dNixHYtyqYWPojGvdk';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = 'hLAGtwocGeBkRxeFia' WHERE `menu_code` = 'hLAGtwocGeBkRxeFia';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = 'KcEmQoFuDntcACrlEW' WHERE `menu_code` = 'KcEmQoFuDntcACrlEW';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = 'iRkpVJxgPnSDBOnftp' WHERE `menu_code` = 'iRkpVJxgPnSDBOnftp';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = 'XMXoTrNcVajRaraFFb' WHERE `menu_code` = 'XMXoTrNcVajRaraFFb';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'lbGhiWQNFmJQdEgxha' WHERE `menu_code` = 'lbGhiWQNFmJQdEgxha';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'EhmMGADBATqVJzGkZc' WHERE `menu_code` = 'EhmMGADBATqVJzGkZc';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'fCRtpROuicLNKPtmfq' WHERE `menu_code` = 'fCRtpROuicLNKPtmfq';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'fNmncTXuFoRSsnUWzC' WHERE `menu_code` = 'fNmncTXuFoRSsnUWzC';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'TbyigsUrdFQiDZmFWF' WHERE `menu_code` = 'TbyigsUrdFQiDZmFWF';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'eOsdApKyICRwcEvvSC' WHERE `menu_code` = 'eOsdApKyICRwcEvvSC';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'jyQJMvdnGHtzmCmYBI' WHERE `menu_code` = 'jyQJMvdnGHtzmCmYBI';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,partner', `menu_code` = 'GBRtouCgJcuWuNBHxd' WHERE `menu_code` = 'GBRtouCgJcuWuNBHxd';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = 'BXULUOBHzMcpRiROUI' WHERE `menu_code` = 'BXULUOBHzMcpRiROUI';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'iXLvBgBDqOJrkkPjUw' WHERE `menu_code` = 'iXLvBgBDqOJrkkPjUw';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = 'ySmqSoCkCaMlFBOJAK' WHERE `menu_code` = 'ySmqSoCkCaMlFBOJAK';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = 'ejPNSJpUpBxDNGznxw' WHERE `menu_code` = 'ejPNSJpUpBxDNGznxw';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2643' WHERE `menu_code` = '2643';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2452' WHERE `menu_code` = '2452';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2451' WHERE `menu_code` = '2451';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2450' WHERE `menu_code` = '2450';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2449' WHERE `menu_code` = '2449';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2448' WHERE `menu_code` = '2448';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2447' WHERE `menu_code` = '2447';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2446' WHERE `menu_code` = '2446';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2445' WHERE `menu_code` = '2445';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2442' WHERE `menu_code` = '2442';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = '2441' WHERE `menu_code` = '2441';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = '2440' WHERE `menu_code` = '2440';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = '2444' WHERE `menu_code` = '2444';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2443' WHERE `menu_code` = '2443';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2439' WHERE `menu_code` = '2439';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2432' WHERE `menu_code` = '2432';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2431' WHERE `menu_code` = '2431';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2430' WHERE `menu_code` = '2430';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2429' WHERE `menu_code` = '2429';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2428' WHERE `menu_code` = '2428';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2427' WHERE `menu_code` = '2427';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2426' WHERE `menu_code` = '2426';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2436' WHERE `menu_code` = '2436';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2425' WHERE `menu_code` = '2425';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2437' WHERE `menu_code` = '2437';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2424' WHERE `menu_code` = '2424';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2438' WHERE `menu_code` = '2438';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2435' WHERE `menu_code` = '2435';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2423' WHERE `menu_code` = '2423';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2434' WHERE `menu_code` = '2434';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2422' WHERE `menu_code` = '2422';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2433' WHERE `menu_code` = '2433';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2421' WHERE `menu_code` = '2421';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2420' WHERE `menu_code` = '2420';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2419' WHERE `menu_code` = '2419';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2418' WHERE `menu_code` = '2418';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2417' WHERE `menu_code` = '2417';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2416' WHERE `menu_code` = '2416';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2415' WHERE `menu_code` = '2415';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2414' WHERE `menu_code` = '2414';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2413' WHERE `menu_code` = '2413';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2412' WHERE `menu_code` = '2412';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2411' WHERE `menu_code` = '2411';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2401' WHERE `menu_code` = '2401';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2400' WHERE `menu_code` = '2400';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2399' WHERE `menu_code` = '2399';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2398' WHERE `menu_code` = '2398';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2397' WHERE `menu_code` = '2397';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2396' WHERE `menu_code` = '2396';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2380' WHERE `menu_code` = '2380';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2379' WHERE `menu_code` = '2379';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2378' WHERE `menu_code` = '2378';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2377' WHERE `menu_code` = '2377';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2376' WHERE `menu_code` = '2376';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2375' WHERE `menu_code` = '2375';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2374' WHERE `menu_code` = '2374';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2373' WHERE `menu_code` = '2373';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2372' WHERE `menu_code` = '2372';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2371' WHERE `menu_code` = '2371';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2370' WHERE `menu_code` = '2370';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc', `menu_code` = '2369' WHERE `menu_code` = '2369';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'dc,supplier', `menu_code` = '2368' WHERE `menu_code` = '2368';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2367' WHERE `menu_code` = '2367';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2360' WHERE `menu_code` = '2360';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2359' WHERE `menu_code` = '2359';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2348' WHERE `menu_code` = '2348';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2347' WHERE `menu_code` = '2347';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = '2346' WHERE `menu_code` = '2346';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner', `menu_code` = '2340' WHERE `menu_code` = '2340';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2339' WHERE `menu_code` = '2339';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2338' WHERE `menu_code` = '2338';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2337' WHERE `menu_code` = '2337';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2335' WHERE `menu_code` = '2335';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2259' WHERE `menu_code` = '2259';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2256' WHERE `menu_code` = '2256';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2255' WHERE `menu_code` = '2255';
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,supplier', `menu_code` = '2027' WHERE `menu_code` = '2027';


-- 平台商字典新增表
CREATE TABLE `zksr_cloud`.`sys_partner_dict_type` (
                                                      `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
                                                      `sys_code` bigint NOT NULL COMMENT '平台商编号',
                                                      `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
                                                      `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
                                                      `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                                      `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                      `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                                      PRIMARY KEY (`dict_id`) USING BTREE,
                                                      KEY `dict_type` (`dict_type`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='平台商字典类型表';


CREATE TABLE `zksr_cloud`.`sys_partner_dict_data` (
                                         `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
                                         `sys_code` bigint(20) NOT NULL COMMENT '平台商编号',
                                         `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
                                         `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
                                         `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
                                         `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
                                         `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
                                         `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
                                         `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
                                         `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                         `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                         PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4  COMMENT='平台商字典数据表';

-- 平台商字典菜单
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '字典删除', 2729, 4, 'VNooudsoWFuomJhcPS', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:remove', '#', 'zksr', '2025-01-14 17:04:07', 'zksr', '2025-01-14 17:04:33', '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '字典修改', 2729, 3, 'CxwRJcFHAifuEcarWT', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:edit', '#', 'zksr', '2025-01-14 17:03:30', '', NULL, '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '字典新增', 2729, 2, 'KTKXzitWeLTQlrNxFT', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:add', '#', 'zksr', '2025-01-14 17:03:05', 'zksr', '2025-01-14 17:03:37', '', 'partner');
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '查看详情', 2729, 1, 'kPqxKifdwXMvuZtkKP', 'jCtwbnehGKJAHKJAtm', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:partnerDict:query', '#', 'zksr', '2025-01-14 17:02:38', '', NULL, '', 'partner');

INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '平台商数据字典', 1, 11, 'jCtwbnehGKJAHKJAtm', '1', 'aftersaleDictionary', 'system/aftersaleDictionary/index', NULL, 1, 0, 'C', '0', '0', 'system:partnerDict:list', '#', 'zksr', '2025-01-03 14:28:36', 'zksr', '2025-01-14 17:06:42', '', 'partner,software');


ALTER TABLE `zksr_cloud`.`sys_partner_policy`
    ADD COLUMN `product_distribution_label` varchar(100) COMMENT '商品配送标签' AFTER `supplier_id`;

