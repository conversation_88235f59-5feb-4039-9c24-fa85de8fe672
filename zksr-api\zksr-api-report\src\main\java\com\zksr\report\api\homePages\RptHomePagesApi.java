package com.zksr.report.api.homePages;

import com.zksr.report.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @time 2024/12/19
 * @desc
 */
@FeignClient(
        contextId = "remoteRptHomePagesApi",
        value = ApiConstants.NAME
)
public interface RptHomePagesApi {
    String PREFIX = ApiConstants.PREFIX + "/rptHomePages";

    /**
     * 定时刷新ES首页报表数据 - day
     * @param sysCode
     */
    @PutMapping(value = PREFIX + "/refreshEsHomePagesDataJob")
    public void refreshEsHomePagesDataJob(@RequestParam("sysCode") Long sysCode);

    /**
     * 定时刷新ES首页报表数据- month
     * @param sysCode
     */
    @PutMapping(value = PREFIX + "/refreshEsHomePagesMonthDataJob")
    public void refreshEsHomePagesMonthDataJob(@RequestParam("sysCode") Long sysCode, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);
}
