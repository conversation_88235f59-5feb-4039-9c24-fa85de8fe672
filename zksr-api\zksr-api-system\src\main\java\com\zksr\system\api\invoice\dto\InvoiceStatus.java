package com.zksr.system.api.invoice.dto;


/**
 * 开票状态枚举
 */
public enum InvoiceStatus {
    SUCCESS(3, "开票成功"),
    FAILED(5, "开票失败"),
    VOIDED(8, "已作废"),
    FILE_CALLBACK(10, "二次附件回调");

    private final Integer code;
    private final String desc;

    InvoiceStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

