package com.zksr.promotion.api.activity.vo;

import com.zksr.promotion.api.activity.dto.ActivityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动标签简单展示
 * @date 2024/5/18 10:20
 */
@Data
@Accessors(chain = true)
@ApiModel("促销标签描述")
public class ActivityLabelInfoVO<Role> {

    @ApiModelProperty("促销活动")
    private ActivityDTO activity;

    @ApiModelProperty("目标值")
    private BigDecimal targetValue = BigDecimal.ZERO;

    @ApiModelProperty("已完成")
    private BigDecimal finishValue;

    @ApiModelProperty("活动触发赠送的单位")
    private Integer ruleUnitType = 1;

    @ApiModelProperty("当前促销活动已优惠金额")
    private BigDecimal totalCouponAmt = BigDecimal.ZERO;

    @ApiModelProperty("下一个阶梯促销目标类型  0 满足金额  1满足购买品项数（默认为满足金额）暂时只有满赠时使用")
    private Integer betterTargetType = 0;

    @ApiModelProperty("已满足促销规则")
    private List<Role> adequateRole = new ArrayList<>();

    @ApiModelProperty("下一个阶梯促销规则")
    private Role betterRole;
}
