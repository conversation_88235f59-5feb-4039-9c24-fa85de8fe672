package com.zksr.account.api.transfer.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;


/**
 * 交易对账单明细导出返回对象 acc_transfer_bill_order
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@ApiModel("交易对账单明细导出返回对象 - acc_transfer_bill_order Response DTO")
public class AccTransferBillOrderRespDTO extends BaseEntity {

    /** 交易时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "下单时间")
    @Excel(name = "下单时间", cellType = Excel.ColumnType.STRING, dateFormat = YYYY_MM_DD)
    private Date transferTime;

    /** 商户号 */
    @ApiModelProperty(value = "商户号")
    @Excel(name = "商户号", cellType = Excel.ColumnType.STRING)
    private String altNo;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    @Excel(name = "入驻商名称", cellType = Excel.ColumnType.STRING)
    private String supplierName;


    @ApiModelProperty(value = "平台交易单号")
    @Excel(name = "平台交易单号", cellType = Excel.ColumnType.STRING)
    private String platformTradeNo;

    /** 商户交易单号*/
    @ApiModelProperty(value = "商户交易单号")
    @Excel(name = "商户交易单号", cellType = Excel.ColumnType.STRING)
    private String merchantTradeNo;

    /** 平台支付金额 */
    @ApiModelProperty(value = "平台支付金额")
    @Excel(name = "平台支付金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal platformPayAmt;


    @ApiModelProperty(value = "商户支付金额")
    @Excel(name = "商户支付金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal merchantPayAmt;

    /** 平台退款金额 */
    @ApiModelProperty(value = "平台退款金额")
    @Excel(name = "平台退款金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal platformRefundAmt;

    /** 商户退款金额 */
    @ApiModelProperty(value = "商户退款金额")
    @Excel(name = "商户退款金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal merchantRefundAmt;

    /** 平台支付手续费 */
    @ApiModelProperty(value = "平台支付手续费")
    @Excel(name = "平台支付手续费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal platformPayFree;

    /** 商户支付手续费 */
    @ApiModelProperty(value = "商户支付手续费")
    @Excel(name = "商户支付手续费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal merchantPayFree;


    @ApiModelProperty(value = "平台退款手续费")
    @Excel(name = "平台退款手续费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal platformRefundFree;


    @ApiModelProperty(value = "商户退款手续费")
    @Excel(name = "商户退款手续费", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal merchantRefundFree;


//    @ApiModelProperty(value = "业务单号(订单号, 退款单号...)")
//    @Excel(name = "业务单号", cellType = Excel.ColumnType.STRING)
//    private String busiTradeNo;


    @ApiModelProperty(value = "订单类型 0-销售单 1-退款单")
    @Excel(name = "订单类型", cellType = Excel.ColumnType.STRING, readConverterExp = "0=销售单,1=退款单")
    private Integer orderType;


    @ApiModelProperty(value = "状态 0-正常 1-异常")
    @Excel(name = "状态", cellType = Excel.ColumnType.STRING, readConverterExp = "0=正常,1=异常")
    private Integer state;

    @ApiModelProperty(value = "备注信息")
    @Excel(name = "备注信息", cellType = Excel.ColumnType.STRING)
    private String remark;

    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

}
