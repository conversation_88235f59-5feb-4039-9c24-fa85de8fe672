alter table `zksr_product`.`prdt_adjust_prices`
   add column `task_execute_status` tinyint(1) DEFAULT NULL COMMENT '定时调价执行状态 0-未执行，1-已执行';

-- 新增商品定时调价定时任务
INSERT INTO `xxl_job`.`xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (22, 2, '每天两点执行-商品调价单定时调价', '2024-11-08 11:01:44', '2024-11-08 11:02:26', '蒋剑超', '', 'CRON', '0 00 2 * * ?', 'DO_NOTHING', 'FIRST', 'adjustPricesTaskExecuteStatusJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-08 11:01:44', '', 0, 0, 0);

-- 入驻商售后订单详情增加  字段
ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    ADD COLUMN `return_original_price` DECIMAL(18,6) DEFAULT 0 COMMENT '售后最小单位原销售单价（不包括优惠）' AFTER `return_sales_unit_price`,
    ADD COLUMN `return_original_unit_price` DECIMAL(18,6) DEFAULT 0 COMMENT '售后单位原销售单价（不包括优惠）' AFTER `return_original_price`;

-- 入驻商售后订单增加  字段
ALTER TABLE `zksr_trade`.`trd_supplier_after`
    ADD COLUMN `return_sub_order_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '入驻商订单本次售后退款金额（不包括优惠）' AFTER `sub_refund_amt`;

-- 售后订单增加  字段
ALTER TABLE `zksr_trade`.`trd_after`
    ADD COLUMN `return_order_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '订单本次售后退款金额（不包括优惠）' AFTER `refund_amt`,
    ADD COLUMN `return_discount_amt` decimal(12,2) DEFAULT 0 COMMENT '本次售后订单优惠金额' AFTER `return_order_amt`;

CREATE TABLE `zksr_product`.`prdt_product_share` (
`share_product_id` bigint(20) NOT NULL,
`sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
`create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`item_id` bigint(20) DEFAULT NULL COMMENT '商品上架ID',
`unit_size` bigint(20) DEFAULT NULL COMMENT '商品单位',
`remote_ip` varchar(32) DEFAULT NULL COMMENT '发起分享IP',
`expiration_time` datetime DEFAULT NULL COMMENT '有效时间',
`share_key` varchar(64) DEFAULT NULL COMMENT '分享key',
PRIMARY KEY (`share_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分享';
