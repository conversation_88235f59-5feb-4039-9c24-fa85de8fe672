package com.zksr.file.api.file.vo;

import com.zksr.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入记录
 */
@Data
public class SysFileImportVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 导入记录id
     */
    private Long fileImportId;

    /**
     * 平台编号
     */
    private Long sysCode;

    /**
     * 导入文件名
     */
    private String fileName;

    /**
     * 导入文件下载地址
     */
    private String fileUrl;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 导入总数
     */
    private Integer totalNum;

    /**
     * mq发送数量
     */
    private Integer mqSendNum;

    /**
     * mq接收数量
     */
    private Integer mqReceiveNum;

    /**
     * 成功条数
     */
    private Integer successNum;

    /**
     * 失败条数
     */
    private Integer failNum;

    /**
     * 是否更新已存在的数据;是否更新已存在的数据
     */
    private Integer updateSupport;

    /**
     * 导入类型
     */
    private String importType;

    private Long dcId;

    private String funcScop;

    private Long supplierId;
}
