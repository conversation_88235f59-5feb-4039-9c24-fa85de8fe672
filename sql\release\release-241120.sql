CREATE DATABASE zksr_report DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci;

-- 报表库下执行
DROP TABLE IF EXISTS `zksr_report`.`rpt_tag_def`;
CREATE TABLE `zksr_report`.`rpt_tag_def` (
`tag_def_id` bigint(20) NOT NULL COMMENT '标签定义id',
`sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
`create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`tag_name` varchar(32) DEFAULT NULL COMMENT '标签名',
`tag_rule_json` text COMMENT '标签规则json',
`enabled` int(1) DEFAULT NULL COMMENT '是否启用 1-是 0-否',
`tag_type` varchar(32) DEFAULT NULL COMMENT '标签类型（数据字典）',
`tag_category` varchar(32) DEFAULT NULL COMMENT '标签分类,(客户活跃, 客户等级, 类占比)',
PRIMARY KEY (`tag_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签定义表';

CREATE TABLE `zksr_report`.`WORKER_NODE` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto increment id',
  `HOST_NAME` varchar(64) NOT NULL COMMENT 'host name',
  `PORT` varchar(64) NOT NULL COMMENT 'port',
  `TYPE` int(11) NOT NULL COMMENT 'node type: ACTUAL or CONTAINER',
  `LAUNCH_DATE` date NOT NULL COMMENT 'launch date',
  `MODIFIED` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'modified time',
  `CREATED` timestamp NOT NULL DEFAULT '1976-01-01 00:00:00' COMMENT 'created time',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='DB WorkerID Assigner for UID Generator';


-- 修改是否同步标准价格字段名称和注释
ALTER TABLE `zksr_cloud`.`sys_opensource`
    CHANGE COLUMN `sync_price` `sync_mark_price` tinyint(1) DEFAULT '1' COMMENT '是否同步标准价格 0-不同步 1-同步（默认同步）';

-- 增加是否同步供货价新字段
ALTER TABLE `zksr_cloud`.`sys_opensource`
    ADD COLUMN `sync_cost_price` tinyint(1) DEFAULT '1' COMMENT '是否同步供货价格 0-不同步 1-同步（默认同步）';
-- 订单结算表 结算状态 备注说明调整
ALTER TABLE `zksr_trade`.`trd_settle`
    MODIFY COLUMN  `state` tinyint(1) NOT NULL COMMENT '结算状态 0=未结算，1=已结算 , 2=结算中，3= 待执行结算';

-- 促销特价允许字段为null调整, 如果未null则是不参与活动, 或者不限制
ALTER TABLE `zksr_promotion`.`prm_sp_rule`
    MODIFY COLUMN `sp_price` decimal(10, 2) NULL COMMENT '促销价' AFTER `sku_id`,
    MODIFY COLUMN `once_buy_limit` int(11) NULL COMMENT '单次限购数量' AFTER `sp_price`,
    MODIFY COLUMN `total_limit_qty` int(11) NULL COMMENT '总限量' AFTER `once_buy_limit`;

ALTER TABLE `zksr_promotion`.`prm_sk_rule`
MODIFY COLUMN `seckill_stock` int(11) NULL COMMENT '秒杀库存' AFTER `once_limit`;


