package com.zksr.account.model.pay.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 异步支付通知
 * @date 2024/4/9 18:38
 */
@Data
public class PayOrderNotifyDTO {
    @ApiModelProperty("订单支付回调")
    private PayOrderRespDTO orderResp;
    @ApiModelProperty("订单类型")
    private Integer orderType;

    public PayOrderNotifyDTO() {

    }

    public PayOrderNotifyDTO(PayOrderRespDTO orderResp, Integer orderType) {
        this.orderResp = orderResp;
        this.orderType = orderType;
    }
}
