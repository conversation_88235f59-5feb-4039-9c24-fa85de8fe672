package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户活跃定义
 * @date 2024/11/12 8:43
 */
@Data
@ApiModel(description = "客户活跃定义")
public class BranchActiveConfigDTO {

    @ApiModelProperty("A-计划按照自然月, B-计划按照指定分类自然月")
    private String plan;

    @ApiModelProperty("管理分类列表")
    private List<Long> catgoryList;

    @ApiModelProperty("满足目标金额")
    private BigDecimal targetAmt;
}
