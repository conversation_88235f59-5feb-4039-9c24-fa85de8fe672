package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 平台商提款配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WithdrawalSettingPolicyDTO {
    /**
     * 提现费率
     */
    @ApiModelProperty("提现费率")
    private String withdrawalFeeRate;

    /**
     * 最小提现金额
     */
    @ApiModelProperty("最小提现金额")
    private String minimumWithdrawalAmount;

    /**
     * 门店提现费率
     */
    @ApiModelProperty("门店提现费率,最大1=100%")
    private String branchWithdrawRate;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;
}
