package com.zksr.account.api.account;

import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.vo.ApiAccAccountFlowPageVO;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.domain.vo.openapi.BranchValueInfoOpenDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/23
 * @desc
 */
@FeignClient(
        contextId = "remoteAccountFlowApi",
        value = ApiConstants.NAME
)
public interface AccountFlowApi {

    String PREFIX = ApiConstants.PREFIX + "/accountFlow";

    /**
     * 获取结算重试流水记录
     * @param minId 查询最小起始ID
     * @return 返回待结算流水对应的下标ID, 每批次500条
     */
    @PostMapping(PREFIX + "/getTrySettleFlow")
    CommonResult<List<AccAccountFlowDTO>> getTrySettleFlow(@RequestParam("minId") Long minId);

    /**
     * 获取账户流水记录分页
     * @param pageVO    分页请求
     * @return  分页记录
     */
    @PostMapping(PREFIX + "/getAccountFlowPge")
    CommonResult<PageResult<AccAccountFlowDTO>> getAccountFlowPge(@RequestBody ApiAccAccountFlowPageVO pageVO);

    /**
     * 获取门店账户流水记录分页, 门店账户流水有些特殊, 需要合并赠金和本金变动记录
     * @param pageVO    分页请求
     * @return  分页记录
     */
    @PostMapping(PREFIX + "/getBranchAccountFlowPge")
    CommonResult<PageResult<RechargeConsumeRespVO>> getBranchAccountFlowPge(@RequestBody ApiAccAccountFlowPageVO pageVO);

    /**
     * 获取账户流水数据 根据ID集合
     * @param accountFlowIds ID集合
     * @return 流水集合
     */
    @GetMapping(PREFIX + "/getAccAccountFlowListByIds")
    CommonResult<List<AccAccountFlowDTO>> getAccAccountFlowListByIds(@RequestParam("accountFlowIds") List<Long> accountFlowIds);

    /**
     * 获取对接第三方需要的门店储值充值/提现信息 根据ID集合
     * @param accountFlowIds ID集合
     * @return 流水集合
     */
    @GetMapping(PREFIX + "/getBranchValueInfoByIds")
    CommonResult<BranchValueInfoOpenDTO> getBranchValueInfoByIds(@RequestParam("accountFlowIds") List<Long> accountFlowIds);
}
