package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 入驻商售后配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AfterSaleSettingPolicyDTO {

    /**
     * 入驻商地址
     */
    @ApiModelProperty("入驻商售后地址")
    private String afterSaleAddress;

    /**
     * 入驻商电话
     */
    @ApiModelProperty("入驻商售后电话")
    private String afterSalePhone;


    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    @ApiModelProperty("入驻商ID")
    private String supplierId;
}
