package com.zksr.member.api.colonel;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.dto.MemColonelSaveReqVO;
import com.zksr.member.api.colonel.form.MemColonelImportForm;
import com.zksr.member.api.colonel.dto.MemColonelVisitLogRespDTO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.member.api.colonel.vo.MemShopAppRecodeReqVO;
import com.zksr.member.api.colonel.vo.MemColonelVisitLogVO;
import com.zksr.member.api.colonel.vo.WxColonelPublishOpenidBindReqVO;
import com.zksr.member.enums.ApiConstants;
import com.zksr.system.api.fileImport.vo.FileImportHandlerVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月28日 09:30
 * @description: ColonelApi
 */
@FeignClient(
        contextId = "remoteColonelApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface ColonelApi {

    String PREFIX = ApiConstants.PREFIX + "/colonel";


    /**
     * 根据ID查询数据
     * @param colonelId
     * @return
     */
    @GetMapping(PREFIX + "/getByColonelId")
    public CommonResult<ColonelDTO> getByColonelId(@RequestParam("colonelId") Long colonelId);

    /**
     * 新增业务员信息
     */
    @PostMapping(PREFIX + "/addColonel")
    public CommonResult<Long> add(@RequestBody MemColonelSaveReqVO createReqVO);

    /**
     * 修改业务员信息
     */

    @PutMapping(PREFIX + "/updateColonel")
    public CommonResult<Boolean> edit( @RequestBody MemColonelSaveReqVO updateReqVO);

    /**
     * 获取分页数据
     * @param pageReqVO 分页对象
     * @return
     */
    @PostMapping(PREFIX + "/getPage")
    public CommonResult<PageResult<MemColonelRespVO>> getPage(@RequestBody MemColonelPageReqVO pageReqVO);

    /**
     * 获取某个业务员的直接下属业务员列表
     * @param colonelId 业务员ID
     * @return 直接下属业务员列表
     */
    @GetMapping(PREFIX + "/getSubordinates")
    CommonResult<List<ColonelDTO>> getSubordinates(@RequestParam("colonelId") Long colonelId);

    @GetMapping(PREFIX + "/getColonelMonthlySalesTarget")
    CommonResult<BigDecimal> getColonelMonthlySalesTarget(@RequestParam("colonelId") Long colonelId);

    /**
     * 获取某个业务员的所发展的业务员信息
     * @param colonelId 业务员ID
     * @return 业务员发展下属信息
     */
    @GetMapping(PREFIX + "/selectDevelopmentSalesmanByColonelId")
    CommonResult<List<ColonelDTO>> selectDevelopmentSalesmanByColonelId(@RequestParam("colonelId") Long colonelId);

    /**
     * 获取业务通过mem_colonel_relation查询业务员所有子集
     * @param colonelId
     * @return
     */
    @GetMapping(PREFIX + "/getAllChildColonel")
    CommonResult<List<Long>> getAllChildColonel(@RequestParam("colonelId") Long colonelId);

    /**
     * 更新绑定微信公众号openid
     */
    @PostMapping(PREFIX + "/updateColonelPublishOpenidBind")
    CommonResult<Boolean> updateColonelPublishOpenidBind(@RequestBody WxColonelPublishOpenidBindReqVO reqVO);

    /**
     * 获取平台下业务员信息
     * @param sysCode
     * @return
     */
    @GetMapping(PREFIX + "/getColonelIdList")
    CommonResult<List<Long>> getColonelIdList(@RequestParam("sysCode") Long sysCode,@RequestParam("status") Integer status,
                                              @RequestParam(value = "colonelName", required = false) String colonelName,@RequestParam(value = "colonelPhone", required = false) String colonelPhone,@RequestParam(value = "areaIds", required = false) List<Long> areaIds);

    /**
     * 获取上次门店拜访时间
     * @param branchId
     * @return
     */
    @PostMapping(PREFIX + "/getLastVisitTime")
    CommonResult<List<MemColonelRespVO>> getLastVisitTime(@RequestBody List<Long> branchIds);


    /**
     * 删除门店业务员关系表
     */
    @PostMapping(PREFIX + "/deleteColonelBranchZip")
    void deleteColonelBranchZip(@RequestParam("branchId")Long branchId,@RequestParam("colonelId") Long colonelId,@RequestParam("sysCode") Long sysCode);

    @PostMapping(PREFIX + "/importDataEvent")
    public CommonResult<String> importDataEvent(@RequestBody MemColonelImportForm memColonelImportForm);


    /**
     * 获取业务员拜访明细
     * @param MemColonelVisitLogVO 请求参数
     * @return
     */
    @PostMapping(PREFIX + "/getColonelVisitLog")
    CommonResult<List<MemColonelVisitLogRespDTO>> getColonelVisitLog(@RequestBody MemColonelVisitLogVO MemColonelVisitLogVO);

    @PostMapping(PREFIX + "/getShopAppRecode")
    CommonResult<String> getShopAppRecode(@RequestBody MemShopAppRecodeReqVO reqVO);

    /**
     * 查询该areaId是否生成过数据
     */
    @GetMapping(PREFIX + "/getAreaIdExistColone")
    public CommonResult<List<MemColonelRespVO>> getAreaIdExistColone(@RequestParam("areaId") Long areaId, @RequestParam("sysCode") Long sysCode);
}
