package com.zksr.product.api.spu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品SPU对象 prdt_spu
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@Data
@ApiModel("商品SPU - prdt_spu分页 Request VO")
public class PrdtSpuSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 商品SPU_id */
    @ApiModelProperty(value = "商品SPU_id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id",required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 品牌名称 */
    @Excel(name = "品牌名称")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价" ,required = true)
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号" ,required = true)
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称" ,required = true)
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图" ,required = true)
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频" ,required = true)
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播" ,required = true)
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)" ,required = true)
    private String details;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量" ,required = true)
    private Long stock;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否" ,required = true)
    private Long isDelete;

    /** 最旧生产日期 */
    @Excel(name = "最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最旧生产日期",  example = "最旧生产日期" ,required = true)
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @Excel(name = "最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新生产日期",  example = "最新生产日期" ,required = true)
    private Date latestDate; 		 // 最新生产日期

    /** 是否开启多规格 1-是 0-否 */
    @Excel(name = "是否开启多规格 1-是 0-否")
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否",  example = "1-是 0-否" ,required = true)
    private Long isSpecs;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值" ,required = true)
    private Long status;

    /** 产地 */
    @Excel(name = "产地")
    @ApiModelProperty(value = "产地", example = "示例值" ,required = true)
    private String originPlace;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值" ,required = true)
    private String memo;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private BigDecimal largeSize;

    /** 是否开启联动换算 1-是 0-否 */
    @Excel(name = "是否开启联动换算 1-是 0-否")
    @ApiModelProperty(value = "是否开启联动换算 1-是 0-否")
    private Integer isLinkage;

    /** 保质期 */
    @Excel(name = "保质期")
    @ApiModelProperty(value = "保质期" ,required = true)
    private Integer expirationDate;		 // 保质期


    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    @ApiModelProperty(value = "外部来源商品编号")
    private String sourceNo;

    /** 外部来源商品编号 */
    @Excel(name = "来源（B2B、ERP）")
    @ApiModelProperty(value = "来源（B2B、ERP）")
    private String source;

    /**
     * 是否同步标准价格 0-不同步 1-同步（默认同步）
     */
    @ApiModelProperty(value = "是否同步标准价格 0-不同步 1-同步（默认同步）")
    private Integer syncMarkPrice;

    /**
     * 是否同步供货价格 0-不同步 1-同步（默认同步）
     */
    @ApiModelProperty(value = "是否同步供货价格 0-不同步 1-同步（默认同步）")
    private Integer syncCostPrice;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    @ApiModelProperty(value = "SPU辅助的商品编号")
    private String auxiliarySpuNo;


    /**
     * 是否为第三方同步商品，true表示同步，false表示不同步
     */
    @ApiModelProperty(value = "是否为第三方同步商品")
    private Boolean isSyncThirdParty;

    /** 关联关键词 */
    @Excel(name = "关联关键词")
    @ApiModelProperty(value = "关联关键词")
    private String keywords;

    /**
     * 商品计价方式类型 默认为普通商品 1：普通商品 2：称重商品
     */
    @ApiModelProperty(value = "商品计价方式类型")
    private Integer pricingWay;

    /** 是否零售(0否1是) */
    @ApiModelProperty(value = "是否零售(0否1是)")
    private Integer enableRetail;

    /** 零售分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额) */
    @ApiModelProperty(value = "零售分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额)")
    private Integer retailProfitMode;

    /** 是否B2b(0否1是) */
    @ApiModelProperty(value = "是否B2b(0否1是)")
    private Integer enableWholesale;

    /** B2b分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额) */
    @ApiModelProperty(value = "B2b分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额)")
    private Integer wholesaleProfitMode;
    
    /** 其他属性 */
    @Excel(name = "其他属性")
    private String otherAttr;
}
