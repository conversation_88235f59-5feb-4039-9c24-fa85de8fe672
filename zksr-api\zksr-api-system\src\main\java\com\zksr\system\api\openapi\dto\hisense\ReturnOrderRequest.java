package com.zksr.system.api.openapi.dto.hisense;

import java.util.List;

// 退款订单主类
public class ReturnOrderRequest {
    // 订单信息
    private RefundOrder refundOrder;
    // 商品明细
    private List<RefundSkuOrder> refundSkuOrders;

    // Getters and Setters
    public RefundOrder getRefundOrder() {
        return refundOrder;
    }

    public void setRefundOrder(RefundOrder refundOrder) {
        this.refundOrder = refundOrder;
    }

    public List<RefundSkuOrder> getRefundSkuOrders() {
        return refundSkuOrders;
    }

    public void setRefundSkuOrders(List<RefundSkuOrder> refundSkuOrders) {
        this.refundSkuOrders = refundSkuOrders;
    }
}

// 退款订单信息类
class RefundOrder {
    // 销售单号
    private Long orderCode;
    // 供应商ID
    private Long supplierId;
    // 供应商名称
    private String supplierName;
    // 创建时间
    private String createTime;
    // 店铺ID
    private String shopId;
    // 店铺名称
    private String shopName;
    // 退款单号
    private String refundNo;
    // 退款原因
    private String reason;

    // Getters and Setters
    public Long getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(Long orderCode) {
        this.orderCode = orderCode;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

// 退款SKU订单类
class RefundSkuOrder {
    // ERP商品编号
    private Long erpItemNo;
    // 数量
    private Integer quantity;
    // 海信商城同步过去得商品
    private String skuCode;
    // 商品名称
    private String skuName;

    // Getters and Setters
    public Long getErpItemNo() {
        return erpItemNo;
    }

    public void setErpItemNo(Long erpItemNo) {
        this.erpItemNo = erpItemNo;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
}