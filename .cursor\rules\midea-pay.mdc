---
description: 
globs: 
alwaysApply: false
---
## 1. 现有美的付是否支持分账？

### 1.1 配置与枚举
- `PayChannelEnum` 中，`MIDEA_PAY`（美的付）配置的 `onlineSupportDivide` 字段为 `true`，理论上**支持在线分账**。
- 但实际是否实现分账，还需看支付请求参数和业务实现。

### 1.2 支付请求参数
- `MideaPayWechatSubOrderReqVO`（美的付微信子单请求对象）中有如下分账相关字段：
  - `profitParams`：分账参数
  - `profitSharing`：分账标志
- 但在 `MideaPaySdkClient.mideaPay()` 方法中，`profitParams` 和 `profitSharing` 字段**未见实际赋值和业务逻辑**，即分账参数未被主动设置。

### 1.3 业务流程
- `PayOrderServiceImpl.notifyOrder()` 回调处理时，会调用 `divideDtlService.createDivide(payFlow, notify);` 记录分账信息，但**未见美的付专用分账实现**。
- 订单分账回调接口 `OrderApiImpl.updateOrderDivideSettleState` 仅做了结算状态更新，未见与美的付分账的直接交互。

### 1.4 结论
- **目前美的付的分账参数已预留，但实际支付流程中未主动传递分账参数，分账功能未落地实现。**

---

## 2. 如果要支持美的付分账，需要如何更改？

### 2.1 需要修改的类
1. **支付请求参数组装**
   - `MideaPaySdkClient.mideaPay()`：在构造 `MideaPayWechatSubOrderReqVO` 时，需根据业务分账需求，设置 `profitParams` 和 `profitSharing` 字段。
   - `getMideaPayWechatSubOrder()`：完善子单分账参数的组装逻辑。

2. **分账明细记录**
   - `divideDtlService.createDivide()` 及其实现类：需根据美的付分账回调，完善分账明细的落地逻辑。

3. **支付回调处理**
   - `PayOrderServiceImpl.notifyOrder()`：根据美的付分账回调结果，更新订单和分账状态。

4. **订单与分账关系**
   - 订单实体（如 `AccPay`、`AccPayFlow`）需补充分账相关字段（如分账状态、分账金额等）。

5. **分账回调接口**
   - 若美的付平台有分账回调，需新增或完善回调接口，处理分账结果。

### 2.2 需要修改的订单信息
- 订单表（如 `acc_pay`、`acc_pay_flow`）需增加分账相关字段（如 `divide_status`、`divide_amount`）。
- 订单分账明细表（如 `acc_divide_dtl`）需与支付流水、订单建立关联。

### 2.3 业务流程建议
1. 下单时，若需分账，组装分账参数，传递给美的付。
2. 支付成功后，监听美的付分账回调，更新分账状态。
3. 订单结算时，校验分账是否完成，确保资金安全。

---

## 3. 参考代码片段（伪代码）

```java
// 1. 组装分账参数
MideaPayWechatSubOrderReqVO subOrder = new MideaPayWechatSubOrderReqVO();
subOrder.setProfitParams(json分账明细);
subOrder.setProfitSharing("Y"); // 启用分账

// 2. 支付请求时传递分账参数
wechatReqVO.setSubOrders(JsonUtils.toJsonString(subOrderList));

// 3. 回调处理
if (isMideaPayDivideCallback) {
    // 更新分账状态
    accPayFlow.setDivideStatus(已分账);
    divideDtlService.createDivide(payFlow, notify);
}
```

---

## 4. 总结

- **现状**：美的付分账参数已预留，但未实际落地，需补充分账参数组装与回调处理。
- **改造点**：
  - 组装并传递分账参数到美的付
  - 完善分账明细记录与回调处理
  - 订单表、支付流水表补充分账字段
  - 若有分账回调，需实现回调接口

如需详细代码改造方案或具体表结构设计，可进一步说明！