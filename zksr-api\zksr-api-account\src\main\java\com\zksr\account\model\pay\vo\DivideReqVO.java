package com.zksr.account.model.pay.vo;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.PayChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/17 14:04
 */
@Data
@ApiModel(description = "请求分账")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DivideReqVO {
    
    @ApiModelProperty(value = "实际支付单号")
    private String tradeNo;
    
    @ApiModelProperty(value = "分账流水ID")
    private Long divideFlowId;
    
    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;
    
    @ApiModelProperty(value = "商户ID", required = true)
    private Long merchantId;

    /**
     * 商户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    @ApiModelProperty(value = "分账金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付平台")
    private PayChannelEnum platform;

    @ApiModelProperty(value = "支付流水")
    private PayFlowDTO payFlow;

    @ApiModelProperty(value = "支付商户号")
    private String payMerchantNo;

    @ApiModelProperty(value = "接受分账商户号")
    private String receiveMerchantNo;
    
    /**
     * 分销模式
     */
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;
}
