package com.zksr.member.api.member.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户地址对象 mem_member_address
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ApiModel("用户地址 - mem_member_address Response VO")
public class MemberAddressRespDTO {
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId) */
    @Excel(name = "三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    @ApiModelProperty(value = "三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long threeAreaCityId;

    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 详细收货地址 */
    @Excel(name = "详细收货地址")
    @ApiModelProperty(value = "详细收货地址")
    private String deliveryAddress;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "详细收货地址")
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private Long version;

}
