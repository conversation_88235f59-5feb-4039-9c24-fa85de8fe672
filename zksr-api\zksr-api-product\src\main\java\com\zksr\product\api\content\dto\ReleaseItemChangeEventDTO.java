package com.zksr.product.api.content.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/21 10:26
 */
@Data
@AllArgsConstructor
public class ReleaseItemChangeEventDTO {

    @ApiModelProperty("城市ID")
    private Long areaId;

    @ApiModelProperty("平台商ID")
    private Long sysCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReleaseItemChangeEventDTO that = (ReleaseItemChangeEventDTO) o;
        return Objects.equals(areaId, that.areaId) && Objects.equals(sysCode, that.sysCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(areaId, sysCode);
    }
}
