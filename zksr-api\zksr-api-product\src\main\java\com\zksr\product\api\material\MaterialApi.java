package com.zksr.product.api.material;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @time 2025/1/11
 * @desc
 */
@FeignClient(
        contextId = "remoteMaterialApi",
        value = ApiConstants.NAME
)
public interface MaterialApi {

    String PREFIX = ApiConstants.PREFIX + "/material";

    /**
     * 素材数据
     * @param applyType 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品
     * @param applyId   促销活动ID, 全国上架ID, 本地上架ID
     * @return 素材数据
     */
    @GetMapping(PREFIX + "/getMaterialCache")
    CommonResult<MaterialCacheVO> getMaterialCache(@RequestParam("applyType") Integer applyType, @RequestParam("applyId") Long applyId);
}
