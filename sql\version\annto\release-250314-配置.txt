 -- -----------------------------------  xxl-job 增加配置 start ---------------------------------
 任务描述     :  处理微信B2B支付充值流水自动分账
 JobHandler  :   wxB2bRechargeDivide
 CRON        :   * 0/10 * * * ?
 状态        ： 	 RUNNING
 ------------------------------------------------

 -- -----------------------------------  xxl-job 增加配置 end ---------------------------------



--nacos配置
--application-rocketmq-system-dev.yml 新增配置：
definition:
syncDataBranchValueInfoEvent;supplierSyncDataBranchValueInfoEvent;

bindings:
        #门店储值充值/提现信息
        syncDataBranchValueInfoEvent-out-0:
          destination: syncDataBranchValueInfoEvent
        syncDataBranchValueInfoEvent-in-0:
          destination: syncDataBranchValueInfoEvent
          group: syncDataBranchValueInfoEventGroup

        #入驻商同步数据 -- 推送门店储值充值/提现信息
        supplierSyncDataBranchValueInfoEvent-out-0:
          destination: supplierSyncDataBranchValueInfoEvent
        supplierSyncDataBranchValueInfoEvent-in-0:
          destination: supplierSyncDataBranchValueInfoEvent
          group: supplierSyncDataBranchValueInfoEventtGroup


-- application-rocketmq-account-dev.yml 新增配置
bindings:
        # 推送第三方门店储值充值/提现信息
        syncDataBranchValueInfoEvent-out-0:
          destination: syncDataBranchValueInfoEvent


---------------------------------- 订单货到付款默认支付成功消息通知处理  application-rocketmq-portal-dev.yml---------------------------------------
-- bindings 新增
#货到付款订单发送支付成功消息
orderHdfkSuccessEvent-out-0:
          destination: orderHdfkSuccessEvent