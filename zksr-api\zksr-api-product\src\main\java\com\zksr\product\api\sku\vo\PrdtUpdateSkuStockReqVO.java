package com.zksr.product.api.sku.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 更新SKU商品库存
 * @date 2024/9/23 16:42
 */
@ApiModel(description = "更新商品库存")
@Data
public class PrdtUpdateSkuStockReqVO {

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("操作库存")
    @Min(value = 1, message = "最少变更一个数量")
    private BigDecimal stock;

    @ApiModelProperty("true-增量, false-递减")
    private boolean incry;
}
