package com.zksr.product.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p>
 * bpm 系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {

    // ==========  通用流程处理 模块 1-009-000-000 ==========
    ErrorCode HIGHLIGHT_IMG_ERROR = new ErrorCode(1_009_000_002, "获取高亮流程图异常");

    ErrorCode PRDT_CHECK_SUPPLIER = new ErrorCode(1_009_001_001, "入驻商不能为空");

    ErrorCode PRDT_BRAND_NO_REPT = new ErrorCode(1_101_001_001, "品牌编号重复");

    ErrorCode PRDT_BRAND_NAME_REPT = new ErrorCode(1_101_001_002, "品牌名称重复");

    // ========== 平台商展示分类  ==========
    ErrorCode PRDT_SALE_CLASS_NOT_EXISTS = new ErrorCode(1_101_004_000, "平台商展示分类不存在");

    ErrorCode PRDT_SALE_CLASS_NAME_NOT_UNIQUE = new ErrorCode(1_101_004_001, "平台商展示分类名称重复");
    ErrorCode PRDT_SALE_CLASS_LEVEL_EXCEED_MAX = new ErrorCode(1_101_004_002, "平台商展示分类级别超出最大值");
    ErrorCode PRDT_SALE_CLASS_PID_ERROR = new ErrorCode(1_101_004_003, "平台商展示分类上级分类异常");
    ErrorCode PRDT_SALE_CLASS_IS_NULL = new ErrorCode(1_101_004_004, "平台商展示分类不能为空");
    ErrorCode PRDT_SALE_CLASS_LEVEL_NOT_MAKE = new ErrorCode(1_101_004_005, "当前选择的平台商展示分类不符，请选择3级分类");
    ErrorCode PRDT_SALE_CLASS_CHANGE_STATUS_MISSING_PARAMETER = new ErrorCode(1_101_004_006, "平台展示分类状态变更缺少参数");
    ErrorCode PRDT_SALE_CLASS_NOT_SECOND_LEVEL_EXISTS= new ErrorCode(1_101_004_007, "当前全国展示分类关联的二级分类不存在");
    ErrorCode PRDT_SALE_CLASS_NOT_FIRST_LEVEL_EXISTS = new ErrorCode(1_101_004_008, "当前全国展示分类关联的一级分类不存在");
    ErrorCode PRDT_SALE_CLASS_IS_SUB_EXISTS = new ErrorCode(1_101_004_009, "当前全国展示分类存在子级不允许变更级别");
    ErrorCode PRDT_SALE_CLASS_BIND_ITEM_EXISTS = new ErrorCode(1_101_004_010, "当前全国展示分类绑定了上架商品不允许变更级别");
    ErrorCode PRDT_SALE_CLASS_HAS_CHILD = new ErrorCode(1_101_004_011, "{} 存在下级展示分类");
    ErrorCode PRDT_SALE_CLASS_HAS_RELEASE = new ErrorCode(1_101_004_012, "{} 存在上架商品");
    ErrorCode PRDT_SALE_CLASS_REMOVE_MULTILEVE = new ErrorCode(1_101_004_013, "当前选中的分类下所有的三级分类都存在已上架商品");

    ErrorCode PRDT_SALE_CLASS_COPY_DATA_NULL = new ErrorCode(1_101_004_014, "展示分类复制数据异常，复制全国分类ID或目标全国父级分类ID为空！");

    //========== 平台商管理分类  ==========
    ErrorCode PRDT_CATGORY_NOT_EXISTS = new ErrorCode(1_101_001_000, "平台商管理分类不存在");
    ErrorCode PRDT_CATGORY_CATGORY_NAME_NOT_UNIQUE = new ErrorCode(1_101_001_001, "平台商管理分类名称重复");
    ErrorCode PRDT_CATGORY_CATGORY_LEVEL_EXCEED_MAX = new ErrorCode(1_101_001_002, "平台商管理分类级别超出最大值");
    ErrorCode PRDT_CATGORY_RATE_ERROR = new ErrorCode(1_101_001_003, "平台商管理分类分润比例异常");
    ErrorCode PRDT_CATGORY_CATGORY_PID_ERROR = new ErrorCode(1_101_001_004, "平台商管理分类上级类别异常");
    ErrorCode PRDT_CATGORY_IS_NULL = new ErrorCode(1_101_001_005, "平台商管理分类不能为空");
    ErrorCode PRDT_CATGORY_LEVEL_NOT_MAKE = new ErrorCode(1_101_001_006, "当前选择的平台商管理分类不符，请选择3级分类");
    ErrorCode PRDT_CATGORY_NOT_UPDATE = new ErrorCode(1_101_001_007, "管理分类绑定了入驻商不允许修改级别");
    ErrorCode PRDT_CATGORY_IS_SUB_EXISTS = new ErrorCode(1_101_018_008, "当前平台商管理分类存在子级不允许变更级别");
    ErrorCode PRDT_CATGORY_BIND_SPU_EXISTS = new ErrorCode(1_101_018_009, "当前平台商管理分类绑定了商品不允许变更级别");

    // ========== 入驻商-平台商管理分类 关联关系  ==========
    ErrorCode PRDT_SUPPLIER_CLASS_NOT_EXISTS = new ErrorCode(1_101_003_000, "入驻商-平台商管理分类 关联关系不存在");
    ErrorCode SUPPLIER_QUERY_FAIL = new ErrorCode(1_101_003_003, "入驻商不存在，编码[{}]");

    // ========== 城市级管理分类扣点设置  ==========
    ErrorCode PRDT_CATGORY_RATE_NOT_EXISTS = new ErrorCode(1_101_002_000, "城市级管理分类扣点设置不存在");

    // ========== 城市展示分类 ==========
    ErrorCode PRDT_AREA_CLASS_NOT_EXISTS = new ErrorCode(1_101_018_000, "城市展示分类不存在");
    ErrorCode PRDT_AREA_CLASS_LEVEL_EXCEED_MAX = new ErrorCode(1_101_018_001, "城市展示分类级别超出最大值");
    ErrorCode PRDT_AREA_CLASS_NAME_REUSE= new ErrorCode(1_101_018_002, "城市展示分类名称重复");
    ErrorCode PRDT_AREA_CLASS_PID_ERROR= new ErrorCode(1_101_018_003, "城市展示分类上级分类异常");
    ErrorCode PRDT_AREA_CLASS_IS_NULL = new ErrorCode(1_101_018_004, "城市展示分类不能为空");
    ErrorCode PRDT_AREA_CLASS_LEVEL_NOT_MAKE = new ErrorCode(1_101_018_005, "当前选择的城市展示分类不符，请选择3级分类");
    ErrorCode PRDT_AREA_CLASS_NOT_UPDATE = new ErrorCode(1_101_018_006, "城市展示分类绑定了渠道不允许修改级别");
    ErrorCode PRDT_AREA_CLASS_DZWL_CHANNEL_NPT_BIND = new ErrorCode(1_101_018_007, "城市展示分类电子围栏与渠道不能同时开启");
    ErrorCode PRDT_AREA_CLASS_CHANNEL_BIND_EXIST = new ErrorCode(1_101_018_008, "城市展示分类子分类绑定了渠道不允许开启电子围栏");
    ErrorCode PRDT_AREA_CLASS_SUB_LEVEL_MAX = new ErrorCode(1_101_018_009, "城市展示分类子分类级别超出最大值");
    ErrorCode PRDT_AREA_CLASS_NOT_SECOND_LEVEL_EXISTS= new ErrorCode(1_101_018_010, "当前城市展示分类关联的二级分类不存在");
    ErrorCode PRDT_AREA_CLASS_NOT_FIRST_LEVEL_EXISTS = new ErrorCode(1_101_018_011, "当前城市展示分类关联的一级分类不存在");
    ErrorCode PRDT_AREA_CLASS_IS_SUB_EXISTS = new ErrorCode(1_101_018_012, "当前城市展示分类存在子级不允许变更级别");
    ErrorCode PRDT_AREA_CLASS_ITEM_IS_NOT_NULL = new ErrorCode(1_101_018_012, "当前城市展示分类绑定了上架商品不允许变更级别");
    ErrorCode PRDT_AREA_CLASS_AREA_RELEASE = new ErrorCode(1_101_018_013, "当前类目城市上架商品还有商品上架中");
    ErrorCode PRDT_AREA_CLASS_SUPPLIER_RELEASE = new ErrorCode(1_101_018_014, "当前类目全国上架商品还有商品上架中");
    ErrorCode PRDT_AREA_CLASS_AREA_HAVE_CHILD = new ErrorCode(1_101_018_015, "当前分类存在下级分类");

    ErrorCode PRDT_AREA_CLASS_REMOVE_MULTILEVE = new ErrorCode(1_101_018_016, "当前选中的分类下所有的三级分类都存在已上架商品！");

    ErrorCode PRDT_AREA_CLASS_COPY_TYPE_NULL = new ErrorCode(1_101_018_017, "展示分类复制数据异常，未设置复制类型！");

    ErrorCode PRDT_AREA_CLASS_COPY_0_DATA_NULL = new ErrorCode(1_101_018_018, "展示分类复制数据异常，复制城市分类ID或目标城市父级分类ID或城市ID为空！");

    ErrorCode PRDT_AREA_CLASS_COPY_1_DATA_NULL = new ErrorCode(1_101_018_019, "展示分类复制数据异常，复制城市ID或目标城市ID为空！");

    // ========== 平台商城市分组-展示分类关联 ==========
    ErrorCode PRDT_GROUP_SALE_CLASS_NOT_EXISTS = new ErrorCode(1_101_002_000, "平台商城市分组-展示分类关联不存在");


    // ========== 商品基本信息 ==========
    ErrorCode PRDT_PROPERTY_NOT_EXISTS = new ErrorCode(1_101_002_001, "规格名称不存在");
    ErrorCode PRDT_PROPERTY_EXISTS = new ErrorCode(1_101_002_001, "规格名称已存在");
    ErrorCode PRDT_PROPERTY_VAL_NOT_EXISTS = new ErrorCode(1_101_003_001, "规格值不存在");
    ErrorCode PRDT_PROPERTY_VAL_EXISTS = new ErrorCode(1_101_003_002, "规格值已存在");
    ErrorCode PRDT_SKU_NOT_EXISTS = new ErrorCode(1_101_004_001, "商品SKU不存在");
    ErrorCode PRDT_SKU_EXISTS = new ErrorCode(1_101_004_002, "商品SKU已存在");
    ErrorCode PRDT_SKU_STOCK_NOT_ENOUGH = new ErrorCode(1_101_004_003, "商品SKU【{}】库存不足");
    ErrorCode PRDT_CB_SKU_STOCK_NOT_ENOUGH = new ErrorCode(1_101_004_003, "组合商品【{}】库存不足");
    ErrorCode PRDT_SKU_PROPERTY_NOT_EXISTS = new ErrorCode(1_101_004_004, "商品SKU信息与规格信息不匹配");
    ErrorCode PRDT_SKU_CHECK_MARK_PRICE = new ErrorCode(1_101_004_005, "商品SKU的标准价和配送价均不能低于供货价(成本价)");
    ErrorCode PRDT_SPU_NOT_EXISTS = new ErrorCode(1_101_005_001, "商品SPU不存在");
    ErrorCode PRDT_SPU_EXISTS = new ErrorCode(1_101_005_002, "商品SPU已存在");
    ErrorCode PRDT_SPU_STATE_DISABLE = new ErrorCode(1_101_005_003, "商品 SPU 已停用");
    ErrorCode PRDT_SPU_DETAILS_SIZE = new ErrorCode(1_101_005_004, "商品详情过大，不允许保存");
    ErrorCode PRDT_SPU_SPUNO_EXISTS = new ErrorCode(1_101_005_005, "商品编码已存在，请重新设置");
    ErrorCode PRDT_SPU_PRODUCTION_DATE_NOT_NORM = new ErrorCode(1_101_005_006, "生产日期设置不规范，最旧生产日期不能大于最新生产日期");
    ErrorCode PRDT_SPU_IMPORT_EMPTY = new ErrorCode(1_101_005_007, "导入商品为空");
    ErrorCode PRDT_SPU_SPU_ID_EXISTS = new ErrorCode(1_101_005_008, "商品信息异常，SPU不存在");
    ErrorCode PRDT_SPU_BARCODE_REPEAT = new ErrorCode(1_101_005_009, "SKU条码重复");
    ErrorCode PRDT_SPU_CATEGORY_NOT_EXISTS = new ErrorCode(1_101_005_010, "{} 商品管理分类不存在");
    ErrorCode PRDT_SPU_REPEAT_SHARE = new ErrorCode(1_101_005_011, "{} 商品已经开启共享了");
    ErrorCode PRDT_SPU_CHECK_SOURCE_STOCK = new ErrorCode(1_101_005_012, "外部商品不允许修改库存");
    ErrorCode PRDT_SPU_RELEASE_VALIDATE = new ErrorCode(1_101_005_013, "{} 存在上架商品无法操作");
    ErrorCode PRDT_SPU_ACTIVITY_VALIDATE = new ErrorCode(1_101_005_014, "{} 存在生效中或者等待发布的促销活动, 不可修改价格类敏感信息");
    ErrorCode PRDT_SPU_MAX_PROFIT_RATE = new ErrorCode(1_101_005_015, "商品销售利润占比最大29%");
    ErrorCode PRDT_SKU_COST_PRICE_ERR = new ErrorCode(1_101_005_016, "上架商品供货价不能为空");

    ErrorCode PRDT_SPU_CHECK_SUPPLIER_SYNC = new ErrorCode(1_101_005_016, "已对接第三方的入驻商不允许新增商品！！！");

    ErrorCode PRDT_SPU_DELETE_CHECK_ORDER_SPU = new ErrorCode(1_101_005_017, "选中的SPU已产生过订单数据，不能删除，请重新选择！产生过订单数据的商品信息：{}");

    ErrorCode PRDT_SPU_CHECK_SOURCE_NO_PC = new ErrorCode(1_101_005_018, "新增/修改不能设置外部来源编号！");

    ErrorCode PRDT_SPU_RETAIL_PROFIT_RATE_REQUIRED = new ErrorCode(1_101_005_019, "零售分润模式为按比例时，分润比例必填");
    ErrorCode PRDT_SPU_RETAIL_PROFIT_AMOUNT_REQUIRED = new ErrorCode(1_101_005_020, "零售分润模式为按金额时，分润金额必填");
    ErrorCode PRDT_SPU_WHOLESALE_PROFIT_RATE_REQUIRED = new ErrorCode(1_101_005_021, "批发分润模式为按比例时，分润比例必填");
    ErrorCode PRDT_SPU_WHOLESALE_PROFIT_AMOUNT_REQUIRED = new ErrorCode(1_101_005_022, "批发分润模式为按金额时，分润金额必填");
    ErrorCode PRDT_SPU_RETAIL_NOT_NULL = new ErrorCode(1_101_005_023, "零售类型SKU必须填写零售价");

    // ========== 城市上架商品 ==========
    ErrorCode PRDT_AREA_ITEM_NOT_EXISTS = new ErrorCode(1_101_006_001, "城市上架商品不存在");
    ErrorCode PRDT_AREA_ITEM_SORT_FALG_NOT_EXISTS = new ErrorCode(1_101_006_002, "城市上架商品移动标识不存在");
    ErrorCode PRDT_AREA_ITEM_SORT_FALG_CHECK_NO = new ErrorCode(1_101_006_003, "城市上架商品顺序为1，不允许上移");
    ErrorCode PRDT_AREA_ITEM_NOT_SHELF_STATUS = new ErrorCode(1_101_006_004, "城市上架商品未上架");
    ErrorCode PRDT_AREA_ITEM_SORT_NOT_EXISTS = new ErrorCode(1_101_006_005, "入驻商移动商品不存在");
    ErrorCode PRDT_AREA_ITEM_AREA_NOT_EXISTS = new ErrorCode(1_101_006_006, "未选择城市");
    ErrorCode PRDT_AREA_ITEM_UPDATE_FALG_NOT_EXISTS = new ErrorCode(1_101_006_007, "城市上架商品修改标识不存在");
    ErrorCode PRDT_AREA_ITEM_SHELF_STATUS_NOT_EXISTS = new ErrorCode(1_101_006_008, "上架商品修改上架状态标识不存在");
    ErrorCode PRDT_AREA_ITEM_SHELF_STATUS_SKU_STATIS = new ErrorCode(1_101_006_008, "选择上架的商品Sku已停用，无法上架");
    ErrorCode PRDT_AREA_ITEM_COPY_TYPE_NULL = new ErrorCode(1_101_006_009, "城市上架商品复制数据异常，未设置复制类型！");
    ErrorCode PRDT_AREA_ITEM_COPY_0_CHECK_NULL = new ErrorCode(1_101_006_010, "城市上架商品复制数据异常，未勾选上架商品");

    ErrorCode PRDT_AREA_ITEM_COPY_DATA_NULL = new ErrorCode(1_101_006_011, "需要复制到新类别的城市上架商品已全部都在新类别中存在！请重新选择");

    // ========== 全国上下架商品 ==========
    ErrorCode PRDT_SUPPLIER_ITEM_NOT_EXISTS = new ErrorCode(1_101_007_001, "入驻商上架商品不存在");
    ErrorCode PRDT_SUPPLIER_ITEM_SORT_FALG_NOT_EXISTS = new ErrorCode(1_101_007_002, "入驻商上架商品移动标识不存在");
    ErrorCode PRDT_SUPPLIER_ITEM_SORT_FALG_CHECK_NO = new ErrorCode(1_101_007_003, "入驻商上架商品顺序为1，不允许上移");
    ErrorCode PRDT_SUPPLIER_ITEM_SORT_NOT_EXISTS = new ErrorCode(1_101_007_004, "入驻商移动商品不存在");
    ErrorCode PRDT_SUPPLIER_ITEM_NOT_SHELF_STATUS = new ErrorCode(1_101_007_005, "入驻商上架商品未上架");
    ErrorCode PRDT_SUPPLIER_ITEM_UPDATE_FALG_NOT_EXISTS = new ErrorCode(1_101_007_006, "入驻商上架商品修改标识不存在");
    ErrorCode PRDT_SUPPLIER_ITEM_SHELF_STATUS_NOT_EXISTS = new ErrorCode(1_101_007_007, "上架商品修改上架状态标识不存在");
    ErrorCode PRDT_SUPPLIER_ITEM_SHELF_STATUS_SKU_STATIS = new ErrorCode(1_101_006_008, "选择上架的商品Sku已停用，无法上架");
    ErrorCode PRDT_SUPPLIER_ITEM_ID_NOT_NULL = new ErrorCode(1_101_007_007, "上架商品ID集合不能为空");

    // ========== 价格方案 ==========
    ErrorCode PRDT_SKU_PRICE_NOT_EXISTS = new ErrorCode(1_101_008_001, "价格方案不存在");
    ErrorCode PRDT_SKU_PRICE_EXISTS = new ErrorCode(1_101_008_002, "价格方案已存在");
    ErrorCode PRDT_SKU_PRICE_NOT_SET = new ErrorCode(1_101_008_003, "价格方案未设置");
    ErrorCode PRDT_SKU_PRICE_PRICING_NOT_EXISTS = new ErrorCode(1_101_008_004, "批量订价，请设置正确的价格选项、价格类型、调价比率，不允许为空");
    ErrorCode PRDT_SKU_PRICE_SAVE_FLAG = new ErrorCode(1_101_008_005, "定价标识不存在");

    // ========== 全国价格分组价格码 ==========
    ErrorCode PRDT_SUPPLIER_GROUP_PRICE_NOT_EXISTS = new ErrorCode(1_101_009_001, "平台商城市分组价格不存在");
    ErrorCode PRDT_SUPPLIER_GROUP_PRICE_EXISTS = new ErrorCode(1_101_009_002, "平台商城市分组价格已存在");

    // ========== 城市价格分组价格码 ==========
    ErrorCode PRDT_AREA_CHANNEL_PRICE_NOT_EXISTS = new ErrorCode(1_101_010_001, "城市渠道价格不存在");
    ErrorCode PRDT_AREA_CHANNEL_PRICE_EXISTS = new ErrorCode(1_101_010_002, "城市渠道价格已存在");
    // ========== 平台管理类别 ==========
    ErrorCode THE_PLATFORM_MANAGEMENT_CATEGORY_IS_EMPTY = new ErrorCode(1_101_011_001, "导入平台管理类别为空");

    // ====================商品调价单=============================
    ErrorCode PRDT_ADJUST_PRICES_NOT_EXISTS = new ErrorCode(1_101_012_001, "商品调价单主不存在");

    ErrorCode PRDT_ADJUST_PRICES_DTL_NOT_EXISTS = new ErrorCode(1_101_012_002, "商品调价单主不存在");

    ErrorCode PRDT_ADJUST_PRICES_SUPPLIER_NOT_MATCH = new ErrorCode(1_101_012_003, "商品调价单商品明细匹配入驻商与上传入驻商不匹配！");

    ErrorCode PRDT_ADJUST_PRICES_CHECK_PRICE_ZERO = new ErrorCode(1_101_012_004, "商品调价单商品调价不能调整为0或空！商品编号为：{}");

    ErrorCode PRDT_ADJUST_PRICES_CHECK_PARTNER_ROLE = new ErrorCode(1_101_012_005, "平台商不能审核有主运营商的入驻商商品调价单！");

    ErrorCode PRDT_ADJUST_PRICES_CHECK_MIN_PRICE_SIZE = new ErrorCode(1_101_012_006, "商品调价后的小单位标准价必须大于或等于供货价！商品编号为：{}");

    ErrorCode PRDT_ADJUST_PRICES_CHECK_MID_PRICE_SIZE = new ErrorCode(1_101_012_007, "商品调价后的中单位标准价必须大于或等于供货价！商品编号为：{}");

    ErrorCode PRDT_ADJUST_PRICES_CHECK_LARGE_PRICE_SIZE = new ErrorCode(1_101_012_008, "商品调价后的大单位标准价必须大于或等于供货价！商品编号为：{}");

    ErrorCode PRDT_ADJUST_PRICES_CHECK_SUPPLIER_SYNC = new ErrorCode(1_101_012_009, "已对接第三方的入驻商不允许设置商品调价单！！！");




    // ========== 商品分享 ==========
    ErrorCode SHARE_PRODUCT_SAVE_FAIL = new ErrorCode(1_101_012_001, "商品分享失败");
    ErrorCode SHARE_PRODUCT_EXPIRE = new ErrorCode(1_101_012_002, "转发分享链接已过期");
    ErrorCode SHARE_PRODUCT_NOT_EXISTS = new ErrorCode(1_101_012_003, "转发分享链接不存在");

    ErrorCode SHARE_PRODUCT_NOT_NULL = new ErrorCode(1_101_012_004, "分享链接不能为空");

    // ========== 运营展示类别 ==========
    ErrorCode THE_IMPORTED_CARRIER_DISPLAY_CATEGORY_IS_EMPTY = new ErrorCode(1_101_013_001, "导入运营商展示类别为空");

    // ========== 平台展示类别 ==========
    ErrorCode PLATFORM_DISPLAY_CATEGORY_IMPORT_EMPTY = new ErrorCode(1_101_014_001, "导入平台展示类别为空");

    // ========== 要货单数据 ==========
    ErrorCode PRDT_YH_DATA_BRANCH_ERR = new ErrorCode(1_101_015_001, "要货门店不存在");
    ErrorCode PRDT_YH_DATA_DTL_REPEAT = new ErrorCode(1_101_015_002, "商品编号或者条码重复");
    ErrorCode PRDT_YH_DATA_BATCH_NO_REPEAT = new ErrorCode(1_101_015_003, "批次号重复");

    // ========== 经营屏蔽方案 ==========
    ErrorCode PRDT_BLOCK_SCHEME_NOT_EXISTS = new ErrorCode(1_101_016_001, "经营屏蔽方案不存在");
    ErrorCode PRDT_BLOCK_BRANCH_NOT_EXISTS = new ErrorCode(1_101_016_002, "经营屏蔽客户不存在");
    ErrorCode PRDT_BLOCK_SKU_NOT_EXISTS = new ErrorCode(1_101_016_003, "经营屏蔽sku不存在");

    // ========== 组合促销商品 ==========
    ErrorCode PRDT_COMBINE_NOT_EXISTS = new ErrorCode(1_101_017_001, "组合促销商品不存");
    ErrorCode PRDT_COMBINE_ACTIVITY_NOT_EXISTS = new ErrorCode(1_101_017_002, "活动不存在,CB");
    ErrorCode PRDT_COMBINE_ACTIVITY_NOT_START = new ErrorCode(1_101_017_003, "活动未开始,CB");
    ErrorCode PRDT_COMBINE_ACTIVITY_ALREADY_FINISH = new ErrorCode(1_101_017_003, "活动已结束,CB");



    // ========== 素材库 ==========
    ErrorCode PRDT_MATERIAL_NOT_EXISTS = new ErrorCode(1_101_017_001, "素材信息不存在");

    // ========== 素材应用 ==========
    ErrorCode PRDT_MATERIAL_APPLY_NOT_EXISTS = new ErrorCode(1_101_018_001, "素材应用信息不存在");

    ErrorCode PRDT_MATERIAL_APPLY_CHECK_USER = new ErrorCode(1_101_018_002, "该账号角色不能新增素材应用信息");

    ErrorCode PRDT_MATERIAL_APPLY_CHECK_TYPE = new ErrorCode(1_101_018_003, "请设置素材应用类型，促销/商品");

    ErrorCode PRDT_MATERIAL_APPLY_CHECK_ACTIVITY_ONEC = new ErrorCode(1_101_018_004, "该促销活动<{}>在该生效时间内已经设置了素材，不能再次设置");

    ErrorCode PRDT_MATERIAL_APPLY_CHECK_SUPPLIER_ITEM_ONEC = new ErrorCode(1_101_018_005, "该全国商品<{}>在该生效时间内已设置素材，不能再次设置");

    ErrorCode PRDT_MATERIAL_APPLY_CHECK_AREA_ITEM_ONEC = new ErrorCode(1_101_018_006, "该城市下的商品<{}>在该生效时间内已设置素材，不能再次设置");


}

