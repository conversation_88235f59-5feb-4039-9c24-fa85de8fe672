package com.zksr.member.api.command.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel("操作指令 -  CommandSkuListReqVO ")
public class CommandSkuAddShopCartSaveVO {
    @ApiModelProperty(value = "主键ID")
    private Long commandId;

    @ApiModelProperty(value = "加单指令SKU商品清单明细")
    private List<CommandSkuDtl> commandSkuDtlList;
    @Data
    @ApiModel("加单指令SKU商品清单明细")
    public static class CommandSkuDtl {

        @ApiModelProperty("商品类型, local本地, global 全国")
        private String type;

        @ApiModelProperty(value = "门店ID")
        private Long branchId;

        @ApiModelProperty(value = "入驻商ID")
        private Long supplierId;

        @ApiModelProperty(value = "全国商品上架ID")
        private Long supplierItemId;

        @ApiModelProperty(value = "本地商品上架ID")
        private Long areaItemId;

        @ApiModelProperty(value = "skuId")
        private Long skuId;

        @ApiModelProperty(value = "spuId")
        private Long spuId;

        /**
         * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
         */
        @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位", notes = "7")
        private Integer unitSize = 1;

        @ApiModelProperty(value = "unit, 单位")
        private String unit = StringPool.ZERO;

        @ApiModelProperty(value = "操作数量",required = true)
        private Integer opQty;
    }

}
