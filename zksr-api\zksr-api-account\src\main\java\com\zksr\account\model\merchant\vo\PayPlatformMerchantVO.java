package com.zksr.account.model.merchant.vo;

import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/20 15:36
 */
@Data
@ApiModel(description = "支付平台进件信息")
public class PayPlatformMerchantVO {

    @ApiModelProperty("商户编号")
    private String merchantNo;

    @ApiModelProperty("子商户签约名")
    private String signName;

    @ApiModelProperty("结算卡银行名称")
    private String bankName;

    @ApiModelProperty("结算卡银行分行名")
    private String bankBranch;

    @ApiModelProperty("结算账号")
    private String accountNo;

    @ApiModelProperty("结算账号户名")
    private String accountName;

    /**
     * 子商户类型
     */
    @ApiModelProperty("子商户类型,0-个人, 1-个人工商, 2-企业")
    private String merchantType;

    @ApiModelProperty("银行卡联行号")
    private String bankCode;

    @ApiModelProperty(value = "结算卡类型", notes = "TOPRIVATE-对私,TOPUBLIC-对公")
    private String settleBankType;

    @ApiModelProperty("法人")
    private String legalPerson;

    @ApiModelProperty("法人身份证ID")
    private String legalPersonID;

    @ApiModelProperty("营业执照号码")
    private String businessLicense;

    @ApiModelProperty("联系人")
    private String linkman;

    @ApiModelProperty("联系人手机号")
    private String linkPhone;

    @ApiModelProperty("商户进件状态")
    private MerchantRegisterStateEnum state;

    @ApiModelProperty("进件审核失败原因")
    private String msg;

    @ApiModelProperty("外部链接")
    private String outLink;
}
