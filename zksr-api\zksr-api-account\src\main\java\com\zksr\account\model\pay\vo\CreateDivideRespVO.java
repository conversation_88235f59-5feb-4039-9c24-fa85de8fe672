package com.zksr.account.model.pay.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账请求
 * @date 2025/2/17 11:37
 */
@Data
public class CreateDivideRespVO {

    @ApiModelProperty(value = "是否分账成功, true-成功, false-失败", notes = "这里的成功只是发起请求分账成功")
    private boolean success;

    @ApiModelProperty("分账流水ID")
    private Long divideFlowId;

    @ApiModelProperty("如果是失败, 失败原因是什么")
    private String msg;

    public static CreateDivideRespVO success() {
        CreateDivideRespVO respVO = new CreateDivideRespVO();
        respVO.setSuccess(true);
        return respVO;
    }

    public static CreateDivideRespVO fail(String msg) {
        CreateDivideRespVO respVO = new CreateDivideRespVO();
        respVO.setSuccess(false);
        respVO.setMsg(msg);
        return respVO;
    }
}
