package com.zksr.member.api.complain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2024/5/6 16:09
 */
@Data
public class MemComplainDTO extends PageParam {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @Excel(name = "投诉信息ID")
    private Long complainId;


    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;


    @Excel(name = "用户ID")
    @ApiModelProperty(value = "用户ID")
    private Long memberId;

    @Excel(name = "用户名称")
    @ApiModelProperty(value = "用户名称")
    private String username;

    @Excel(name = "用户手机号")
    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @Excel(name = "所在门店ID")
    @ApiModelProperty(value = "所在门店ID")
    private Long branchId;

    @Excel(name = "投诉说明")
    @ApiModelProperty(value = "投诉说明")
    private String complainContent;

    @Excel(name = "投诉对象类型", readConverterExp = "投诉对象字典(1.商品2.物流3.业务员4.司机)")
    @ApiModelProperty(value = "投诉对象字典(1.商品2.物流3.业务员4.司机)")
    private Integer complainType;

    @Excel(name = "投诉凭证")
    @ApiModelProperty(value = "投诉凭证")
    private String complainImage;

    @Excel(name = "投诉对象名称")
    @ApiModelProperty(value = "投诉对象名称")
    private String targetUsername;

    @Excel(name = "投诉对象手机号(非必填)")
    @ApiModelProperty(value = "投诉对象手机号(非必填)")
    private String targetPhone;

    @Excel(name = "投诉处理回复")
    @ApiModelProperty(value = "投诉处理回复")
    private String complainReply;

    @Excel(name = "投诉方业务ID")
    @ApiModelProperty(value = "投诉方业务ID")
    private Long businessId;

    @Excel(name = "处理时间")
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime processTime;

    @Excel(name = "处理人")
    @ApiModelProperty(value = "处理人")
    private String processBy;

    @Excel(name = "状态", readConverterExp = "状态,0-待处理,1-已回复")
    @ApiModelProperty(value = "状态,0-待处理,1-已回复")
    private String status;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;


    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "创建开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createBinTime;

    @Excel(name = "创建结束时间")
    @ApiModelProperty
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createEndTime;

    @Excel(name = "处理开始时间")
    @ApiModelProperty
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date processBinTime;

    @Excel(name = "处理结束时间")
    @ApiModelProperty
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date processEndTime;


}
