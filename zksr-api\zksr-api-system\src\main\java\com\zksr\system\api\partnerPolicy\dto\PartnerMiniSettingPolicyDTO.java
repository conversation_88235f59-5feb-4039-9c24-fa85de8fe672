package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 平台商小程序设置项配置类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PartnerMiniSettingPolicyDTO {

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    @ApiModelProperty("小程序用户注册类型 (0：简易注册  1：常规注册)")
    private String registerType;

    @ApiModelProperty("是否开启微信b端商家助手认证 (1 开启, 0 不开启)")
    private String openWechatMerchantAuth;

    @ApiModelProperty("强制微信b端商家助手认证 (1 开启, 0 不开启)")
    private String forceWechatMerchantAuth;

    @ApiModelProperty("商城是否开启二级分类展示 (1 开启, 0 不开启)")
    private String openLevelTwoCategoryShow;

    @ApiModelProperty("平台本地商品售后类型")
    private String localItemAfterType;

    @ApiModelProperty("平台本地商品售后审核类型")
    private String localItemAfterApproveType;

    @ApiModelProperty("是否展示佣金 0-关闭 1-开启")
    private String isShowCommission;

    @ApiModelProperty("平台账号默认密码")
    private String defalutPassword;

    @ApiModelProperty("库存显示模式")
    private String stockShowMode;

    @ApiModelProperty("库存显示临界值")
    private String stockShowThreshold;
}
