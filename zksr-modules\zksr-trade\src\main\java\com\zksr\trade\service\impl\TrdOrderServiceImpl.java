package com.zksr.trade.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.api.pay.PayFlowApi;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.*;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.*;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsStoreProduct;
import com.zksr.common.elasticsearch.model.dto.StoreProductDTO;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisCacheService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.command.vo.CommandAddOrderVO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemMemberInvoiceRespDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierClass.SupplierClassApi;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.OrderSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SupplierOrderVO;
import com.zksr.trade.api.after.dto.BranchSkuMergeAfterResDTO;
import com.zksr.trade.api.after.dto.OrderAfterDtlDTO;
import com.zksr.trade.api.after.dto.OrderAfterResDTO;
import com.zksr.trade.api.after.dto.OrderMergeAfterResDTO;
import com.zksr.trade.api.after.vo.BranchSkuMergeAfterReqVO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.hdfk.HdfkApi;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO;
import com.zksr.trade.controller.order.vo.*;
import com.zksr.trade.controller.rating.vo.TrdDriverRatingRespVO;
import com.zksr.trade.convert.order.TrdOrderConvert;
import com.zksr.trade.convert.order.TrdSettleConvert;
import com.zksr.trade.convert.orderDiscount.TrdOrderDiscountDtlConvert;
import com.zksr.trade.convert.orderExpress.TrdOrderExpressConvert;
import com.zksr.trade.domain.*;
import com.zksr.trade.mapper.*;
import com.zksr.trade.mq.TradeMqProducer;
import com.zksr.trade.print.vo.PrintSupplierOrderVO;
import com.zksr.trade.service.*;
import com.zksr.trade.service.handler.ITrdOrderHandlerService;
import com.zksr.trade.service.handler.payWay.ITrdOrderPayWayHandlerService;
import com.zksr.trade.service.price.ITrdPriceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
public class TrdOrderServiceImpl implements ITrdOrderService {
    private static final Logger log = LoggerFactory.getLogger(TrdOrderServiceImpl.class);

    @Autowired
    private TrdOrderMapper trdOrderMapper;
    @Autowired
    private TrdSupplierOrderMapper trdSupplierOrderMapper;
    @Autowired
    private TrdSupplierOrderDtlMapper trdSupplierOrderDtlMapper;
    @Autowired
    private TrdSupplierOrderSettleMapper supplierOrderSettleMapper;
    @Autowired
    private TrdSettleMapper trdSettleMapper;
    @Autowired
    private TrdSupplierAfterDtlMapper trdSupplierAfterDtlMapper;

    @Autowired
    private TrdCacheService trdCacheService;
    @Autowired
    private List<ITrdOrderHandlerService> trdOrderHandlerServices;
    @Autowired
    private List<ITrdOrderPayWayHandlerService> trdOrderPayWayHandlerServices;
    @Autowired
    private TrdOrderLogMapper orderLogMapper;
    @Autowired
    private TrdOrderExpressMapper trdOrderExpressMapper;
    @Autowired
    private SupplierApi supplierApi;
    @Autowired
    private SpuApi spuApi;
    @Autowired
    private AccountApi accountApi;
    @Autowired
    private CouponApi couponApi;
    @Autowired
    private AreaCityApi areaCityApi;
    @Autowired
    private EsProductService esProductService;
    @Autowired
    private TrdOrderDiscountDtlMapper trdOrderDiscountDtlMapper;
    @Autowired
    private TradeMqProducer tradeMqProducer;
    @Autowired
    private TrdExpressStatusMapper trdExpressStatusMapper;
    @Autowired
    private TrdAfterMapper trdAfterMapper;
    @Autowired
    private ITrdPriceService trdPriceService;
    @Resource
    private OpensourceApi opensourceApi;
    @Autowired
    private ITrdSettleService trdSettleService;
    @Resource
    private PartnerPolicyApi partnerPolicyApi;

    @Resource
    private RemoteUserService remoteUserService;

    @Autowired
    private ITrdDriverRatingService trdDriverRatingService;
    @Resource
    private BranchApi branchApi;
    @Resource
    private DcApi dcApi;
    @Resource
    private ChannelApi channelApi;
    @Resource
    private ColonelApi colonelApi;

    @Resource
    private AreaApi areaApi;
    @Resource
    private BrandApi brandApi;
    @Resource
    private CatgoryApi catgoryApi;
    @Resource
    private TrdSupplierOrderSettleServiceImpl trdSupplierOrderSettleService;
    @Resource
    private AreaClassApi areaClassApi;
    @Resource
    private AreaItemApi areaItemApi;
    @Resource
    private SupplierItemApi supplierItemApi;
    @Resource
    private SaleClassApi saleClassApi;
    @Resource
    private PayFlowApi payFlowApi;
    @Autowired
    private SupplierClassApi supplierClassApi;

    @Autowired
    private PayApi payApi;

    @Autowired
    private RedisCacheService redisCacheService;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private MemberApi memberApi;

    @Resource
    private ITrdSupplierOrderInvoiceService supplierOrderInvoiceService;

    @Resource
    private TrdSupplierOrderInvoiceMapper trdSupplierOrderInvoiceMapper;

    /**
     * 新增订单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertTrdOrder(TrdOrderSaveReqVO createReqVO) {
        // 插入
        TrdOrder trdOrder = HutoolBeanUtils.toBean(createReqVO, TrdOrder.class);
        trdOrderMapper.insert(trdOrder);
        // 返回
        return trdOrder.getOrderId();
    }

    /**
     * 修改订单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateTrdOrder(TrdOrderSaveReqVO updateReqVO) {
        trdOrderMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, TrdOrder.class));
    }

    /**
     * 删除订单
     *
     * @param orderId 订单id
     */
    @Override
    public void deleteTrdOrder(Long orderId) {
        // 删除
        trdOrderMapper.deleteById(orderId);
    }

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单主键
     * @return 结果
     */
    @Override
    public void deleteTrdOrderByOrderIds(Long[] orderIds) {
        for (Long orderId : orderIds) {
            this.deleteTrdOrder(orderId);
        }
    }

    /**
     * 获得订单
     *
     * @param orderId 订单id
     * @return 订单
     */
    @Override
    public TrdOrder getTrdOrder(Long orderId) {
        return trdOrderMapper.selectById(orderId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<TrdOrder> getTrdOrderPage(TrdOrderPageReqVO pageReqVO) {
        return trdOrderMapper.selectPage(pageReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    //!@订单 - 创建 - 5、后置处理 - 3、保存订单（父单 + 子单 + 子单明细 + 子单明细结算 + 日志流水 + 优惠）
    public TrdOrderResDto saveOrder(RemoteSaveOrderVO saveVo) {
        // 获取运营商订单设置
        OrderSettingPolicyDTO dcOrderSetting = trdCacheService.getOrderSettingPolicyInfo(saveVo.getOrderSaveVo().getDcId());
        if (ToolUtil.isEmpty(dcOrderSetting)) {
            dcOrderSetting = new OrderSettingPolicyDTO();
        }
        /**
         * 1、保存主订单
         */
        TrdOrder trdOrder = HutoolBeanUtils.toBean(saveVo.getOrderSaveVo(), TrdOrder.class);
        trdOrderMapper.insert(trdOrder);

        /**
         * 2、保存入驻商订单
         */
        List<TrdSupplierOrder> supplierOrders = saveVo.getSupplierOrderSaveVOs().stream().map(supplierSaveVo -> {
            TrdSupplierOrder sOrderVo = HutoolBeanUtils.toBean(supplierSaveVo, TrdSupplierOrder.class);
            sOrderVo.setOrderId(trdOrder.getOrderId());
            sOrderVo.setSubCancelAmt(BigDecimal.ZERO);
            sOrderVo.setSubCancelQty(BigDecimal.ZERO);
            sOrderVo.setDeliveryState(SupplierOrderStatusEnum.UNPAID.getCode());
            return sOrderVo;
        }).collect(Collectors.toList());
        trdSupplierOrderMapper.insertBatch(supplierOrders);

        // 根据入驻商ID和分类ID获取三级管理分类售后配置
        List<SupplierClassRespDTO> supplierClassRespDTOS = supplierClassApi.getSupplierClassAfterConfig(
                saveVo.getSupplierOrderDtlSaveVOS().stream().map(TrdSupplierOrderDtlSaveVO::getSupplierId).distinct().collect(Collectors.toList()),
                saveVo.getSupplierOrderDtlSaveVOS().stream().map(TrdSupplierOrderDtlSaveVO::getCategoryId).distinct().collect(Collectors.toList())
        ).getCheckedData();
        Map<String, SupplierClassRespDTO> supplierClassMap = supplierClassRespDTOS.stream()
                .filter(ToolUtil.distinctByKey(SupplierClassRespDTO::getDistinctKey)) // 过滤去重
                .collect(
                        Collectors.toMap(x ->
                                StringUtils.format("{}_{}", x.getSupplierId(), x.getCatgoryId()), Function.identity()
                        )
                );
        /**
         *  3、保存入驻商订单明细
         */
        Map<String, TrdSupplierOrder> supplierVoMap = supplierOrders.stream().collect(Collectors.toMap(TrdSupplierOrder::getSupplierOrderNo, TrdSupplierOrder -> TrdSupplierOrder));
        OrderSettingPolicyDTO finalDcOrderSetting = dcOrderSetting;
        List<TrdSupplierOrderDtl> dtlList = saveVo.getSupplierOrderDtlSaveVOS().stream().map(dtl -> {
            TrdSupplierOrderDtl supplierDtlVo = HutoolBeanUtils.toBean(dtl, TrdSupplierOrderDtl.class);
            TrdSupplierOrder supplierOrder = supplierVoMap.get(supplierDtlVo.getSupplierOrderNo());
            supplierDtlVo.setSupplierOrderId(supplierOrder.getSupplierOrderId());
            supplierDtlVo.setOrderId(trdOrder.getOrderId());
            supplierDtlVo.setCancelQty(BigDecimal.ZERO); // 发货前取消数量
            supplierDtlVo.setCancelAmt(BigDecimal.ZERO);
            supplierDtlVo.setSendQty(BigDecimal.ZERO); // 发货数量

            String supplierClassKey = StringUtils.format("{}_{}", dtl.getSupplierId(), dtl.getCategoryId());
            SupplierClassRespDTO supplierClassRespDTO = supplierClassMap.get(supplierClassKey);
            supplierDtlVo.setIsAfterSales(ToolUtil.isNotEmpty(supplierClassRespDTO) ?  supplierClassRespDTO.getIsAfterSales() : 1);
            supplierDtlVo.setAfterSalesTime(
                    ToolUtil.isNotEmpty(supplierClassRespDTO) ? supplierClassRespDTO.getAfterSalesTimeMinute(finalDcOrderSetting.getOrderFinishDateMinute()) : finalDcOrderSetting.getOrderFinishDateMinute()
            );
            return supplierDtlVo;
        }).collect(Collectors.toList());
        trdSupplierOrderDtlMapper.insertBatch(dtlList);

        /**
         * 4、保存入驻商订单结算明细
         */
        Map<String, TrdSupplierOrderDtl> supplierDtlVoMap = dtlList.stream().collect(Collectors.toMap(TrdSupplierOrderDtl::getSupplierOrderDtlNo, TrdSupplierOrderDtl -> TrdSupplierOrderDtl));
        List<TrdSupplierOrderSettle> supplierOrderSettles = saveVo.getSupplierOrderSettleSaveVOS().stream().map(dtlSettle -> {
            TrdSupplierOrderSettle supplierOrderSettle = HutoolBeanUtils.toBean(dtlSettle, TrdSupplierOrderSettle.class);
            TrdSupplierOrderDtl trdSupplierOrderDtl = supplierDtlVoMap.get(supplierOrderSettle.getSupplierOrderDtlNo());
            TrdSupplierOrder supplierOrder = supplierVoMap.get(dtlSettle.getSupplierOrderNo());
            supplierOrderSettle.setSupplierOrderDtlId(trdSupplierOrderDtl.getSupplierOrderDtlId());
            supplierOrderSettle.setSupplierOrderId(supplierOrder.getSupplierOrderId());
            supplierOrderSettle.setOrderId(trdOrder.getOrderId());
            dtlSettle.setSupplierOrderId(supplierOrder.getSupplierOrderId());

            return supplierOrderSettle;
        }).collect(Collectors.toList());
        supplierOrderSettleMapper.insertBatch(supplierOrderSettles);

        /**
         * 5、保存订单明细日志流水
         */
        List<TrdOrderLog> orderLogs = saveVo.getOrderLogSaveVOS().stream().map(orderSaveLog -> {
            TrdOrderLog orderLog = HutoolBeanUtils.toBean(orderSaveLog, TrdOrderLog.class);
            TrdSupplierOrderDtl trdSupplierOrderDtl = supplierDtlVoMap.get(orderLog.getSupplierOrderDtlNo());
            orderLog.setSupplierOrderDtlId(trdSupplierOrderDtl.getSupplierOrderDtlId());
            return orderLog;
        }).collect(Collectors.toList());
        orderLogMapper.insertBatch(orderLogs);

        /**
         *  保存订单优惠信息
         */
        if (ToolUtil.isNotEmpty(saveVo.getOrderDiscountDtlSaveVOS()) && saveVo.getOrderDiscountDtlSaveVOS().size() > 0) {
           List<TrdOrderDiscountDtl> trdOrderDiscountDtls= saveVo.getOrderDiscountDtlSaveVOS().stream().map(orderDiscount -> {
                TrdOrderDiscountDtl orderDiscountDtl = TrdOrderDiscountDtlConvert.INSTANCE.convert(orderDiscount);
                orderDiscountDtl.setOrderId(trdOrder.getOrderId());
                if (ToolUtil.isNotEmpty(orderDiscountDtl.getSupplierOrderDtlNo())) {
                    orderDiscountDtl.setSupplierOrderDtlId(supplierDtlVoMap.get(orderDiscountDtl.getSupplierOrderDtlNo()).getSupplierOrderDtlId());
                }
                if (ToolUtil.isNotEmpty(orderDiscount.getSupplierOrderNo())) {
                    orderDiscountDtl.setSupplierOrderId(supplierVoMap.get(orderDiscount.getSupplierOrderNo()).getSupplierOrderId());
                }
                return orderDiscountDtl;
            }).collect(Collectors.toList());
            trdOrderDiscountDtlMapper.insertBatch(trdOrderDiscountDtls);
        }

        //保存用户发票信息
        if(null != trdOrder.getMemberInvoiceId()){
            MemMemberInvoiceRespDTO memMemberInvoiceRespDTO = queryMemMemberInvoice(trdOrder);
            if(null != memMemberInvoiceRespDTO){
                List<TrdSupplierOrderInvoice> orderInvoiceList = supplierOrders.stream().map(supplierOrder -> {
                    TrdSupplierOrderInvoice invoice = TrdOrderConvert.INSTANCE.convert2TrdSupplierOrderInvoice(memMemberInvoiceRespDTO);
                    invoice.setSupplierOrderId(supplierOrder.getSupplierOrderId());
                    invoice.setSupplierOrderNo(supplierOrder.getSupplierOrderNo());
                    invoice.setId(null);
                    return invoice;
                }).collect(Collectors.toList());

                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderInvoiceList)){
                    trdSupplierOrderInvoiceMapper.insertBatch(orderInvoiceList);
                }
            }else {
                log.error("没有找到对应的用户发票信息[{}]",trdOrder.getMemberInvoiceId());
            }
        }

//        /**
//         *如果当前支付方式为货到付款，则默认直接进入订单回调接口执行逻辑
//         */
//        if (ToolUtil.isNotEmpty(trdOrder.getPayWay()) && trdOrder.getPayWay().equals(OrderPayWayEnum.HDFK.getPayWay())) {
//            TrdPayOrderPageVO pageVo = new TrdPayOrderPageVO();
//            pageVo.setOrderNo(trdOrder.getOrderNo());
//            pageVo.setPayWay(OrderPayWayEnum.HDFK.getPayWay());
//            pageVo.setSuccessTime(DateUtils.getNowDate());
//            // 获取支付平台
//            PayConfigDTO payConfigDTO = trdCacheService.getPayConfigDTO(saveVo.getOrderSaveVo().getSysCode());
//            pageVo.setPayPlatform(payConfigDTO.getStoreOrderPayPlatform());  // 订单支付平台
//
//            // 发送货到付款订单支付成功消息 执行订单支付成功回调流程
//            tradeMqProducer.sendOrderHdfkSuccessEvent(pageVo);
//        }

        // 创建冻结入驻商分成利润金额流水 并 执行冻结入驻商分成利润金额流水   TODO 应入驻商充值方案弃用，故此代码注释
//        List<AccAccountFlowDTO> accountFreezeFlows = createAccountFreezeFlow(supplierOrders, saveVo.getSupplierOrderSettleSaveVOS(), trdOrder, StatusConstants.FREEZE_FLOW);
//        if (ToolUtil.isNotEmpty(accountFreezeFlows) && !accountFreezeFlows.isEmpty()) {
//            accountApi.saveAccountFlowAndProcess(accountFreezeFlows).checkError();
//        }


        TrdOrderResDto orderResDto = HutoolBeanUtils.toBean(trdOrder, TrdOrderResDto.class);
        orderResDto.setExpirePayTime(DateUtils.getDateAddSecond(trdOrder.getCreateTime(), dcOrderSetting.getOrderExpiryDateSecond()));
        return orderResDto;
    }


    /**
     * 查询发票信息
     * @param trdOrder
     * @return
     */
    private MemMemberInvoiceRespDTO queryMemMemberInvoice(TrdOrder trdOrder){
        MemMemberInvoiceRespDTO memMemberInvoiceRespDTO = null;
        CommonResult<MemMemberInvoiceRespDTO> result = null;
        try {
            result = memberApi.getMemberInvoiceById(trdOrder.getMemberInvoiceId());
            if(null != result){
                memMemberInvoiceRespDTO = result.getCheckedData();
            }

        } catch (Exception e) {
            log.error("{}查询用户发票失败[{}],",trdOrder.getOrderNo(),trdOrder.getMemberInvoiceId(),e);
            throw new ServiceException("查询用户发票失败,"+e.getMessage());
        } finally {
            log.info(" 查询用户发票信息req={},resp={}", trdOrder.getMemberInvoiceId(), JsonUtils.toJsonString(result));
        }

        return memMemberInvoiceRespDTO;
    }

    private void saveOrderInvoice(){

    }
    @Override
    public OrderPayInfoRespDTO getSupplierPayInfo(TrdSupplierPageVO pageVo) {
        // 查询入驻商订单信息
        List<TrdSupplierResDto> payInfoList = trdSupplierOrderMapper.getSupplierOrderPayInfo(pageVo);
        if (ToolUtil.isEmpty(payInfoList) || payInfoList.isEmpty()) {
            throw exception(TRD_ORDER_NOT_EXISTS);
        }
        List<Long> supplierOrderIds = payInfoList.stream().map(TrdSupplierResDto::getSupplierOrderId).collect(Collectors.toList());
        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectBySpuNameAndTotalNum(supplierOrderIds);


        // 生成订单结算表信息
        List<TrdSettle> settles = trdSettleService.createBatchSettle(trdOrderMapper.getOrderByOrderNo(TrdOrderPageReqVO.builder()
                .orderNo(pageVo.getOrderNo())
                .build()));

        OrderPayInfoRespDTO respDTO = new OrderPayInfoRespDTO();
        // 订单应支付金额总金额 & 储值赠送余额承担金额
        respDTO.setPayAmt(payInfoList.get(NumberPool.INT_ZERO).getPayAmt())
                .setWalletGiveAmt(payInfoList.get(NumberPool.INT_ZERO).getCzGivePayAmt())
                .setOrderId(payInfoList.get(NumberPool.INT_ZERO).getOrderId())
                .setPlatform(pageVo.getStoreOrderPayPlatform())
                .setSupplierList(new ArrayList<>())
                .setDistributionMode(payInfoList.get(NumberPool.INT_ZERO).getDistributionMode())
        ;

        if (!supplierOrderDtls.isEmpty()) {
            Map<Long, List<TrdSupplierOrderDtl>> supplierItemMap = supplierOrderDtls.stream().collect(Collectors.groupingBy(TrdSupplierOrderDtl::getSupplierOrderId));
            // 支付获取订单结算分账数据- 订单支付结算分账数据处理
            //!@支付 - 提交支付订单 - 2、校验订单 - 2、订单支付结算分账数据处理 （orderSettl）【1个入驻商 + 平台 + 运营 + 负责人 等 分账信息】
            getPayWayHandlerService(pageVo.getStoreOrderPayPlatform(), OrderPayWayEnum.ONLINE.getPayWay()).orderSettleAccountInfo(payInfoList, supplierItemMap, pageVo, settles, respDTO);
        }
        return respDTO;
    }

    @Resource
    private HdfkApi hdfkApi;


    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#pageVo.orderNo", tryLock = true)
    @Transactional(rollbackFor = Exception.class)
    public void orderPaySuccessCallback(TrdPayOrderPageVO pageVo) {
        log.info("进入Trade模块支付回调成功{}", pageVo);
        Long startTime = System.currentTimeMillis();
        log.info("订单{}进入Trade模块支付回调开始时间{}", pageVo.getOrderNo(), startTime);

        TrdOrderPageReqVO orderVo = new TrdOrderPageReqVO();
        orderVo.setOrderNo(pageVo.getOrderNo());
        TrdOrder order = trdOrderMapper.getOrderByOrderNo(orderVo);
        if (StringUtils.isNull(order)){
            TrdSupplierOrder supplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(pageVo.getOrderNo());
            if(StringUtils.isNull(supplierOrder)){
                throw exception(TRD_ORDER_NOT_EXISTS);
            }
            ITrdOrderService trdOrderService = SpringUtils.getBean(ITrdOrderService.class);
            trdOrderService.orderPaySuccessCallbackBySupplierOrder(supplierOrder, pageVo);
            return;
//            orderVo.setOrderNo(supplierOrder.getOrderNo());
//            order = trdOrderMapper.getOrderByOrderNo(orderVo);
//
//            if(StringUtils.isNull(order)){
//                throw exception(TRD_ORDER_NOT_EXISTS);
//            }
//            supplierOrder.setPayState(PayStateEnum.PAY_ALREADY_ONLINE.getCode());
//            supplierOrder.setDeliveryState(SupplierOrderStatusEnum.WAIT_DELIVER.getCode());
//            trdSupplierOrderMapper.updateById(supplierOrder);
//
//            AtomicBoolean hasNoPay = new AtomicBoolean(false);
//            AtomicReference<String> hasNoPayNo = new AtomicReference<>("");
//            // 判断是否有未支付的订单
//            List<TrdSupplierOrder> supplierOrderList = trdSupplierOrderMapper.selectListByOrderId(order.getOrderId());
//            supplierOrderList.forEach(supplierOrderItem -> {
//                if(PayStateEnum.PAY_NOT_ONLINE.getCode().equals(supplierOrderItem.getPayState())){
//                    hasNoPay.set(true);
//                    hasNoPayNo.set(supplierOrderItem.getSupplierOrderNo());
//                }
//            });
//
//            if(hasNoPay.get()){
//                log.warn("回调支付单{},存在未支付的单{}", pageVo.getOrderNo(), hasNoPayNo.get());
//                return;
//            }
        }

        // 订单支付状态
        Integer paySate = order.getPayState();
        if (PayStateEnum.isOrderPaid(paySate)){
            log.info("订单{}已支付回调，重复回调操作!", order.getOrderNo());
            return;
        }

        List<TrdSupplierOrder> supplierOrderList = trdSupplierOrderMapper.selectListByOrderId(order.getOrderId());
        if(StringUtils.isNull(supplierOrderList)){
            throw exception(TRD_ORDER_NOT_EXISTS);
        }
        supplierOrderList.forEach(supplierOrderItem -> {
            supplierOrderItem.setDeliveryState(SupplierOrderStatusEnum.WAIT_DELIVER.getCode());
            supplierOrderItem.setUpdateTime(DateUtils.getNowDate());
            trdSupplierOrderMapper.updateById(supplierOrderItem);
        });


        TrdOrder finalOrder = order;
        trdOrderHandlerServices.forEach(handler -> {
            handler.orderPay(pageVo, finalOrder);
        });

        // 发送订单支付成功处理消息（用于处理订单支付完成后的业务逻辑）
        tradeMqProducer.sendOrderPayUpdateSuccessEvent(order);
        // 发送 订单加单通知
        sendAddOrderCommand(order);

        // 订单取消收到支付回调
        // 处理订单库存数量扣减及 订单优惠信息扣减
        if (PayStateEnum.isOrderCancel(paySate)) {
            log.info("订单{}已取消，但收到支付成功回调，进行相应处理", order.getOrderNo());
            try {
                trdOrderHandlerServices.forEach(handler -> {
                    handler.handleCancelledOrderPaymentSuccess(finalOrder);
                });
            } catch (Exception e) {
                log.error("订单{}已取消，但收到支付成功回调，执行已售库存和促销库存扣减失败！", order.getOrderNo(), e);
            }
        }
        Long endTime = System.currentTimeMillis();
        log.info("订单{}进入Trade模块支付回调结束时间{}", pageVo.getOrderNo(), endTime);
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#supplierOrder.orderNo", tryLock = true)
    public void orderPaySuccessCallbackBySupplierOrder(TrdSupplierOrder supplierOrder, TrdPayOrderPageVO pageVo){
        TrdOrderPageReqVO orderVo = new TrdOrderPageReqVO();
        orderVo.setOrderNo(supplierOrder.getOrderNo());
        orderVo.setOrderNo(supplierOrder.getOrderNo());
        TrdOrder order = trdOrderMapper.getOrderByOrderNo(orderVo);

        if(StringUtils.isNull(order)){
            throw exception(TRD_ORDER_NOT_EXISTS);
        }
        supplierOrder.setPayState(PayStateEnum.PAY_ALREADY_ONLINE.getCode());
        supplierOrder.setDeliveryState(SupplierOrderStatusEnum.WAIT_DELIVER.getCode());
        supplierOrder.setUpdateTime(DateUtils.getNowDate());
        trdSupplierOrderMapper.updateById(supplierOrder);

        AtomicBoolean hasNoPay = new AtomicBoolean(false);
        AtomicReference<String> hasNoPayNo = new AtomicReference<>("");
        // 判断是否有未支付的订单
        List<TrdSupplierOrder> supplierOrderList = trdSupplierOrderMapper.selectListByOrderId(order.getOrderId());
        supplierOrderList.forEach(supplierOrderItem -> {
            if(PayStateEnum.PAY_NOT_ONLINE.getCode().equals(supplierOrderItem.getPayState())){
                hasNoPay.set(true);
                hasNoPayNo.set(supplierOrderItem.getSupplierOrderNo());
            }
        });

        if(hasNoPay.get()){
            log.warn("回调支付单{},存在未支付的单{}", orderVo.getOrderNo(), hasNoPayNo.get());
            return;
        }

        // 订单支付状态
        Integer paySate = order.getPayState();
        if (PayStateEnum.isOrderPaid(paySate)){
            log.info("订单{}已支付回调，重复回调操作!", order.getOrderNo());
            return;
        }

        TrdOrder finalOrder = order;
        trdOrderHandlerServices.forEach(handler -> {
            handler.orderPay(pageVo, finalOrder);
        });

        // 发送订单支付成功处理消息（用于处理订单支付完成后的业务逻辑）
        tradeMqProducer.sendOrderPayUpdateSuccessEvent(order);
        // 发送 订单加单通知
        sendAddOrderCommand(order);

        // 订单取消收到支付回调
        // 处理订单库存数量扣减及 订单优惠信息扣减
        if (PayStateEnum.isOrderCancel(paySate)) {
            log.info("订单{}已取消，但收到支付成功回调，进行相应处理", order.getOrderNo());
            try {
                trdOrderHandlerServices.forEach(handler -> {
                    handler.handleCancelledOrderPaymentSuccess(finalOrder);
                });
            } catch (Exception e) {
                log.error("订单{}已取消，但收到支付成功回调，执行已售库存和促销库存扣减失败！", order.getOrderNo(), e);
            }
        }


        Long endTime = System.currentTimeMillis();
        log.info("订单{}进入Trade模块支付回调结束时间{}", pageVo.getOrderNo(), endTime);

    }


    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#trdOrder.orderNo", tryLock = true)
    @Transactional(rollbackFor = Exception.class)
    public void afterOrderPayUpdateSuccess(TrdOrder trdOrder) {
        Long afterOrderPayTime = System.currentTimeMillis();
        log.info("订单{}进入Trade模块支付回调更新订单之后其他操作开始时间{}", trdOrder.getOrderNo(), afterOrderPayTime);
        trdOrderHandlerServices.forEach(handler -> {
            handler.afterOrderPay(trdOrder);
        });
        Long afterOrderPayEndTime = System.currentTimeMillis();
        log.info("订单{}进入Trade模块支付回调更新订单之后其他操作结束时间{}", trdOrder.getOrderNo(), afterOrderPayEndTime);
    }

    /**
     * @Description: 分页获取订单数据 （小程序使用）
     * @Author: chenmingqing
     * @Date: 2025/3/26 16:40
     */
    @Override
    public CommonResult<PageResult<TrdOrderMiniHeadRespDTO>> pageOrderList(TrdOrderPageReqDTO orderPageReqVO) {
        com.github.pagehelper.Page<TrdOrderMiniHeadRespDTO> page = PageUtils.startPage(orderPageReqVO);
        //先判断是否是查询全部数据
        PageResult<TrdOrderMiniHeadRespDTO> result = new PageResult<>();
//        orderPageReqVO.setPageNo((orderPageReqVO.getPageNo() - 1) * orderPageReqVO.getPageSize());
//        Long total = trdOrderMapper.selectPageAllNewCount(orderPageReqVO);
//        if (total <= NumberPool.LONG_ZERO) { // 没有查询到数据，直接返回
//            result.setList(new ArrayList<>());
//            result.setTotal(0L);
//            return result;
//        }
//        List<TrdOrderMiniRespDTO> resultData =  trdOrderMapper.selectPageAll(orderPageReqVO);
        List<TrdOrderMiniHeadRespDTO> resultData =  trdOrderMapper.selectPageAllNew3(orderPageReqVO);

        if(org.apache.commons.collections4.CollectionUtils.isEmpty(resultData)){
            PageResult<TrdOrderMiniHeadRespDTO> rs = new PageResult<>(resultData, page.getTotal());
            return success(rs);
        }

        List<TrdOrderMiniHeadRespDTO> returnRs = new ArrayList<>();
        for(TrdOrderMiniHeadRespDTO dto : resultData){
            TrdOrderMiniHeadRespDTO detail = getTrdOrderMiniRespDTOAssembleResult(dto, orderPageReqVO);
            returnRs.add(detail);
        }
//        resultData.forEach(this::getTrdOrderMiniRespDTOAssembleResult);

        PageResult<TrdOrderMiniHeadRespDTO> rs = new PageResult<>(returnRs, page.getTotal());
//        return success(trdOrderServiceImpl.pageOrderList(orderPageReqVO));
        return success(rs);

//        result.setList(resultData);
//        result.setTotal(resultData.get);
//        return resultData;
    }

    @Override
    public PageResult<TrdOrderRespDTO> pageOrderListNew(TrdOrderPageReqDTO orderPageReqVO) {
        //先判断是否是查询全部数据
        PageResult<TrdOrderRespDTO> result = new PageResult<>();
        orderPageReqVO.setPageNo((orderPageReqVO.getPageNo() - 1) * orderPageReqVO.getPageSize());
        Long total = trdOrderMapper.selectPageAllNewCount(orderPageReqVO);
        if (total <= NumberPool.LONG_ZERO) { // 没有查询到数据，直接返回
            result.setList(new ArrayList<>());
            result.setTotal(0L);
            return result;
        }
        List<TrdOrderRespDTO> resultData =  trdOrderMapper.selectPageAllNew(orderPageReqVO);
        resultData.forEach(this::getTrdOrderRespDTOAssembleResult);
        result.setList(resultData);
        result.setTotal(total);
        return result;
    }


    @Override
    public TrdOrderRespDTO getOrderInfo(TrdOrderPageReqDTO orderPageReqVO) {
        orderPageReqVO.setPageNo(NumberPool.INT_ZERO);
        List<TrdOrderRespDTO> resultData =  trdOrderMapper.selectPageAllNew(orderPageReqVO);
        if (ToolUtil.isEmpty(resultData)) {
            return new TrdOrderRespDTO();
        }

        resultData.forEach(order -> {
            // 组装订单数据
            getTrdOrderRespDTOAssembleResult(order);

            order.getSupplierOrderList().forEach(tso -> {
                // 查询根据入驻商单查询订单优惠信息表
                tso.setCouponTemplateList(getSupplierOrderGiftCouponInfoBySupplierOrderId(tso.getSupplierOrderId()));

                // 查询入驻商
                SupplierOtherSettingPolicyDTO policyDTO = trdCacheService.getPartnerSupplierOtherSettingPolicy(tso.getSupplierId());
                if (Objects.nonNull(policyDTO)) {
                    tso.setProductDistributionLabel(policyDTO.getProductDistributionLabel());
                    tso.setSwitchWalletPay(policyDTO.getSwitchWalletPay());
                }
            });
        });

        // 订单配置项
        OrderSettingPolicyDTO settingPolicyInfo = trdCacheService.getOrderSettingPolicyInfo(resultData.get(0).getDcId());
        if (Objects.nonNull(settingPolicyInfo)) {
            resultData.forEach(item -> TrdOrderConvert.INSTANCE.buildSetOrderSetting(item, settingPolicyInfo));
        }
        return resultData.get(NumberPool.INT_ZERO);
    }

    /**
     * @Description: 获取入驻商首页统计数据
     * @Author: liuxingyu
     * @Date: 2024/4/3 9:11
     */
    @Override
    public HomePageRespVO getHomePage(HomePageReqVO homePageReqVo) {
        String startDate = "";
        String endDate = "";
        if (ObjectUtil.isNotNull(homePageReqVo.getCountStartDate())) {
            startDate = DateUtils.parseDateToStr("yyyy-MM-dd", homePageReqVo.getCountStartDate());
            startDate = startDate + " 00:00:00";
        }
        if (ObjectUtil.isNotNull(homePageReqVo.getCountEndDate())) {
            endDate = DateUtils.parseDateToStr("yyyy-MM-dd", homePageReqVo.getCountEndDate());
            endDate = endDate + " 23:59:59";
        }
        HomePageRespVO result = trdSupplierOrderMapper.selectHomePageMonth(startDate, endDate, SecurityUtils.getSupplierId());
        HomePageRespVO result2 = trdSupplierOrderMapper.selectHomePageDay(SecurityUtils.getSupplierId());
        TrdOrderConvert.INSTANCE.buildSetSupplierPageHomeResp(result, result2);
        return result;
    }

    /**
     * @Description: 获取入驻商小程序订单分页列表
     * @Author: liuxingyu
     * @Date: 2024/4/8 11:18
     */
    @Override
    public PageResult<MiniProgramRespVO> getMerchantMiniProgramOrderPageList(TrdOrderPageReqDTO trdOrderPageReqDTO) {
        Page<TrdOrderPageReqDTO> page = new Page<>(trdOrderPageReqDTO.getPageNo(), trdOrderPageReqDTO.getPageSize());
        Page<MiniProgramRespVO> orderPage = trdOrderMapper.getMerchantMiniProgramOrderPageList(trdOrderPageReqDTO, SecurityUtils.getSupplierId(), page);
        if (ObjectUtil.isEmpty(orderPage.getRecords())) {
            return new PageResult<>(orderPage.getRecords(), orderPage.getTotal());
        }
        List<MiniProgramRespVO> records = orderPage.getRecords();
        records = records
                .stream()
                .peek(x -> {
                    BranchDTO branchDTO = trdCacheService.getBranchDTO(x.getBranchId());
                    x.setBranchName(
                            ObjectUtil.isNotNull(branchDTO) ?
                                    branchDTO.getBranchName() : null);
                    x.setBranchUrl(branchDTO.getBranchImages());
                })
                .collect(Collectors.toList());
        return new PageResult<>(records, orderPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderOutbound(TrdOrderOperVO trdOrderOperVO) {
        //校验订单是否已发送第三方
        checkSupplierOrderPushStatus(trdOrderOperVO.getSupplierId(),
                trdOrderOperVO.getTrdOrderVOS().stream().map(TrdOrderOperVO.TrdOrderVO::getOrderId).collect(Collectors.toList()));

        trdOrderOperVO.getTrdOrderVOS().forEach(order -> {
            trdOrderHandlerServices.forEach(handler -> handler.orderOutbound(trdOrderMapper.selectById(order.getOrderId()), trdOrderOperVO.getSupplierId()));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderEntrucking(TrdOrderOperVO trdOrderOperVO) {
        //校验订单是否已发送第三方
        checkSupplierOrderPushStatus(trdOrderOperVO.getSupplierId(),
                trdOrderOperVO.getTrdOrderVOS().stream().map(TrdOrderOperVO.TrdOrderVO::getOrderId).collect(Collectors.toList()));

        trdOrderOperVO.getTrdOrderVOS().forEach(order -> {
            trdOrderHandlerServices.forEach(handler -> handler.orderEntrucking(trdOrderMapper.selectById(order.getOrderId()), trdOrderOperVO.getSupplierId()));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCancelEntrucking(TrdOrderOperVO trdOrderOperVO) {
        //校验订单是否已发送第三方
        checkSupplierOrderPushStatus(trdOrderOperVO.getSupplierId(),
                trdOrderOperVO.getTrdOrderVOS().stream().map(TrdOrderOperVO.TrdOrderVO::getOrderId).collect(Collectors.toList()));

        trdOrderOperVO.getTrdOrderVOS().forEach(order -> {
            trdOrderHandlerServices.forEach(handler -> handler.orderCancelEntrucking(trdOrderMapper.selectById(order.getOrderId()), trdOrderOperVO.getSupplierId()));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderTakeDelivery(TrdOrderOperVO trdOrderOperVO) {
        //校验订单是否已发送第三方
        checkSupplierOrderPushStatus(trdOrderOperVO.getSupplierId(),
                trdOrderOperVO.getTrdOrderVOS().stream().map(TrdOrderOperVO.TrdOrderVO::getOrderId).collect(Collectors.toList()));

        TrdSupplierOrderDtl orderDtl = new TrdSupplierOrderDtl();
        orderDtl.setSupplierId(trdOrderOperVO.getSupplierId());
        orderDtl.setItemType(StatusConstants.ITEM_TYPE_1);
        trdOrderOperVO.getTrdOrderVOS().forEach(order -> {
            orderDtl.setOrderId(order.getOrderId());
            List<TrdSupplierOrderDtl> orderDtls = trdSupplierOrderDtlMapper.getSupplierOrdeDtl(orderDtl);
            if (ToolUtil.isEmpty(orderDtls) || orderDtls.isEmpty()) {
                throw exception(TRD_SUPPLIER_LOCAL_ORDER_DTL_NOT_EXISTS, order.getOrderId());
            }

            Set<Long> supplierIdSet = new HashSet<>();
            orderDtls.forEach(dtl -> {
                supplierIdSet.add(dtl.getSupplierOrderId());
                // 订单收货逻辑处理
                trdOrderHandlerServices.forEach(handler -> handler.orderTakeDelivery(dtl));
            });
            // 订单收货完成
            trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryFinish(orderDtls));

            // 订单赠送优惠劵发放
            trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryReceiveCoupon(order.getOrderId(), supplierIdSet));
        });
    }

    /**
     * @Description: 获取运营商订单管理
     * @Author: liuxingyu
     * @Date: 2024/4/9 10:11
     */
    @Override
    public PageResult<DcOrderPageRespVO> getOperatorOrderPageList(DcOrderPageReqVO dcOrderPageReqVO) {
        List<Long> supplierIdList = dcOrderPageReqVO.getSupplierIdList();
        //如果前端传入入驻商集合为空,判断是未开启查询条件还是查询条件为空
        if (ObjectUtil.isEmpty(supplierIdList)) {
            //通过运营商ID获取绑定的入驻商
            if (ObjectUtil.equal(dcOrderPageReqVO.getIsSupplier(), NumberPool.INT_ZERO)) {
                if (ObjectUtil.isNotNull(SecurityUtils.getLoginUser().getDcId())) {
                    supplierIdList = supplierApi.getByOrder(SecurityUtils.getLoginUser().getDcId()).getCheckedData();
                    if (ObjectUtil.isEmpty(supplierIdList)) {
                        //运营商区域未匹配到入驻商 直接返回
                        return new PageResult<>();
                    }
                }
                //获取当前登录的入驻商账号查询
                if (ObjectUtil.isNotNull(SecurityUtils.getSupplierId())) {
                    supplierIdList.add(SecurityUtils.getSupplierId());
                }
            } else {
                return new PageResult<>();
            }
        }
        //处理产品集合
        List<Long> spuIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dcOrderPageReqVO.getProductList())) {
            spuIds = dcOrderPageReqVO.getProductList().stream().map(DcOrderProductReqVO::getSpuId).distinct().collect(Collectors.toList());
            skuIds = dcOrderPageReqVO.getProductList().stream().map(DcOrderProductReqVO::getSkuId).distinct().collect(Collectors.toList());
        }
        if (ToolUtil.isNotEmpty(dcOrderPageReqVO.getEndTime())) {
            dcOrderPageReqVO.setEndTime(DateUtil.endOfDay(dcOrderPageReqVO.getEndTime()));
        }
        Page<DcOrderPageReqVO> page = new Page<>(dcOrderPageReqVO.getPageNo(), dcOrderPageReqVO.getPageSize());
        Page<DcOrderPageRespVO> orderPageRespPage = trdOrderMapper.getOperatorOrderPageList(
                page, dcOrderPageReqVO, supplierIdList, spuIds, skuIds,
                dcOrderPageReqVO.getBranchIds(), dcOrderPageReqVO.getColonelIds());
        //获取结果集并填充数据
        List<DcOrderPageRespVO> orderPageRespVOList = orderPageRespPage.getRecords();
        if (ObjectUtil.isNotEmpty(orderPageRespVOList)) {
            orderPageRespVOList = orderPageRespVOList.stream().peek(x -> {
                //填充入驻商名称
                if (ObjectUtil.isNotNull(x.getSupplierId())) {
                    SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(x.getSupplierId());
                    if (ObjectUtil.isNotNull(supplierDTO)) {
                        x.setSupplierName(supplierDTO.getSupplierName());
                        x.setContactPhone(supplierDTO.getContactPhone());
                    }
                }
                //填充业务员名称
                if (ObjectUtil.isNotNull(x.getColonelId())) {
                    ColonelDTO colonelDTO = trdCacheService.getColonelDTO(x.getColonelId());
                    if (ObjectUtil.isNotNull(colonelDTO)) {
                        x.setColonelName(colonelDTO.getColonelName());
                    }
                }
                //填充门店信息
                if (ObjectUtil.isNotNull(x.getBranchId())) {
                    BranchDTO branchDTO = trdCacheService.getBranchDTO(x.getBranchId());
                    if (ObjectUtil.isNotNull(branchDTO)) {
                        x.setBranchName(branchDTO.getBranchName());
                        x.setBranchAddr(branchDTO.getBranchAddr());
                    }
                }
                //填充spu信息
                if (ObjectUtil.isNotNull(x.getSpuId())) {
                    SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
                    if (ObjectUtil.isNotNull(spuDTO)) {
                        x.setSpuName(spuDTO.getSpuName());
                        x.setSpuNo(spuDTO.getSpuNo());
                    }
                }
                //填充sku
                if (ObjectUtil.isNotNull(x.getSkuId())) {
                    SkuDTO skuDTO = trdCacheService.getSkuDTO(x.getSkuId());
                    if (ObjectUtil.isNotNull(skuDTO)) {
                        x.setBarcode(skuDTO.getBarcode());
                        String properties = PropertyAndValDTO.getProperties(skuDTO.getProperties());
                        if (ToolUtil.isNotEmpty(properties))  x.setSpuName(x.getSpuName() + "-" + properties);

                    }
                }
            }).collect(Collectors.toList());
        }
        return new PageResult<>(orderPageRespVOList, orderPageRespPage.getTotal());
    }


    @Override
    public PageResult<DcSupplierOrderPageRespVO> getOperatorOrderPageListNew(DcOrderPageReqVO dcOrderPageReqVO) {
        PageResult<DcSupplierOrderPageRespVO> result = new PageResult<>();
//        List<Long> supplierIdList = dcOrderPageReqVO.getSupplierIdList();
        //如果前端传入入驻商集合为空,判断是未开启查询条件还是查询条件为空
//        if (ObjectUtil.isEmpty(supplierIdList)) {
//            //通过运营商ID获取绑定的入驻商
//            if (ObjectUtil.equal(dcOrderPageReqVO.getIsSupplier(), NumberPool.INT_ZERO)) {
//                if (ObjectUtil.isNotNull(SecurityUtils.getLoginUser().getDcId())) {
//                    supplierIdList = supplierApi.getByOrder(SecurityUtils.getLoginUser().getDcId()).getCheckedData();
//                    if (ObjectUtil.isEmpty(supplierIdList)) {
//                        //运营商区域未匹配到入驻商 直接返回
//                        return new PageResult<>();
//                    }
//                }
//                //获取当前登录的入驻商账号查询
//                if (ObjectUtil.isNotNull(SecurityUtils.getSupplierId())) {
//                    supplierIdList.add(SecurityUtils.getSupplierId());
//                }
//            } else {
//                return new PageResult<>();
//            }
//
////            SecurityUtils.getLoginUser().setSupplierId(supplierIdList.get(0));
//        }
        //处理产品集合
        List<Long> spuIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dcOrderPageReqVO.getProductList())) {
            spuIds = dcOrderPageReqVO.getProductList().stream().map(DcOrderProductReqVO::getSpuId).distinct().collect(Collectors.toList());
//            skuIds = dcOrderPageReqVO.getProductList().stream().map(DcOrderProductReqVO::getSkuId).distinct().collect(Collectors.toList());
        }


        dcOrderPageReqVO.setPageNo((dcOrderPageReqVO.getPageNo() - 1) * dcOrderPageReqVO.getPageSize());
//        dcOrderPageReqVO.setSupplierIdList(supplierIdList);
        if (ToolUtil.isNotEmpty(dcOrderPageReqVO.getEndTime())) {
            dcOrderPageReqVO.setEndTime(DateUtil.endOfDay(dcOrderPageReqVO.getEndTime()));
        }

//        Long dcId = SecurityUtils.getLoginUser().getDcId();
        Long dcId = SecurityUtils.getLoginUser() == null ? null : SecurityUtils.getLoginUser().getDcId();

        String orderDeliveryOvertimeDay = "3";
        if(ToolUtil.isNotEmpty(dcId)){
            OrderSettingPolicyDTO partnerPolicy =partnerPolicyApi.getOrderSettingPolicy(dcId).getCheckedData();
            if(ToolUtil.isNotEmpty(partnerPolicy) && ToolUtil.isNotEmpty(partnerPolicy.getOrderDeliveryOvertimeDay()))
                orderDeliveryOvertimeDay = partnerPolicy.getOrderDeliveryOvertimeDay();
        }

        dcOrderPageReqVO.setOrderDeliveryOvertimeDay(orderDeliveryOvertimeDay);
        Long total = trdOrderMapper.selectOrderPageListNewCount(dcOrderPageReqVO, spuIds, skuIds);
        if (total <= NumberPool.LONG_ZERO) { // 没有查询到数据，直接返回
            result.setList(new ArrayList<>());
            result.setTotal(0L);
            return result;
        }
        List<DcSupplierOrderPageRespVO> resultData =  trdOrderMapper.getOperatorOrderPageListNew(dcOrderPageReqVO, spuIds, skuIds);

        resultData.forEach(data -> {
            getOperatorOrderRespAssembleResult(data, dcOrderPageReqVO);
        });
        result.setList(resultData);
        result.setTotal(total);
        return result;
    }

    @Override
    public List<PcOrderPrintMasterVO> getOperatorOrderPageListNewPrint(DcOrderPageReqVO dcOrderPageReqVO) {
        List<PcOrderPrintDetailVO> list = trdOrderMapper.getOperatorOrderPageListNewPrint(dcOrderPageReqVO);
        // 按入驻商, 订单号分组
        Map<String, List<PcOrderPrintDetailVO>> supplierOrderMap = list.stream().collect(Collectors.groupingBy(item -> StringUtils.format("{}:{}", item.getOrderId(), item.getSupplierId())));

        // 内存缓存
        LFUCache<Long, SupplierDTO> supplierCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, BranchDTO> branchCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, SpuDTO> spuCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, SkuDTO> skuCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, MemberDTO> memberCache = CacheUtil.newLFUCache(0);

        // 渲染规格参数
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        // 返回列表
        List<PcOrderPrintMasterVO> resultList = new ArrayList<>();

        // 处理数据
        supplierOrderMap.forEach((key, itemList) -> {

            PcOrderPrintDetailVO tmp = itemList.get(0);

            // 加载缓存
            SupplierDTO supplierDTO = supplierCache.get(tmp.getSupplierId(), () -> trdCacheService.getSupplierDTO(tmp.getSupplierId()));
            BranchDTO branchDTO = branchCache.get(tmp.getBranchId(), () -> trdCacheService.getBranchDTO(tmp.getBranchId()));
            MemberDTO memberDTO = memberCache.get(tmp.getMemberId(), () -> trdCacheService.getMemberDTO(tmp.getMemberId()));

            PcOrderPrintMasterVO masterVO = new PcOrderPrintMasterVO();
            masterVO.setSupplierName(supplierDTO.getSupplierName());
            masterVO.setCreateTime(tmp.getCreateTime());
            masterVO.setOrderNo(tmp.getOrderNo());
            masterVO.setSupplierOrderNo(tmp.getSupplierOrderNo());
            masterVO.setPayWay(tmp.getPayWay());

            masterVO.setBranchName(branchDTO.getBranchName());
            masterVO.setReceiveAddr(branchDTO.getBranchAddr());
            masterVO.setBranchContactPhone(branchDTO.getContactPhone());

            masterVO.setReceiveName(memberDTO.getMemberName());
            masterVO.setMemo(tmp.getMemo());
            masterVO.setPrintQty(tmp.getPrintQty());
            masterVO.setDetaiList(itemList);

            BigDecimal totalAmt = itemList.stream().map(PcOrderPrintDetailVO::getTotalAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            int totalNum = itemList.stream().mapToInt(PcOrderPrintDetailVO::getNum).sum();
            masterVO.setTotalAmt(totalAmt);
            masterVO.setTotalNum(totalNum);
            masterVO.setTotalAmtCase(MoneyUtil.toChinese(totalAmt.toString()));

            itemList.forEach(item -> {

                SpuDTO spuDTO = spuCache.get(item.getSpuId(), () -> trdCacheService.getSpuDTO(item.getSpuId()));
                SkuDTO skuDTO = skuCache.get(item.getSkuId(), () -> trdCacheService.getSkuDTO(item.getSkuId()));
                item.setSpuName(spuDTO.getSpuName());
                item.setItemNo(spuDTO.getSpuNo());
                item.setBarcode(skuDTO.getBarcode());

                // 设置单位名称
                item.setOrderUnit(unitMap.get(item.getOrderUnit()));
            });
            resultList.add(masterVO);
        });
        return resultList;
    }


    @Override
    public DcSupplierOrderPageRespVO getOperatorOrderInfoDetail(Long supplierOrderId, Integer deliveryState) {
        DcOrderPageReqVO dcOrderPageReqVO = new DcOrderPageReqVO();
        dcOrderPageReqVO.setSubOrderId(supplierOrderId);
        dcOrderPageReqVO.setOrderType(deliveryState);
        dcOrderPageReqVO.setPageNo(NumberPool.INT_ZERO);
        Long dcId = SecurityUtils.getLoginUser().getDcId();

        String orderDeliveryOvertimeDay = "3";
        if(ToolUtil.isNotEmpty(dcId)){
            OrderSettingPolicyDTO partnerPolicy =partnerPolicyApi.getOrderSettingPolicy(dcId).getCheckedData();

            if(ToolUtil.isNotEmpty(partnerPolicy) && ToolUtil.isNotEmpty(partnerPolicy.getOrderDeliveryOvertimeDay())){
                orderDeliveryOvertimeDay = partnerPolicy.getOrderDeliveryOvertimeDay();
            }
        }

        dcOrderPageReqVO.setOrderDeliveryOvertimeDay(orderDeliveryOvertimeDay);

        List<DcSupplierOrderPageRespVO> resultData =  trdOrderMapper.getOperatorOrderPageListNew(dcOrderPageReqVO, null, null);
        if (ToolUtil.isEmpty(resultData) || resultData.isEmpty()) {
            return new DcSupplierOrderPageRespVO();
        }
        DcSupplierOrderPageRespVO respVO = resultData.get(NumberPool.INT_ZERO);

        // 订单查询返回结果组装
        getOperatorOrderRespAssembleResult(respVO,null);
        // 补充订单明细售后差异信息
        getSupplierOrderDtlAfterAssembleResult(respVO);


        //这里需要单独处理下订单详情门店地址显示省市区问题 再列表数据列表中 api 调用会耗时较长
        BranchDTO branchDTO = trdCacheService.getBranchDTO(respVO.getBranchId());
        if (Objects.nonNull(branchDTO) && Objects.nonNull(branchDTO.getThreeAreaCityId())) {
            SysAreaCityRespVO three = areaCityApi.getById(branchDTO.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                String areaCityName = first.getName()+second.getName()+three.getName();
                respVO.setBranchAddr(areaCityName+branchDTO.getBranchAddr());
            }
        }

        // 处理订单详情的优惠信息
        getTrdOrderRespDTOAssembleDiscountResult(respVO);

        // 订单配置项
        OrderSettingPolicyDTO settingPolicyInfo = trdCacheService.getOrderSettingPolicyInfo(resultData.get(0).getDcId());
        if (Objects.nonNull(settingPolicyInfo)) {
            resultData.forEach(item -> TrdOrderConvert.INSTANCE.buildSetOrderSetting(item, settingPolicyInfo));
        }

        // 设置支付流水号
        PayFlowDTO payFlowDTO = payFlowApi.getPaySuccessFlow(resultData.get(0).getOrderNo()).getCheckedData();
        if (Objects.nonNull(payFlowDTO)) {
            respVO.setPayFlowId(payFlowDTO.getPayFlowId());
        }
        return respVO;
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#supplierOrderNo", tryLock = true)
    //!@订单 - 取消 - 1、入口
    public void orderCancel(String supplierOrderNo) {
        // 获取主订单信息
        TrdSupplierOrder trdSupplierOrder = trdSupplierOrderMapper.selectOrderBySupplierOrderNo(supplierOrderNo);
        if (ToolUtil.isEmpty(trdSupplierOrder)) {
            log.error("入驻商订单{}不存在！", supplierOrderNo);
            throw exception(TRD_ORDER_NOT_EXISTS);
        } else if (PayStateEnum.PAY_BEING_CANCELLED.getCode().equals(trdSupplierOrder.getPayState())) {
            log.error("入驻商订单{}取消中，请稍后再试！", supplierOrderNo);
            throw exception(TRD_ORDER_CANCELLING);
        } else if (PayStateEnum.PAY_CANCEL.getCode().equals(trdSupplierOrder.getPayState())) {
            log.error("入驻商订单{}已取消！", supplierOrderNo);
            throw exception(TRD_ORDER_ALREADY_CANCEL);
        }

        trdOrderHandlerServices.forEach(handler -> handler.orderCancel(trdSupplierOrder));

        //订单取消  校验是否是首单
//        BranchDTO branchDTO = trdCacheService.getBranchDTO(trdSupplierOrder.getBranchId());
//        if(branchDTO.fistOrderNoFlagTrue(trdSupplierOrder.getOrderNo())){
//            //如果是首单
//            //校验该门店是否存在已支付的订单
//            //校验是否是首单单号取消
//            //如果是首单单号取消 需要校验该门店是否存在已支付的订单
//            //如果不存在，修改首单标识、清除首单单号
//            //如果存在，则不是首单了
//            if(trdSupplierOrderDtlMapper.checkOrderDeliveryStatusCountByBranchId(trdSupplierOrder.getBranchId(),trdSupplierOrder.getOrderId()) == NumberPool.INT_ZERO){
//                //修改首单标识、清除首单单号
//                branchDTO.setFirstOrderNo(null);
//                branchDTO.setFirstOrderFlag(null);
//                branchApi.updateFirstOrder(branchDTO);
//            }
//        }

        // 获取入驻商订单信息
//        List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectListByOrderId(trdSupplierOrder.getOrderId());
//
//        // 获取订单结算信息
//        List<TrdSupplierOrderSettle> supplierOrderSettles = supplierOrderSettleMapper.selectListByOrderId(trdSupplierOrder.getOrderId());


        /**
         * 取消冻结入驻商分成利润金额流水 TODO 应入驻商充值方案弃用，故此代码弃用
         */
//        List<AccAccountFlowDTO> accountFreezeFlows = createAccountFreezeFlow(supplierOrders, HutoolBeanUtils.toBean(supplierOrderSettles, TrdSupplierOrderSettleSaveVO.class), trdOrder, StatusConstants.FREEZE_FLOW_RELEASE);
//        if (ToolUtil.isNotEmpty(accountFreezeFlows) && !accountFreezeFlows.isEmpty()) {
//            accountApi.saveAccountFlowAndProcess(accountFreezeFlows).checkError();
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderTakeDeliveryJob(TrdOrderTakeDeliveryVO takeDeliveryVO) {
        /**
         * //!@定时器 - 2、单据自动确认收货定时任务 - 2、查询出已装车订单明细数据，一条一条进行处理
         */
        List<TrdSupplierOrderDtl> orderDtls = trdSupplierOrderDtlMapper.getEntruckingOrderDtlList(takeDeliveryVO);
        Map<Long, List<TrdSupplierOrderDtl>> orderMap = orderDtls.stream().collect(Collectors.groupingBy(TrdSupplierOrderDtl::getOrderId));
        orderMap.forEach((key, value) -> {
            Set<Long> supplierIdSet = new HashSet<>();
            value.stream().forEach(dtl -> {
                supplierIdSet.add(dtl.getSupplierOrderId());
                trdOrderHandlerServices.forEach(handler -> handler.orderTakeDelivery(dtl));
            });
            // 订单收货完成
            trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryFinish(value));

            // 订单赠送优惠劵发放
            trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryReceiveCoupon(key, supplierIdSet));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderComplete(TrdOrderTakeDeliveryVO takeDeliveryVO) {
        /**
         * //!@定时器 - 3、单据自动确认完成定时任务 - 2、查询出已收货订单明细数据，一条一条进行处理
         */
        List<TrdSupplierOrderDtl> orderDtls = trdSupplierOrderDtlMapper.getCompleteOrderDtlList(takeDeliveryVO);
        orderDtls.forEach(dtl -> {
            trdOrderHandlerServices.forEach(handler -> handler.orderComplete(dtl));
        });
    }

    @Override
    //!@定时器 - 4、订单分账 - 2、查询待处理结算的订单信息，判断是否订单完成
    public void orderCreateSettleTransfer(Long sysCode, Long orderId) {
        // 根据平台商查询出已完成  待处理结算的订单信息
        List<TrdSettle> settles = trdSettleMapper.selectSettleListBySettleState(StatusConstants.SETTLE_STATE_3, sysCode, orderId);

        settles.stream().collect(Collectors.groupingBy(TrdSettle::getOrderId))
                .forEach((torId, settleList) -> {
                    // 获取主订单信息
                    TrdOrder tor = trdOrderMapper.selectById(torId);
                    // 获取入驻商订单信息
                    List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListByOrderId(torId);
                    // 获取入驻商订单明细信息
                    List<TrdSupplierOrderDtl> tsodList = trdSupplierOrderDtlMapper.selectListByOrderId(torId);

                    //020模式是在完成后7个工作日开始分账
                    if (DistributionModeEnum.O2O.getCode().equals(tor.getDistributionMode())) {
                        return;
                    }
                    try {
                        // 生成结算转账单或订单发起分账
                        getPayWayHandlerService(tor.getPlatform(), tor.getPayWay()).orderCompleteCreateSettleTransfer(tor, tsoList, tsodList, settleList);
                    } catch (Exception e){
                        log.error("订单{}结算分账失败！",tor.getOrderNo(), e);
                    }


        });
    }

    @Override
    public void orderO2OSignAfterSettle(O2OSettleTaskParamVO o2OSettleTaskParam) {
        Long sysCode = o2OSettleTaskParam.getSysCode();
        List<String> orderNos = o2OSettleTaskParam.getOrderNos();
        List<String> supplierOrderNos = o2OSettleTaskParam.getSupplierOrderNos();
        // 根据商户类型找到待结算信息 （分入驻商和门店）
        List<TrdSettleDTO> searchSettles = trdSettleMapper.selectO2OSettleListBySettleState(StatusConstants.SETTLE_STATE_3, sysCode, orderNos,supplierOrderNos, o2OSettleTaskParam.getSignAfterMinutes(),o2OSettleTaskParam.getMerchantType());
        orderO2OSignAfterSettle(searchSettles);
    }
    
    @Override
    public void orderO2OSignAfterSettle(List<TrdSettleDTO> searchSettles) {
        if (ToolUtil.isEmpty(searchSettles)) {
            log.info("orderO2OSignAfterSettle -》 没有需要结算的订单！");
            return;
        }
        List<TrdSettle> settles = TrdSettleConvert.INSTANCE.convert2TrdSettleList(searchSettles);
        settles.stream().collect(Collectors.groupingBy(TrdSettle::getOrderId))
        .forEach((torId, settleList) -> {
            // 获取主订单信息
            TrdOrder tor = trdOrderMapper.selectById(torId);
            // 获取入驻商订单信息
            List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListByOrderId(torId);
            // 获取入驻商订单明细信息
            List<TrdSupplierOrderDtl> tsodList = trdSupplierOrderDtlMapper.selectListByOrderId(torId);
            try {
                // 生成结算转账单或订单发起分账
                getPayWayHandlerService(tor.getPlatform(), tor.getPayWay()).orderCompleteCreateSettleTransfer(tor, tsoList, tsodList, settleList);
            } catch (Exception e){
                log.error("订单{}结算分账失败！",tor.getOrderNo(), e);
            }
        });
    }

    @Override
    public void mideaPayO2OSettle(Long sysCode, String orderNo) {

        TrdOrder trdOrder = trdOrderMapper.selectOne(TrdOrder::getOrderNo, orderNo);
        if (ToolUtil.isEmpty(trdOrder)) {
            log.error("订单{}不存在！", orderNo);
            throw exception(TRD_ORDER_NOT_EXISTS);
        }

        // 根据平台商查询出已完成  待处理结算的订单信息
        List<TrdSettle> settles = trdSettleMapper.selectSettleListBySettleState(StatusConstants.SETTLE_STATE_3, sysCode, trdOrder.getOrderId());
        //结算根据子单分组
        settles.stream().collect(Collectors.groupingBy(TrdSettle::getSupplierOrderNo))
        .forEach((supplierOrderNo, settleList) -> {
            Long orderId = trdOrder.getOrderId();
            // 获取入驻商订单信息
            List<TrdSupplierOrder> tsoList = trdSupplierOrderMapper.selectListBySupplierOrderNoList(Arrays.asList(supplierOrderNo));
            // 获取入驻商订单明细信息
            List<TrdSupplierOrderDtl> tsodList = trdSupplierOrderDtlMapper.selectListBySupplierOrderNo(supplierOrderNo);
            try {
                // 生成结算转账单或订单发起分账
                getPayWayHandlerService(trdOrder.getPlatform(), trdOrder.getPayWay()).orderCompleteCreateSettleTransfer(trdOrder, tsoList, tsodList, settleList);
            } catch (Exception e){
                log.error("订单{}结算分账失败！",trdOrder.getOrderNo(), e);
            }
        });
    }

    @Override
    public void orderTakeDelivery(Long orderId) {
        TrdSupplierOrderDtl orderDtl = new TrdSupplierOrderDtl();
        orderDtl.setOrderId(orderId);
        orderDtl.setItemType(ProductType.GLOBAL.getCode());
        List<TrdSupplierOrderDtl> orderDtls = trdSupplierOrderDtlMapper.getSupplierOrdeDtl(orderDtl);
        Set<Long> supplierIdSet = new HashSet<>();
        orderDtls.stream().forEach(dtl -> {
            if (ToolUtil.isEmpty(dtl))
                throw exception(TRD_SUPPLIER_ORDER_DTL_NOT_EXISTS);
            supplierIdSet.add(dtl.getSupplierOrderId());
            trdOrderHandlerServices.forEach(handler -> handler.orderTakeDelivery(dtl));
        });
        // 订单收货完成
        trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryFinish(orderDtls));

        // 订单赠送优惠劵发放
        trdOrderHandlerServices.forEach(handler -> handler.orderTakeDeliveryReceiveCoupon(orderId, supplierIdSet));
    }

    @Override
    public OrderAfterResDTO  getOrderAfterInfoByOrderInfo(OrderAfterRequest request) {
        List<OrderAfterDtlDTO> dtlDto = trdOrderMapper.getOrderAfterInfo(request);
        if (ToolUtil.isEmpty(dtlDto) || dtlDto.isEmpty())
            throw new ServiceException("订单【" + request.getOrderNo() + "】已无可售后的商品！");

        // 查询是否存在有未完成的售后订单
        Integer unfinishedAfterCount = trdAfterMapper.selectUnfinishedAfterByOrderId(Long.valueOf(request.getOrderId()));
        if (unfinishedAfterCount > NumberPool.INT_ZERO){
            throw new ServiceException("订单【" + request.getOrderNo() + "】已发起申请售后，等待入驻商处理！");
        }


        // 售后订单
        OrderAfterResDTO resDTO = new OrderAfterResDTO();
        // 售后类型  发货前售后为 仅退款   收货后售后为 退货退款
        Long afterType = Objects.equals(DeliveryStatusEnum.WAIT_FH.getCode(), request.getDeliveryState()) ? AfterTypeEnum.REDUNDONLY.getState() : AfterTypeEnum.RETURNSANDREFUNDS.getState();
        resDTO.setOrderId(dtlDto.get(0).getOrderId())
                .setOrderNo(dtlDto.get(0).getOrderNo())
                .setBranchId(dtlDto.get(0).getBranchId())
                .setAfterType(afterType)
                .setDeliveryState(request.getDeliveryState())
                .setAfterReturnAmt(BigDecimal.ZERO)
                .setOrderSuppliers(new ArrayList<>());

        BranchDTO branchDTO = trdCacheService.getBranchDTO(dtlDto.get(0).getBranchId());
        if (ToolUtil.isNotEmpty(branchDTO)) resDTO.setBranchName(branchDTO.getBranchName());


        // 售后入驻商订单
        Map<Long, List<OrderAfterDtlDTO>> supplierMap = dtlDto.stream().collect(Collectors.groupingBy(OrderAfterDtlDTO::getSupplierId));
        supplierMap.forEach((key, value) -> {
            // 校验入驻商订单是否已发送 第三方系统 且本次售后是发货前售后时校验
            if (ToolUtil.isNotEmpty(value.get(0).getPushStatus()) && value.get(0).getPushStatus() == 1 && Objects.equals(afterType, AfterTypeEnum.REDUNDONLY.getState())) {
                throw exception(TRD_ORDER_NOT_AFTER, value.get(0).getOrderNo());
            }

            OrderAfterResDTO.OrderSupplier orderSupplier = new OrderAfterResDTO.OrderSupplier();
            resDTO.getOrderSuppliers().add(orderSupplier);
            orderSupplier.setSupplierId(value.get(0).getSupplierId())
                    .setSupplierName(value.get(0).getSupplierName())
                    .setSupplierOrderId(value.get(0).getSupplierOrderId())
                    .setSubReturnAmt(BigDecimal.ZERO)
                    .setCouponTemplateList(new ArrayList<>())
                    .setOrderSupplierDtls(new ArrayList<>());

            // 售后入驻商订单明细
            dtlDto.stream()
                    .filter((dtl -> dtl.getSupplierOrderId().compareTo(orderSupplier.getSupplierOrderId()) == 0))
                    .forEach(dtl -> {
                        OrderAfterResDTO.OrderSupplier.OrderSupplierDtl supplierDtl = new OrderAfterResDTO.OrderSupplier.OrderSupplierDtl();
                        orderSupplier.getOrderSupplierDtls().add(supplierDtl);

                        supplierDtl.setSupplierOrderDtlId(dtl.getSupplierOrderDtlId())
                                // 单价（下单单位）
                                .setPrice(dtl.getPrice())
                                // 数量（下单单位）
                                .setTotalNum(dtl.getTotalNum())
                                // 赠品标识
                                .setGiftFlag(dtl.getGiftFlag())
                                // 售后单位大小 （下单单位）
                                .setUnitType(dtl.getUnitType())
                                // 售后单位编号（下单单位），取数据字典：sys_prdt_unit
                                .setOrderUnit(dtl.getOrderUnit())
                                // 最小单位数量
                                .setMinTotalNum(dtl.getMinTotalNum())
                                // 单价（最小单位）
                                .setMinPrice(dtl.getMinPrice())
                                // 因这里可以拆箱退货，故退货总金额 由最小单位数据计算得出
                                .setTotalAmt(dtl.getTotalAmt())
                                .setOldestDate(dtl.getOldestDate())
                                .setLatestDate(dtl.getLatestDate())
                                .setOrderTotalNum(dtl.getOrderTotalNum())
                        ;

                        resDTO.setAfterReturnAmt(resDTO.getAfterReturnAmt().add(supplierDtl.getTotalAmt()));
                        orderSupplier.setSubReturnAmt(orderSupplier.getSubReturnAmt().add(supplierDtl.getTotalAmt()));

                        SkuDTO skuDTO = trdCacheService.getSkuDTO(dtl.getSkuId());
                        if (ToolUtil.isNotEmpty(skuDTO)) {
                            supplierDtl.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties()));
                            if (UnitTypeEnum.L(dtl.getUnitType())) { // 大单位
                                supplierDtl.setUnitBarcode(skuDTO.getLargeBarcode());
                            } else if (UnitTypeEnum.M(dtl.getUnitType())){ // 中单位
                                supplierDtl.setUnitBarcode(skuDTO.getMidBarcode());
                            } else if (UnitTypeEnum.S(dtl.getUnitType())){ // 小单位
                                supplierDtl.setUnitBarcode(skuDTO.getBarcode());
                            }
                        }




                        SpuDTO spuDTO = trdCacheService.getSpuDTO(dtl.getSpuId());
                        if (ToolUtil.isNotEmpty(spuDTO)) {
                            supplierDtl.setThumb(spuDTO.getThumb())
                                    .setSpuName(spuDTO.getSpuName())
                                    .setMinUnit(spuDTO.getMinUnit())
                                    .setSpuNo(spuDTO.getSpuNo())
                                    .setPricingWay(ToolUtil.isEmptyReturn(spuDTO.getPricingWay(), SpuPricingWayEnum.ORDINARY.getType()))
                            ;
                        }

                    });

            // 查询根据入驻商单查询订单优惠信息表
            orderSupplier.setCouponTemplateList(getSupplierOrderGiftCouponInfoBySupplierOrderId(orderSupplier.getSupplierOrderId()));
        });
        return resDTO;
    }

    @Override
    public List<BranchSkuMergeAfterResDTO> branchSkuMergeAfterList(BranchSkuMergeAfterReqVO reqVo) {
        List<BranchSkuMergeAfterResDTO> resDtoList = trdOrderMapper.branchSkuMergeAfterList(reqVo);
        resDtoList.forEach(item -> {
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(item.getSupplierId());
            if (ToolUtil.isNotEmpty(supplierDTO)) {
                item.setSupplierName(supplierDTO.getSupplierName());
            }

            SpuDTO spuDTO = trdCacheService.getSpuDTO(item.getSpuId());
            if (ToolUtil.isNotEmpty(spuDTO)) {
                item.setSpuName(spuDTO.getSpuName())
                        .setSpuNo(spuDTO.getSpuNo())
                        .setThumb(spuDTO.getThumb())
                        .setMinUnit(spuDTO.getMinUnit())
                ;
            }

            SkuDTO skuDTO = trdCacheService.getSkuDTO(item.getSkuId());
            if (ToolUtil.isNotEmpty(skuDTO)) {
                item.setBarcode(skuDTO.getUnitBarcode(item.getOrderUnitType()));
            }
        });
        return resDtoList;
    }

    @Override
    public OrderMergeAfterResDTO orderMergeAfter(BranchSkuMergeAfterReqVO reqVo) {
        List<BranchSkuMergeAfterResDTO> resDtoList = branchSkuMergeAfterList(reqVo);

        // 查询当前商品订单数据 存在 未完成的售后订单 进行过滤
        List<BranchSkuMergeAfterResDTO> resList = resDtoList.stream().filter(res -> trdAfterMapper.selectUnfinishedAfterByOrderId(res.getOrderId()) <= NumberPool.INT_ZERO).collect(Collectors.toList());
        if (ToolUtil.isEmpty(resList) || resList.isEmpty()) {
            throw new ServiceException("商品不存在可售后的订单数据！");
        }
        BranchSkuMergeAfterResDTO bsmard = resList.get(NumberPool.INT_ZERO);
        BranchDTO branchDTO = trdCacheService.getBranchDTO(bsmard.getBranchId());
        return OrderMergeAfterResDTO.builder()
                .branchId(bsmard.getBranchId())
                .spuId(bsmard.getSpuId())
                .branchSkuMergeAfterResDTOList(resList)
                .thumb(bsmard.getThumb())
                .spuName(bsmard.getSpuName())
                .spuNo(bsmard.getSpuNo())
                .barcode(bsmard.getBarcode())
                .branchName(branchDTO.getBranchName())
                .build();
    }

    @Override
    public RemoteSaveOrderVO getOrderInfoByOrderId(Long orderId) {
        RemoteSaveOrderVO orderRes = new RemoteSaveOrderVO();
        // 查询订单主表数据
        TrdOrder trdOrder = trdOrderMapper.selectById(orderId);
        // 查询订单入驻商表数据
        List<TrdSupplierOrder> supplierOrders = trdSupplierOrderMapper.selectListByOrderId(orderId);
        // 查询入驻商订单明细表数据
        List<TrdSupplierOrderDtl> supplierOrderDtls = trdSupplierOrderDtlMapper.selectListByOrderId(orderId);
        // 查询入驻商订单明细结算数据
        List<TrdSupplierOrderSettle> supplierOrderSettles = supplierOrderSettleMapper.selectListByOrderId(orderId);

        orderRes.setOrderSaveVo(HutoolBeanUtils.toBean(trdOrder, TrdOrderSaveVO.class));
        orderRes.setSupplierOrderSaveVOs(HutoolBeanUtils.toBean(supplierOrders, TrdSupplierOrderSaveVO.class));
        orderRes.setSupplierOrderDtlSaveVOS(HutoolBeanUtils.toBean(supplierOrderDtls, TrdSupplierOrderDtlSaveVO.class));
        orderRes.setSupplierOrderSettleSaveVOS(HutoolBeanUtils.toBean(supplierOrderSettles, TrdSupplierOrderSettleSaveVO.class));
        return orderRes;
    }

    /**
     * @Description: 打印获取订单数据
     * @Author: liuxingyu
     * @Date: 2024/4/19 15:48
     */
    @Override
    public List<PrintSupplierOrderVO> printGetByOrderId(Long orderId, Long sysCode, Long supplierOrderId) {
        return trdOrderMapper.printGetByOrderId(orderId, sysCode, supplierOrderId);
    }

    /**
     * 获取订单状态的数量（角标）
     *
     * @param reqVO
     * @return
     */
    @Override
    public OrderStatusVO getOrderStatus(OrderStatusReqVO reqVO) {
        // 获取门店待付款、待发货、待收货、已收货 状态订单数量
        OrderStatusVO orderStatusVO = trdOrderMapper.getOrderStatus(reqVO);
        // 获取门店售后订单数量
        Integer saleAfterNum = trdOrderMapper.getSaleAfterNum(reqVO);
        if (saleAfterNum == null) {
            orderStatusVO.setAfterSales(NumberPool.INT_ZERO);
        } else {
            orderStatusVO.setAfterSales(saleAfterNum);
        }
        return orderStatusVO;
    }

    /**
     * 当月订单金额统计
     *
     * @param branchId 门店ID
     * @return
     */
    @Override
    public OrderAmountStatisticsVO getOrderAmountStatisticsVO(Long branchId) {
        Date beginTime = DateUtil.beginOfMonth(new Date());
        Date endTime = DateUtil.endOfMonth(new Date());
        OrderAmountStatisticsVO orderAmountStatisticsVO = trdOrderMapper.getBranchMonthOrderAmount(branchId, beginTime, endTime);
        orderAmountStatisticsVO.setStartDate(beginTime);
        orderAmountStatisticsVO.setEndDate(endTime);
        return orderAmountStatisticsVO;
    }

    @Override
    public ColonelAppOrderListTotalDTO selectMemColoneAppOrderListTotal(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        return trdOrderMapper.selectMemColoneAppOrderListTotal(orderPageReqVO);
    }

    @Override
    public PageResult<TrdColonelAppOrderListRespVO> selectMemColoneAppOrder(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        PageResult<TrdColonelAppOrderListRespVO> result = new PageResult<>();
        Page<TrdColonelAppOrderListPageReqVO> page = new Page<>(orderPageReqVO.getPageNo(), orderPageReqVO.getPageSize());
        Page<TrdColonelAppOrderListRespVO> pageResult = trdOrderMapper.selectMemColoneAppOrder(orderPageReqVO,page,orderPageReqVO.getColonelIds());
        pageResult.getRecords().forEach(order -> {
            BranchDTO branch = trdCacheService.getBranchDTO(order.getBranchId());
            order.setBranchName(branch.getBranchName());
            order.setBranchAddress(branch.getBranchAddr());
            ColonelDTO colonel = trdCacheService.getColonelDTO(order.getColonelId());
            order.setColonelName(colonel.getColonelName());
        });
        result.setList(pageResult.getRecords());
        result.setTotal(pageResult.getTotal());
        return result;
    }

    @Override
    public List<TrdOrderRespDTO> selectMemColoneAppOrderNew(TrdColonelAppOrderListPageReqVO orderPageReqVO) {
        List<TrdOrderRespDTO> list = trdOrderMapper.selectMemColoneAppOrderNew(orderPageReqVO);
        list.forEach(order -> {
            // 订单总金额数据设置初始值，防止后续进行操作报NULL异常
            order.setPayAmt(BigDecimal.ZERO)
                    .setOrderAmt(BigDecimal.ZERO)
                    .setDiscountAmt(BigDecimal.ZERO);

            // 查询订单结算金额
            List<TrdSettle> settleList = trdSettleService.getTrdSettleListByOrderIdAndMerchant(order.getOrderId(), MerchantTypeEnum.COLONEL.getType(), SecurityUtils.getLoginUser().getColonelId());
            Map<Long, BigDecimal> settleMap = CollectionUtils.convertMap(settleList, TrdSettle::getSupplierOrderDtlId, TrdSettle::getSettleAmt);

            // 渲染门店信息数据
            BranchDTO branchDTO = trdCacheService.getBranchDTO(order.getBranchId());
            if (ToolUtil.isNotEmpty(branchDTO)) {
                order.setBranchName(branchDTO.getBranchName())
                        .setLongitude(branchDTO.getLongitude())
                        .setLatitude(branchDTO.getLatitude())
                        .setBranchAddr(branchDTO.getBranchAddr())
                ;
            }

            // 渲染入驻商订单信息数据
            order.getSupplierOrderList().forEach(supplierOrder -> {

                SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(supplierOrder.getSupplierOrderId());
                if (ToolUtil.isNotEmpty(supplierDTO)) {
                    supplierOrder.setSupplierName(supplierDTO.getSupplierName());

                }
                supplierOrder.setSubAmt(BigDecimal.ZERO);
                // 渲染入驻商订单明细信息数据
                supplierOrder.getSupplierOrderDtlDTOList().forEach(orderDtl -> {
                    order.setPayAmt(order.getPayAmt().add(orderDtl.getTotalAmt()))
                            .setOrderAmt(order.getOrderAmt().add(orderDtl.getTotalAmt()).add(orderDtl.getDiscountAmt()))
                            .setDiscountAmt(order.getDiscountAmt().add(orderDtl.getDiscountAmt()))
                    ;
                    supplierOrder.setSubAmt(supplierOrder.getSubAmt().add(orderDtl.getTotalAmt()));

                    SpuDTO spuDTO = trdCacheService.getSpuDTO(orderDtl.getSpuId());
                    if (ToolUtil.isNotEmpty(spuDTO)) {
                        orderDtl.setThumb(spuDTO.getThumb())
                                .setThumbVideo(spuDTO.getThumbVideo())
                                .setSpuNo(spuDTO.getSpuNo())
                                .setSpuName(spuDTO.getSpuName())
                        ;
                    }

                    SkuDTO skuDTO = trdCacheService.getSkuDTO(orderDtl.getSkuId());
                    if (ToolUtil.isNotEmpty(skuDTO)) {
                        orderDtl.setItemBarcode(skuDTO.getBarcode());
                    }

                    if (settleMap.containsKey(orderDtl.getSupplierOrderDtlId())) {
                        orderDtl.setSettleAmt(settleMap.get(orderDtl.getSupplierOrderDtlId()));
                    }
                });

                // 查询订单物流信息
                supplierOrder.setOrderDeliveryDTOList(getSupplierOrderDeliveryLog(
                        supplierOrder.getSupplierId(),
                        supplierOrder.getSupplierOrderDtlDTOList().get(0).getSupplierOrderDtlId(),
                        supplierOrder.getSupplierOrderNo())
                );

                // 入驻商订单详情组装快递信息
                getTrdOrderRespDTOAssembleExpressResult(supplierOrder);
            });
        });
        return list;
    }

    @Override
    public List<TrdColonelAppOrderDetailRespVO> getMemColoneAppOrderDetail(Long orderId) {
        List<TrdSupplierOrderDtl> supplierOrderDtls =  trdSupplierOrderDtlMapper.selectListByOrderId(orderId);
        // 查询订单结算金额
        List<TrdSettle> settleList = trdSettleService.getTrdSettleListByOrderIdAndMerchant(orderId, MerchantTypeEnum.COLONEL.getType(), SecurityUtils.getLoginUser().getColonelId());
        Map<Long, BigDecimal> settleMap = CollectionUtils.convertMap(settleList, TrdSettle::getSupplierOrderDtlId,TrdSettle::getSettleAmt);
        List<TrdColonelAppOrderDetailRespVO> result = new ArrayList<>();
        result =HutoolBeanUtils.toBean(supplierOrderDtls, TrdColonelAppOrderDetailRespVO.class);
        result.forEach(detail ->{
            SpuDTO spu = trdCacheService.getSpuDTO(detail.getSpuId());
            SkuDTO sku =trdCacheService.getSkuDTO(detail.getSkuId());
            SupplierDTO supplier = trdCacheService.getSupplierDTO(detail.getSupplierId());
            detail.setSpuName(spu.getSpuName());
            detail.setSpuNo(spu.getSpuNo());
            detail.setBarcode(sku.getBarcode());
            if (ToolUtil.isNotEmpty(supplier)) {
                detail.setSupplierName(supplier.getSupplierName());
            }
            if (settleMap.containsKey(detail.getSupplierOrderDtlId())) {
                detail.setSettleAmt(settleMap.get(detail.getSupplierOrderDtlId()));
            }
        });
        return result;
    }

    /**
     * @Description: 获取门店商品下单信息
     * @Author: liuxingyu
     * @Date: 2024/4/29 10:54
     */
    @Override
    public List<EsStoreProduct> getListByBranch(List<EsStoreProduct> esStoreProductList) {
        //如果压力过大,舍弃丢失的数据 只获取本次下单的商品信息则skuIdList
        ArrayList<EsStoreProduct> result = new ArrayList<>();
        esStoreProductList.stream()
                .filter(item -> Objects.nonNull(item.getUnitSize()))
                .collect(Collectors.groupingBy(EsStoreProduct::getUnitSize))
                .forEach((unitSize, skuList) -> {
                    List<EsStoreProduct> list = trdOrderMapper.getListByBranch(
                            esStoreProductList.get(0).getBranchId(),
                            skuList.stream().map(EsStoreProduct::getSkuId).distinct().collect(Collectors.toList()),
                            unitSize
                    );
                    if (!list.isEmpty()) {
                        result.addAll(list);
                    }
                });
        return result;
    }

    /**
    * @Description: 获取常购商品列表
    * @Author: liuxingyu
    * @Date: 2024/5/7 14:42
    */
    @Override
    public PageResult<StoreProductRespVO> getEsStoreProductList(StoreProductRequest storeProductRequest) {
        StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setBranchId(storeProductRequest.getBranchId());
        storeProductDTO.setCatgoryId(storeProductRequest.getCatgoryId());
        storeProductDTO.setCatgoryFirstId(storeProductRequest.getCatgoryFirstId());
        if (ObjectUtil.notEqual(storeProductRequest.getType(),NumberPool.INT_FOUR)){
            storeProductDTO.setSortType(storeProductRequest.getType());
            storeProductDTO.setOrderBy(storeProductRequest.getOrderBy());
        }
        PageResult<EsStoreProduct> esStoreProductList = esProductService.getEsStoreProductList(storeProductDTO, storeProductRequest);
        return HutoolBeanUtils.toBean(esStoreProductList,StoreProductRespVO.class);
    }

    @Override
    public List<EsStoreProduct> getEsListByOrderId(Long orderId) {
        return trdOrderMapper.getEsListByOrderId(orderId);
    }

    @Override
    public List<CustomApiDetail> getCustomApiDetail(String apiNo) {
        return trdOrderMapper.getCustomApiDetail(apiNo);
    }

    @Override
    public CustomApiMaster getCustomApiMaster(CustomApiMaster customApiMaster) {
        return trdOrderMapper.getCustomApiMaster(customApiMaster);
    }

    @Override
    public BigDecimal getSaleAmount(TrdColonelAppOrderListPageReqVO reqVO) {
        return trdOrderMapper.getSaleAmount(reqVO);
    }

    @Override
    public List<CouponExtendTotalVO> getCouponExtendTotal(List<Long> couponTemplateIdList) {
        return trdSupplierOrderDtlMapper.selectCouponExtendTotal(couponTemplateIdList);
    }

    @Override
    public List<CouponCustomerOrderUseTotalDTO> getCouponCustomerUseTotal(Long couponTemplateId, List<Long> customerIdList) {
        return trdSupplierOrderDtlMapper.getCouponCustomerOrderUseTotal(couponTemplateId, customerIdList);
    }

    @Override
    public PageResult<CouponCustomerOrderUseTotalDTO> getCouponCustomerOrderUseDetailTotal(TrdOrderPromitionReportPageReqVO pageReqVO) {
        com.github.pagehelper.Page<CouponCustomerOrderUseTotalDTO> page = PageUtils.startPage(pageReqVO);

        if(ToolUtil.isNotEmpty(pageReqVO.getCustomerName())){
            List<Long> branchIds =branchApi.getBranchIdListByBranchName(pageReqVO.getCustomerName(), SecurityUtils.getLoginUser().getSysCode()).getCheckedData();
            if (ToolUtil.isNotEmpty(branchIds)){
                pageReqVO.setCustomerIds(branchIds);
            }else{
                // 没有找到对应的门店 则塞入-1
                pageReqVO.setCustomerIds(Collections.singletonList(-1L));
            }
        }
        List<CouponCustomerOrderUseTotalDTO> list = trdSupplierOrderDtlMapper.getCouponCustomerOrderUseDetailTotal(pageReqVO);
        list.forEach(item -> {
            item.setCouponTemplateId(pageReqVO.getCouponTemplateId());
            BranchDTO branchDTO = trdCacheService.getBranchDTO(item.getCustomerId());
            CouponDTO couponDTO =couponApi.getCouponByOrderNoAndCouponTemplateId(item.getOrderNo(), item.getCouponTemplateId()).getCheckedData();
            if (ToolUtil.isNotEmpty(branchDTO)) {
                item.setCustomerName(branchDTO.getBranchName());
            }
            if (ToolUtil.isNotEmpty(couponDTO)) {
                item.setCouponCreateTime(couponDTO.getCreateTime());
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    public PageResult<CouponCustomerOrderUseDetilDTO> getCouponCustomerOrderUseDetail(TrdOrderPromitionReportPageReqVO pageReqVO) {
        com.github.pagehelper.Page<CouponCustomerOrderUseDetilDTO> page = PageUtils.startPage(pageReqVO);
        List<CouponCustomerOrderUseDetilDTO> list = trdSupplierOrderDtlMapper.getCouponCustomerOrderUseDetail(pageReqVO);
        list.forEach(item -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(item.getSpuId());
            BrandDTO brandDTO = trdCacheService.getBrandDTO(spuDTO.getBrandId());
            CatgoryDTO catgoryDTO = trdCacheService.getCatgoryDTO(spuDTO.getCatgoryId());
            if (ToolUtil.isNotEmpty(spuDTO)) {
                item.setSpuNo(spuDTO.getSpuNo())
                        .setSpuUrl(spuDTO.getThumb())
                        .setSpuName(spuDTO.getSpuName())
                        .setBrandId(spuDTO.getBrandId())
                        .setBrandName(ToolUtil.isEmpty(brandDTO) ? "" : brandDTO.getBrandName())
                        .setCatgoryId(catgoryDTO.getCatgoryId())
                        .setCatgoryName(catgoryDTO.getCatgoryName());
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    public List<ActivityOrderTotalDTO> getPrmActivityByActivityIds(List<Long> activityIds) {
        return trdSupplierOrderDtlMapper.getPrmActivityByActivityIds(activityIds);
    }

    @Override
    public PageResult<ActivityCustomerOrderUseTotalDTO> getActivityCustomerTotal(TrdOrderPromitionReportPageReqVO pageReqVO) {
        com.github.pagehelper.Page<ActivityCustomerOrderUseTotalDTO> page = PageUtils.startPage(pageReqVO);
        List<ActivityCustomerOrderUseTotalDTO> list = trdSupplierOrderDtlMapper.getActivityCustomerTotal(pageReqVO);
        list.forEach(item -> {
            item.setActivityId(pageReqVO.getActivityId());
            BranchDTO branchDTO = trdCacheService.getBranchDTO(item.getCustomerId());
            if (ToolUtil.isNotEmpty(branchDTO)) {
                item.setCustomerName(branchDTO.getBranchName());
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    public PageResult<ActivityCustomerOrderUseTotalDTO> getActivityCustomerOrderTotal(TrdOrderPromitionReportPageReqVO pageReqVO) {
        com.github.pagehelper.Page<ActivityCustomerOrderUseTotalDTO> page = PageUtils.startPage(pageReqVO);
        List<ActivityCustomerOrderUseTotalDTO> list = trdSupplierOrderDtlMapper.getActivityCustomerOrderTotal(pageReqVO);
        list.forEach(item -> {
            item.setActivityId(pageReqVO.getActivityId());
            BranchDTO branchDTO = trdCacheService.getBranchDTO(item.getCustomerId());
            if (ToolUtil.isNotEmpty(branchDTO)) {
                item.setCustomerName(branchDTO.getBranchName());
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    public PageResult<ActivityCustomerOrderUseDetailDTO> getActivityCustomerOrderDetail(TrdOrderPromitionReportPageReqVO pageReqVO) {
        com.github.pagehelper.Page<ActivityCustomerOrderUseDetailDTO> page = PageUtils.startPage(pageReqVO);
        List<ActivityCustomerOrderUseDetailDTO> list = trdSupplierOrderDtlMapper.getActivityCustomerOrderDetail(pageReqVO);
        list.forEach(item -> {
            item.setActivityId(pageReqVO.getActivityId());
            SpuDTO spuDTO = trdCacheService.getSpuDTO(item.getSpuId());
            BrandDTO brandDTO = trdCacheService.getBrandDTO(spuDTO.getBrandId());
            CatgoryDTO catgoryDTO = trdCacheService.getCatgoryDTO(spuDTO.getCatgoryId());
            if (ToolUtil.isNotEmpty(spuDTO)) {
                item.setSpuNo(spuDTO.getSpuNo())
                        .setSpuUrl(spuDTO.getThumb())
                        .setSpuName(spuDTO.getSpuName())
                        .setBrandId(spuDTO.getBrandId())
                        .setBrandName(ToolUtil.isEmpty(brandDTO) ? "" : brandDTO.getBrandName())
                        .setCatgoryId(catgoryDTO.getCatgoryId())
                        .setCatgoryName(catgoryDTO.getCatgoryName());
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    public TrdOrder getTrdOrderByOrderNo(String orderNo) {
        return trdOrderMapper.getOrderByOrderNo(new TrdOrderPageReqVO(orderNo));
    }

    @Override
    public void hdfkOrderPaySuccessUpdateStatus(Set<Long> supplierOrderDtlIdList, String payPlatform, Long hdfkPayId, String hdfkPayWay) {
        Set<Long> orderIdSet = new HashSet<>();
        Set<String> supplierOrderNos = new HashSet<>();
        List<TrdSupplierOrderDtl> supplierOrderDtlList = new ArrayList<>();

        // 更新订单明细支付状态
        supplierOrderDtlIdList.forEach(orderDtlId -> {
           TrdSupplierOrderDtl orderDtl = trdSupplierOrderDtlMapper.selectById(orderDtlId);
            orderIdSet.add(orderDtl.getOrderId());
            supplierOrderNos.add(orderDtl.getSupplierOrderNo());
            supplierOrderDtlList.add(orderDtl);

            orderDtl.setPlatform(payPlatform);
            orderDtl.setPayState(PayStateEnum.PAY_ALREADY_HDFK.getCode());
            orderDtl.setHdfkPayId(hdfkPayId);
            trdSupplierOrderDtlMapper.updateById(orderDtl);

//            trdOrderHandlerServices.forEach(handler -> handler.hdfkOrderDtlPaySuccessUpdateStatus(orderDtl));
        });

        if (Objects.equals(hdfkPayWay, PayWayEnum.ONLINE.getPayWay())) { // 当支付方式为线上支付时，才需要重新计算订单中的手续费金额
            trdPriceService.recalculateHdfkOrderReceiveSuccessPayFee(hdfkPayId, supplierOrderDtlList);
        }


        // 因收款单可能会出现合单支付的情况，故做循环发送收款单处理
        supplierOrderNos.forEach(supplierOrderNo->{
            /**
             * B2B发送 货到收款支付成功信息 推送ERP
             */
            tradeMqProducer.sendSyncDataReceiptEvent(
                    new SyncReceiptSendDTO()
                            .setSheetType(SheetTypeConstants.XSS)
                            .setSupplierSheetNo(supplierOrderNo)
//                        .setSupplierDtlIdList(new ArrayList<>(supplierOrderDtlIdList))
            );
        });

        // 更新总订单支付状态
        orderIdSet.stream().forEach(orderId -> {
            trdOrderHandlerServices.forEach(handler -> handler.hdfkOrderPaySuccessUpdateStatus(trdSupplierOrderDtlMapper.selectListByOrderId(orderId)));
        });
    }

    @Override
    public List<ColonelOrderDaySettleDTO> getColonelOrderBusinessSettle(ColonelOrderDaySettleVO reqVO) {
        return trdOrderMapper.getColonelOrderBusinessSettle(reqVO);
    }

    @Override
    public List<EsBranchOrderSalesRespDTO> getBranchOrderSales(EsBranchOrderSalesReqVO reqVO) {
        return trdOrderMapper.getBranchOrderSalesByParams(reqVO);
    }

    @Override
    public List<Long> getOrderAndAfterSettleIdBySheetNo(String sheetNo) {
        return trdOrderMapper.getOrderAndAfterSettleIdBySheetNo(sheetNo);
    }

    @Override
    public List<SupplierOrderDtlInfoExportVO> getSupplierOrderDtlInfoExport(DcOrderPageReqApiVO dcOrderPageReqVO) {
        log.info("订单导出参数getSupplierOrderDtlInfoExport={}",JsonUtils.toJsonString(dcOrderPageReqVO));
        if (ToolUtil.isNotEmpty(dcOrderPageReqVO.getEndTime())) {
            dcOrderPageReqVO.setEndTime(DateUtil.endOfDay(dcOrderPageReqVO.getEndTime()));
        }
        //处理产品集合
        List<Long> spuIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dcOrderPageReqVO.getProductList())) {
            spuIds = dcOrderPageReqVO.getProductList().stream().map(DcOrderProductReqApiVO::getSpuId).distinct().collect(Collectors.toList());
            skuIds = dcOrderPageReqVO.getProductList().stream().map(DcOrderProductReqApiVO::getSkuId).distinct().collect(Collectors.toList());
        }



        List<SupplierOrderDtlInfoExportVO> list = trdOrderMapper.getSupplierOrderDtlInfoExport(dcOrderPageReqVO, spuIds, skuIds);
        if (StringUtils.isEmpty(list)){
            return list;
        }
        // 获取单位字典缓存
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));
        //获取支付方式字典信息
        List<SysDictData> payWayList = DictUtils.getDictCache("sys_order_pay_way");
        Map<String, String> payWayMap = payWayList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        //入驻商订单状态
        List<SysDictData> supplierOrderStatusList = DictUtils.getDictCache(DictTypeConstants.SUPPLIER_ORDER_STATUS);
        Map<String, String> supplierOrderStatusMap = supplierOrderStatusList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        //支付状态
        List<SysDictData> payStatusList = DictUtils.getDictCache("sys_pay_status");
        Map<String, String> payStatusMap = payStatusList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<String> orderNos = list.stream().map(t -> t.getOrderNo()).collect(Collectors.toList());
        CommonResult<List<PayFlowDTO>> byOrdersPayFlow = payFlowApi.getByOrdersPayFlow(orderNos);
        if (byOrdersPayFlow.isError()){
            throw new ServiceException(String.format("订单查询支付流水数据失败:{}",JsonUtils.toJsonString(orderNos)));
        }
        log.info("订单查询支付流水数据成功:{}",JsonUtils.toJsonString(orderNos));
        List<PayFlowDTO> payFlowDTOS = byOrdersPayFlow.getCheckedData();


        list.forEach(item -> {
            SpuDTO spuDTO = trdCacheService.getSpuDTO(item.getSpuId());
            SkuDTO skuDTO = trdCacheService.getSkuDTO(item.getSkuId());
            String barcode = "";
            if (UnitTypeEnum.L(item.getOrderUnitType())) { // 大
                barcode = skuDTO.getLargeBarcode();
            } else if (UnitTypeEnum.M(item.getOrderUnitType())) {
                barcode = skuDTO.getMidBarcode();
            } else if (UnitTypeEnum.S(item.getOrderUnitType())) {
                barcode = skuDTO.getBarcode();
            }

            item.setSpuName(spuDTO.getSpuName())
                    .setSpecName(PropertyAndValDTO.getProperties(skuDTO.getProperties()))
                    .setBarcode(barcode)
                    .setSaleUnit(unitMap.containsKey(item.getOrderUnit() + "") ? unitMap.get(item.getOrderUnit() + "").getDictLabel() : "")
            ;

            //状态
            if(Objects.nonNull(dcOrderPageReqVO) && Objects.nonNull(dcOrderPageReqVO.getOrderType()) && dcOrderPageReqVO.getOrderType() == 99){
                item.setErrorState("异常");
                String orderDeliveryOvertimeDate = DateUtils.timeDistance(item.getPayTime(),new Date());
                item.setErrorMemo("超时未发货"+orderDeliveryOvertimeDate);
            }
            item.setDeliveryState(item.getDelivery());
            item.setPayTotalAmt(item.getDemandAmt());


            //打印状态
            if ("0".equals(item.getPrintState())){
                item.setPrintState("未打印");
            }else if ("1".equals(item.getPrintState())){
                item.setPrintState("已打印");
            }
            //支付状态
            if (StringUtils.isNotEmpty(item.getPayState())){
                item.setPayState(payStatusMap.get(item.getPayState()));
            }

            //同步标识
            if (String.valueOf(OpenApiConstants.ORDER_SYNC_FLAG_0).equals(item.getPushStatus())){
                item.setPushStatus("未推送");
            }else if (String.valueOf(OpenApiConstants.ORDER_SYNC_FLAG_1).equals(item.getPushStatus())){
                item.setPushStatus("已推送");
            }else if (String.valueOf(OpenApiConstants.ORDER_SYNC_FLAG_2).equals(item.getPushStatus())){
                item.setPushStatus("已接收");
            }

            //区域城市
            AreaDTO threeArea = trdCacheService.getByAreaId(item.getAreaId());
//            AreaDTO threeArea = areaApi.getAreaByAreaId(item.getAreaId()).getCheckedData();
            if (Objects.nonNull(threeArea)) {
                assignmentArea(item,threeArea);
                if (!"1".equals(String.valueOf(threeArea.getLevel()))&&threeArea.getPid()!=null){
                    AreaDTO twoArea = trdCacheService.getByAreaId(threeArea.getPid());
                    if (Objects.nonNull(twoArea)) {
                        assignmentArea(item,twoArea);
                        if (!"1".equals(String.valueOf(twoArea.getLevel()))&&twoArea.getPid()!=null){
                            AreaDTO oneArea = trdCacheService.getByAreaId(twoArea.getPid());
                            if (Objects.nonNull(oneArea)) {
                                assignmentArea(item, oneArea);
                            }
                        }

                    }
                }
            }
            BranchDTO branchDTO = trdCacheService.getBranchDTO(item.getBranchId());
            //地址 省市县
            if (Objects.nonNull(branchDTO.getThreeAreaCityId())) {
                SysAreaCityRespVO three = areaCityApi.getById(branchDTO.getThreeAreaCityId()).getCheckedData();
                if (Objects.nonNull(three)) {
                    SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                    SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();

                    item.setCounty(three.getName());
                    item.setCity(second.getName());
                    item.setProvince(first.getName());
                }
            }

            //入驻商
//            List<Long> branchIds = branchApi.getSupplierByBranchId(item.getBranchId()).getCheckedData();
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(item.getSupplierId());
            if (Objects.nonNull(supplierDTO)) {
                item.setSupplierCode(supplierDTO.getSupplierCode());
                item.setSupplierName(supplierDTO.getSupplierName());
            }

            //运营商
//            DcDTO dcDTO = dcApi.getDcById(item.getDcId()).getCheckedData();
            DcDTO dcDTO = trdCacheService.getDcDTO(item.getDcId());
            if (Objects.nonNull(dcDTO)) {
                item.setDcCode(dcDTO.getDcCode());
                item.setDcName(dcDTO.getDcName());
            }

            //门店
            item.setBranchName(branchDTO.getBranchName());
            item.setBranchNo(branchDTO.getBranchNo());
            //订单所属的下单门店地址
            item.setBranchAddr(branchDTO.getBranchAddr());
            //经纬度
            item.setLongitude(branchDTO.getLongitude());
            item.setLatitude(branchDTO.getLatitude());
            //渠道
            if (Objects.nonNull(branchDTO.getChannelId())){
                ChannelDTO channelDTO = trdCacheService.getChannelDTO(branchDTO.getChannelId());
                if (Objects.nonNull(channelDTO)){
                    item.setChannelName(channelDTO.getChannelName());
                }
            }

            //订单所属的销售经理
            if (ObjectUtil.isNotNull(item.getPcolonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(item.getPcolonelId());
                if (ObjectUtil.isNotNull(colonelDTO)) {
                    item.setPcolonelName(colonelDTO.getColonelName());
                }
            }

            //业务员
            if (Objects.nonNull(item.getColonelId())){
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(item.getColonelId());
                if (Objects.nonNull(colonelDTO)){
                    item.setColonelName(colonelDTO.getColonelName());
                }
            }


            //支付方式
            item.setPayWay(payWayMap.get(item.getPayWay()));
            //入驻商订单状态
            if (StringUtils.isNotEmpty(item.getDeliveryState())){
                item.setDeliveryState(supplierOrderStatusMap.get(item.getDeliveryState()));
            }
            //订单类型
            String orderType = item.getOrderType();
            if ("0".equals(item.getOrderType())){
                item.setOrderType("全国");
            }else if ("1".equals(item.getOrderType())){
                item.setOrderType("本地");
            }

            //订单商品的辅助商品编号
            item.setSpuNo(spuDTO.getSpuNo());
            item.setAuxiliarySpuNo(spuDTO.getAuxiliarySpuNo());

            //品牌
            if (Objects.nonNull(spuDTO.getBrandId())){
                BrandDTO brandDTO = trdCacheService.getBrandDTO(spuDTO.getBrandId());
                if (Objects.nonNull(brandDTO)){
                    item.setBrandName(brandDTO.getBrandName());
                }
            }

            //平台商管理分类
            if (Objects.nonNull(spuDTO.getCatgoryId())){
                CatgoryDTO threeCategory = trdCacheService.getCatgoryDTO(spuDTO.getCatgoryId());
                if (Objects.nonNull(threeCategory)) {
                    assignmentCategory(item,threeCategory);
                    if (!"1".equals(String.valueOf(threeCategory.getLevel()))){
                        CatgoryDTO twoCategory = trdCacheService.getCatgoryDTO(threeCategory.getPid());
                        if (Objects.nonNull(twoCategory)){
                            assignmentCategory(item,twoCategory);
                            if (!"1".equals(String.valueOf(twoCategory.getLevel()))){
                                CatgoryDTO oneCategory = trdCacheService.getCatgoryDTO(twoCategory.getPid());
                                if (Objects.nonNull(oneCategory)){
                                    assignmentCategory(item,oneCategory);
                                }
                            }
                        }
                    }
                }
            }

            //分润利润  佣金
            TrdSupplierOrderSettle trdSupplierOrderSettle = trdSupplierOrderSettleService.getTrdSupplierOrderSettle(item.getSupplierOrderId());
            if (Objects.nonNull(trdSupplierOrderSettle)){
                item.setPartnerRate(trdSupplierOrderSettle.getPartnerRate());
                item.setDcRate(trdSupplierOrderSettle.getDcRate());
                item.setColonel1Rate(trdSupplierOrderSettle.getColonel1Rate());
                item.setColonel2Rate(trdSupplierOrderSettle.getColonel2Rate());
                item.setOrderAmtRate(item.getPartnerRate().add(item.getDcRate()).add(item.getColonel1Rate()).add(item.getColonel2Rate()));//订单商品的佣金总扣点

                item.setPartnerAmt(trdSupplierOrderSettle.getPartnerAmt());
                item.setDcAmt(trdSupplierOrderSettle.getDcAmt());
                item.setColonel1Amt(trdSupplierOrderSettle.getColonel1Amt());
                item.setColonel2Amt(trdSupplierOrderSettle.getColonel2Amt());
            }

            //的平台商展示城市类别
            if ("0".equals(orderType)){
                //全国类别
                if (Objects.nonNull(item.getSupplierItemId())){
                    SupplierItemDTO supplierItemDTO = supplierItemApi.getBySupplierItemId(item.getSupplierItemId()).getCheckedData();
                    if (Objects.nonNull(supplierItemDTO)) {
                        SaleClassDTO threeSaleClass = saleClassApi.getSaleClassBySaleClassId(supplierItemDTO.getSaleClassId()).getCheckedData();
                        if (Objects.nonNull(threeSaleClass)){
                            item.setThreeAreaClass(threeSaleClass.getName());
                            SaleClassDTO twoSaleClass = saleClassApi.getSaleClassBySaleClassId(threeSaleClass.getPid()).getCheckedData();
                            if (Objects.nonNull(twoSaleClass)){
                                item.setTwoAreaClass(twoSaleClass.getName());
                                SaleClassDTO oneSaleClass = saleClassApi.getSaleClassBySaleClassId(twoSaleClass.getPid()).getCheckedData();
                                if (Objects.nonNull(oneSaleClass)){
                                    item.setOneAreaClass(oneSaleClass.getName());
                                }
                            }
                        }
                    }
                }

            }else if ("1".equals(orderType)){
                //城市类别
                if (Objects.nonNull(item.getAreaItemId())){
                    AreaItemDTO areaItemDTO = areaItemApi.getAreaItemId(item.getAreaItemId()).getCheckedData();
                    if (Objects.nonNull(areaItemDTO)&&Objects.nonNull(areaItemDTO.getAreaClassId())) {
                        AreaClassDTO threeAreaClass = areaClassApi.getAreaClassByAreaClassId(areaItemDTO.getAreaClassId()).getCheckedData();
                        if (Objects.nonNull(threeAreaClass)){
                            assignmentAreaClass(item,threeAreaClass);
                            if (!"1".equals(threeAreaClass.getLevel())){
                                AreaClassDTO twoAreaClass = areaClassApi.getAreaClassByAreaClassId(threeAreaClass.getPid()).getCheckedData();
                                if (Objects.nonNull(twoAreaClass)){
                                    assignmentAreaClass(item,twoAreaClass);
                                    if (!"1".equals(twoAreaClass.getLevel())){
                                        AreaClassDTO oneAreaClass = areaClassApi.getAreaClassByAreaClassId(twoAreaClass.getPid()).getCheckedData();
                                        if (Objects.nonNull(oneAreaClass)){
                                            assignmentAreaClass(item,oneAreaClass);
                                        }
                                    }
                                }
                            }
                        }

                    }
                }

            }

            //优惠信息
            List<TrdOrderDiscountDtl> trdOrderDiscountDtls = trdOrderDiscountDtlMapper.selectListByOrderId(item.getOrderId());
            StringBuffer discount = new StringBuffer("");
            StringBuffer discountRoll = new StringBuffer("");
            StringBuffer getDiscountRollWay = new StringBuffer("");
            trdOrderDiscountDtls.forEach(t -> {
                if (StringUtils.isNotEmpty(discount.toString())){
                    discount.append(";");
                }
                discount.append(TrdDiscountTypeEnum.getDiscountTypeName(t.getDiscountType()));

                if (TrdDiscountTypeEnum.COUPON.getType().equals(t.getDiscountType())){
                    CouponDTO couponDTO = couponApi.getPrmCoupon(t.getDiscountId()).getCheckedData();
                    if (Objects.nonNull(couponDTO)) {
                        //适用范围
                        if (StringUtils.isNotEmpty(discountRoll.toString())){
                            discountRoll.append(";");
                        }
                        //discountRoll.append(CouponSpuScopeEnum.formValue(couponDTO.getSpuScope()).getName());
                        discountRoll.append(t.getDiscountId()+"");
                        //领取方式
                        if (StringUtils.isNotEmpty(getDiscountRollWay.toString())){
                            getDiscountRollWay.append(";");
                        }
                        getDiscountRollWay.append(CouponReceiveType.getReceiveTypeName(couponDTO.getReceiveType()));

                    }
                }

            });
            // 针对discountRoll做去重处理
            String uniqueDiscountRoll = discountRoll.toString();
            if (StringUtils.isNotEmpty(uniqueDiscountRoll)) {
                uniqueDiscountRoll = StringUtils.join(new HashSet<>(Arrays.asList(uniqueDiscountRoll.split(";"))), ";");
            }
            // 针对getDiscountRollWay做去重处理
            String uniqueGetDiscountRollWay = getDiscountRollWay.toString();
            if (StringUtils.isNotEmpty(uniqueGetDiscountRollWay)) {
                uniqueGetDiscountRollWay = StringUtils.join(new HashSet<>(Arrays.asList(uniqueGetDiscountRollWay.split(";"))), ";");
            }
            item.setDiscountType(discount.toString());
            item.setDiscountRoll(uniqueDiscountRoll);
            item.setGetDiscountRollWay(uniqueGetDiscountRollWay);

            //支付流水
            List<PayFlowDTO> collect = payFlowDTOS.stream().filter(t -> item.getOrderNo().equals(t.getTradeNo())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(collect)){
                item.setOutTradeNo(collect.get(0).getOutTradeNo());
            }

        });
        return list;
    }

    /**
     * 平台商管理分类赋值
     * @param export
     * @param catgoryDTO
     */
    public void assignmentCategory(SupplierOrderDtlInfoExportVO export,CatgoryDTO catgoryDTO){
        if ("1".equals(String.valueOf(catgoryDTO.getLevel()))){
            export.setOneCategory(catgoryDTO.getCatgoryName());
        }else if ("2".equals(String.valueOf(catgoryDTO.getLevel()))){
            export.setTwoCategory(catgoryDTO.getCatgoryName());
        }else if ("3".equals(String.valueOf(catgoryDTO.getLevel()))){
            export.setThreeCategory(catgoryDTO.getCatgoryName());
        }

    }

    /**
     * 城市区域赋值
     * @param export
     * @param areaDTO
     */
    public void assignmentArea(SupplierOrderDtlInfoExportVO export,AreaDTO areaDTO){
        if ("1".equals(String.valueOf(areaDTO.getLevel()))){
            export.setOneArea(areaDTO.getAreaName());
        }else if ("2".equals(String.valueOf(areaDTO.getLevel()))){
            export.setTwoArea(areaDTO.getAreaName());
        }else if ("3".equals(String.valueOf(areaDTO.getLevel()))){
            export.setThreeArea(areaDTO.getAreaName());
        }

    }

    public void assignmentArea(SupplierOrderExportVO export,AreaDTO areaDTO){
        if ("1".equals(String.valueOf(areaDTO.getLevel()))){
            export.setOneArea(areaDTO.getAreaName());
        }else if ("2".equals(String.valueOf(areaDTO.getLevel()))){
            export.setTwoArea(areaDTO.getAreaName());
        }else if ("3".equals(String.valueOf(areaDTO.getLevel()))){
            export.setThreeArea(areaDTO.getAreaName());
        }

    }

    /**
     * 城市展示分类赋值
     * @param export
     * @param areaClassDTO
     */
    public void assignmentAreaClass(SupplierOrderDtlInfoExportVO export,AreaClassDTO areaClassDTO){
        if ("1".equals(String.valueOf(areaClassDTO.getLevel()))){
            export.setOneAreaClass(areaClassDTO.getAreaClassName());
        }else if ("2".equals(String.valueOf(areaClassDTO.getLevel()))){
            export.setTwoAreaClass(areaClassDTO.getAreaClassName());
        }else if ("3".equals(String.valueOf(areaClassDTO.getLevel()))){
            export.setThreeAreaClass(areaClassDTO.getAreaClassName());
        }

    }


    @Override
    public void editOrderPrintQty(String[] supplierOrderNos) {
        supplierOrderSettleMapper.editOrderPrintQtyBySupplierOrderNos(supplierOrderNos);
    }

    @Override
    public List<HomePagesCurrentSalesDataRespDTO> getHomePagesCurrentSalesData(HomePagesReqVO reqVO) {
        return trdOrderMapper.getHomePagesCurrentSalesData(reqVO);
    }

    @Override
    public List<HomePagesOrderSalesDataRespDTO> getHomePagesOrderSalesData(HomePagesReqVO reqVO) {
        return trdOrderMapper.getHomePagesOrderSalesData(reqVO);
    }

    @Override
    public List<HomePagesSalesTop10DataRespDTO> getHomePagesSalesTop10Data(HomePagesReqVO reqVO) {
        List<HomePagesSalesTop10DataRespDTO> respDTOS = new ArrayList<>();
        switch (reqVO.getSalesType()) {
            case "area":
                respDTOS = trdOrderMapper.getHomePagesAreaSalesTop10Data(reqVO);
                break;
            case "dc":
                respDTOS = trdOrderMapper.getHomePagesDcSalesTop10Data(reqVO);
                break;
            case "item":
                respDTOS = trdOrderMapper.getHomePagesItemSalesTop10Data(reqVO);
                break;
            case "category":
                respDTOS = trdOrderMapper.getHomePagesCategorySalesTop10Data(reqVO);
                break;
            case "supplier":
                respDTOS = trdOrderMapper.getHomePagesSupplierSalesTop10Data(reqVO);
                break;
            case "colonel":
                respDTOS = trdOrderMapper.getHomePagesColonelSalesTop10Data(reqVO);
                break;
            case "branch":
                respDTOS = trdOrderMapper.getHomePagesBranchSalesTop10Data(reqVO);
                break;
        }
        return respDTOS;
    }

    @Override
    public List<Long> checkMemberOrBranchExistsOrderSaleInfo(String type, Long sysCode, List<Long> ids) {
        List<Long> resultIds = trdOrderMapper.checkMemberOrBranchExistsOrderSaleInfo(type, sysCode, ids);
        return ToolUtil.isEmptyReturn(resultIds, new ArrayList<>());
    }

    @Override
    public void updateFrequentByOrder(TrdOrder data) {
        //根据订单ID获取订单明细
        List<EsStoreProduct> esStoreProducts = this.getEsListByOrderId(data.getOrderId());
        if (ObjectUtil.isEmpty(esStoreProducts)) {
            log.error("当前订单ID未获取到订单明细,订单ID:{}", data);
            return;
        }
        //获取门店历史订单记录
        List<EsStoreProduct> listByBranch = this.getListByBranch(esStoreProducts);
        if (ObjectUtil.isEmpty(listByBranch)) {
            log.error("门店下单统计常购商品,订单为空");
            return;
        }
        Map<String, EsStoreProduct> mapBySkuId = listByBranch.stream()
                .collect(
                        Collectors.toMap(x ->
                                        StringUtils.format("{}_{}_{}_{}_{}", x.getBranchId(), x.getSkuId(), x.getUnitSize(), x.getItemType(), Objects.nonNull(x.getAreaItemId()) ? x.getAreaItemId() : x.getSupplierItemId()),
                                Function.identity()
                        ));
        //获取管理分类(只获取一级和三级ID)
        List<CatgoryIdDTO> catgoryIdDTOList = catgoryApi.getCatgoryFirstId().getCheckedData();
        Map<Long, Long> catgoryIdMap = catgoryIdDTOList.stream().collect(Collectors.toMap(CatgoryIdDTO::getCatgoryId, CatgoryIdDTO::getCatgoryFirstId));
        esStoreProducts = esStoreProducts.stream().peek(x -> {
            String key = StringUtils.format("{}_{}_{}_{}_{}", x.getBranchId(), x.getSkuId(), x.getUnitSize(), x.getItemType(), Objects.nonNull(x.getAreaItemId()) ? x.getAreaItemId() : x.getSupplierItemId());
            x.setId(key);
            x.setClickNumberTotal(0L);
            EsStoreProduct matchedProduct = mapBySkuId.get(key);
            if (matchedProduct != null) {
                x.setWithinPurchasedNumberTotal(matchedProduct.getWithinPurchasedNumberTotal());
                x.setWithinPurchasedNumberAvg(new BigDecimal(matchedProduct.getWithinPurchasedNumberTotal()).divide(new BigDecimal(90), 2, RoundingMode.DOWN));
                x.setWithinPurchasedFrequencyTotal(matchedProduct.getWithinPurchasedFrequencyTotal());
                x.setWithinPurchasedFrequencyAvg(new BigDecimal(matchedProduct.getWithinPurchasedFrequencyTotal()).divide(new BigDecimal(90), 2, RoundingMode.DOWN));
                x.setPurchasedFrequencyTotal(matchedProduct.getPurchasedFrequencyTotal());
                x.setPurchasedNumberTotal(matchedProduct.getPurchasedNumberTotal());
                x.setFirstPurchasedDate(matchedProduct.getFirstPurchasedDate());
                x.setRecentlyPurchasedDate(matchedProduct.getRecentlyPurchasedDate());
                x.setSupplierId(matchedProduct.getSupplierId());
            }
            SpuDTO spuDTO = trdCacheService.getSpuDTO(x.getSpuId());
            if (ObjectUtil.isNotNull(spuDTO)) {
                x.setBrandId(spuDTO.getBrandId());
                x.setCatgoryId(spuDTO.getCatgoryId());
                x.setCatgoryFirstId(ObjectUtil.isNull(catgoryIdMap.get(spuDTO.getCatgoryId())) ? null : catgoryIdMap.get(spuDTO.getCatgoryId()));
            }
        }).collect(Collectors.toList());
        //存入es
        esProductService.saveStoreProduct(esStoreProducts);
    }

    @Override
    public List<ColonelAppBranchOrderDTO> getColonelAppBranchOrder(Long branchId) {
        return trdOrderMapper.getColonelAppBranchOrder(branchId);
    }

    @Override
    public List<ColonelAppPageOrderDTO> getColonelAppPageOrder(Long colonelId) {
        return trdOrderMapper.getColonelAppPageOrder(colonelId);
    }

    @Override
    public PageResult<SyncOrderCallDTO> getOrdersWithDelay(SyncOrderPageReqDTO reqVO) {
        PageResult<SyncOrderCallDTO> result = new PageResult<>();
        //获取入驻商编号
        Long supplierId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getMerchantId();
        if (Objects.isNull(supplierId)) {
            throw new ServiceException(StringUtils.format("入驻商编号为空!"));
        }
        //判断是否开启了入驻商推送
        OpensourceDto orderOpenDTO = opensourceApi.getOpensourceByMerchantId(supplierId).getCheckedData();
        if(Objects.isNull(orderOpenDTO)){
            throw new ServiceException(StringUtils.format("入驻商未开启推送!",orderOpenDTO));
        }
        reqVO.setPageSize(100);
        reqVO.setPageNo((reqVO.getPageNo() - 1) * reqVO.getPageSize());
        //默认延时五分钟
        int propertyDelayTimeLevel = 9;
        //延时时间
        //校验是否设置了延时推送时间 未设置则 默认五分钟
        if (ObjectUtil.isNotEmpty(orderOpenDTO.getOrderDelayPushTime())) {
            propertyDelayTimeLevel = orderOpenDTO.getOrderDelayPushTime();
        }
        Integer secondsByLevel = DelayTimeLevelEnum.getSecondsByLevel(propertyDelayTimeLevel);

        //没有查到数据直接返回
        Long total = trdOrderMapper.getSupplierOrderOpenDTOListCount(reqVO,secondsByLevel,supplierId);
        if(total <= NumberPool.LONG_ZERO){
            result.setList(new ArrayList<>());
            result.setTotal(0L);
            return result;
        }
        List<SyncOrderCallDTO> resultData = trdOrderMapper.getSupplierOrderOpenDTOList(reqVO,secondsByLevel,supplierId);
        for (SyncOrderCallDTO resultDatum : resultData) {
            if(ToolUtil.isNotEmpty(resultDatum.getColonelId())){
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(resultDatum.getColonelId());
                //业务员名称
                resultDatum.setColonelName(colonelDTO.getColonelName());
                //业务员用户账户信息
                resultDatum.setColonelPhone(remoteUserService.getSysUser(colonelDTO.getUserId()).getCheckedData().getUserName());
            }
            // 遍历 detailList 以处理多个 SyncOrderDetailCallDTO
            for (SyncOrderDetailCallDTO detail : resultDatum.getDetailList()) {
                SkuDTO skuDTO = trdCacheService.getSkuDTO(detail.getSkuId());
                detail.setErpItemNo(skuDTO.getSourceNo());
            }
        }
        result.setList(resultData);
        result.setTotal(total);
        return result;
    }

    @Override
    public PageResult<SyncAfterOrderCallDTO> getAfterOrdersWithDelay(SyncOrderPageReqDTO reqVO) {
        PageResult<SyncAfterOrderCallDTO> result = new PageResult<>();
        //获取入驻商编号
        Long supplierId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getMerchantId();
        if (Objects.isNull(supplierId)) {
            throw new ServiceException(StringUtils.format("入驻商编号为空!"));
        }
        //判断是否开启了入驻商推送
        OpensourceDto orderOpenDTO = opensourceApi.getOpensourceByMerchantId(supplierId).getCheckedData();
        if(Objects.isNull(orderOpenDTO)){
            throw new ServiceException(StringUtils.format("入驻商未开启推送!",orderOpenDTO));
        }
        reqVO.setPageSize(100);
        reqVO.setPageNo((reqVO.getPageNo() - 1) * reqVO.getPageSize());
        //没有查到数据直接返回
        Long total = trdOrderMapper.getSupplierAfterOrderOpenDTOListCount(reqVO,supplierId);
        if(total <= NumberPool.LONG_ZERO){
            result.setList(new ArrayList<>());
            result.setTotal(0L);
            return result;
        }
        List<SyncAfterOrderCallDTO> resultData = trdOrderMapper.getSupplierAfterOrderOpenDTOList(reqVO,supplierId);
        for (SyncAfterOrderCallDTO resultDatum : resultData) {
            if(ToolUtil.isNotEmpty(resultDatum.getColonelId())){
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(resultDatum.getColonelId());
                //业务员名称
                resultDatum.setColonelName(colonelDTO.getColonelName());
                //业务员用户账户信息
                resultDatum.setColonelPhone(remoteUserService.getSysUser(colonelDTO.getUserId()).getCheckedData().getUserName());
            }
            // 遍历 detailList 以处理多个 SyncAfterOrderDetailCallDTO
            for (SyncAfterOrderDetailCallDTO detail : resultDatum.getDetailList()) {
                SkuDTO skuDTO = trdCacheService.getSkuDTO(detail.getSkuId());
                detail.setErpItemNo(skuDTO.getSourceNo());
            }
        }
        result.setList(resultData);
        result.setTotal(total);
        return result;
    }

    private void validateTrdOrderExists(Long orderId) {
        if (trdOrderMapper.selectById(orderId) == null) {
            throw exception(TRD_ORDER_NOT_EXISTS);
        }
    }

    /**
     * 生成冻结流水或释放冻结流水  TODO 应入驻商充值方案弃用，故此代码注释
     *
     * @param supplierOrders
     * @param supplierOrderSettleSaveVOS
     * @param trdOrder
     * @param operateType                1：冻结  2：释放冻结
     * @return
     */
    private List<AccAccountFlowDTO> createAccountFreezeFlow(List<TrdSupplierOrder> supplierOrders, List<TrdSupplierOrderSettleSaveVO> supplierOrderSettleSaveVOS, TrdOrder trdOrder, String operateType) {
        String payPlatform = trdOrder.getPlatform();
        if (ToolUtil.isEmpty(payPlatform)) {
            PayConfigDTO payConfigDTO = trdCacheService.getPayConfigDTO(trdOrder.getSysCode());
            if (ToolUtil.isEmpty(payConfigDTO) || ToolUtil.isEmpty(payConfigDTO.getStoreOrderPayPlatform())) {
                throw new ServiceException("平台【"+ MallSecurityUtils.getLoginMember().getSysCode()+"】未设置支付平台！");
            }
            payPlatform = payConfigDTO.getStoreOrderPayPlatform();
        }

        if (PayChannelEnum.isB2b(payPlatform)) { // 返回空值同时不抛异常
            return null;
        }

        Map<Long, BigDecimal> profitAmtMap = supplierOrderSettleSaveVOS.stream().collect(Collectors.groupingBy(TrdSupplierOrderSettleSaveVO::getSupplierOrderId,
                Collectors.reducing(
                        BigDecimal.ZERO,
                        TrdSupplierOrderSettleSaveVO::sumProfitAmt,
                        BigDecimal::add
                )));

        PayConfigDTO payConfig = trdCacheService.getPayConfigDTO(trdOrder.getSysCode());
        List<AccAccountFlowDTO> flowDTOS = supplierOrders.stream().map(sOrder -> {
            AccAccountFlowDTO flowDTO = new AccAccountFlowDTO();
            flowDTO.setSysCode(sOrder.getSysCode());
            flowDTO.setBusiType(AccountBusiType.SUPPLIER_CREATE_ORDER.getType());
            flowDTO.setBusiId(sOrder.getOrderId());
            flowDTO.setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField());
            flowDTO.setPlatform(payConfig.getStoreOrderPayPlatform());
            flowDTO.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
            flowDTO.setMerchantId(sOrder.getSupplierId());
            flowDTO.setBusiFrozenAmt(profitAmtMap.get(sOrder.getSupplierOrderId()));

            if (StatusConstants.FREEZE_FLOW_RELEASE.equals(operateType)) { // 释放冻结流水
                flowDTO.setBusiFrozenAmt(flowDTO.getBusiFrozenAmt().negate());
            }
            return flowDTO;
        }).collect(Collectors.toList());
        log.info("发送订单{}流水信息：" + JSON.toJSONString(flowDTOS), trdOrder.getOrderNo());
        return flowDTOS;
    }


    /**
     * 根据入驻商订单获取入驻商订单赠品优惠劵信息
     * @param supplierOrderId
     * @return
     */
    private List<TrdOrderDiscountDTO> getSupplierOrderGiftCouponInfoBySupplierOrderId(Long supplierOrderId) {
        // 查询根据入驻商单查询订单优惠信息表
        List<TrdOrderDiscountDtl> orderDiscountDtlList = trdOrderDiscountDtlMapper.selectListBySupplierId(supplierOrderId);
        if (ToolUtil.isEmpty(orderDiscountDtlList)) {
            return new ArrayList<>();
        }
        // 获取入驻商订单下的所有赠送的优惠劵模板
        return orderDiscountDtlList.stream().map(discount -> {
            TrdOrderDiscountDTO trdOrderDiscountDTO = TrdOrderDiscountDtlConvert.INSTANCE.convert1(trdCacheService.getCouponTemplate(discount.getGiftCouponTemplateId()));
            trdOrderDiscountDTO.setDiscountGiftCount(discount.getGiftQty());
            return trdOrderDiscountDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 入驻商订单查询 操作物流信息
     * @param supplierId 入驻商ID
     * @param supplierOrderDtlId 入驻商明细Id（取第一条）
     * @param supplierOrderNo 入驻商订单编号
     * @return
     */
    private List<TrdOrderDeliveryDTO> getSupplierOrderDeliveryLog(Long supplierId, Long supplierOrderDtlId, String supplierOrderNo){
        //获取入驻商配置
        OpensourceDto opensourceDto = trdCacheService.getOpensourceByMerchantId(supplierId);
        List<TrdOrderDeliveryDTO> orderDeliveryList = new ArrayList<>();

        // 获取入驻商配置为空 或者 商城显示物流信息 为 1 时进入
        if (ToolUtil.isEmpty(opensourceDto) || ToolUtil.isEmpty(opensourceDto.getLogisticsInfo()) || opensourceDto.getLogisticsInfo().equals("1")) {
            // 获取入驻商订单操作日志记录
            List<TrdOrderLog> orderLogList = orderLogMapper.selectListBySupplierOrderDtlId(supplierOrderDtlId);
            orderDeliveryList = orderLogList.stream().map(orderLog -> {
                DeliveryStatusEnum deliveryStatusEnum = DeliveryStatusEnum.getDeliveryStatus(orderLog.getAfterState());
                return new TrdOrderDeliveryDTO().setCreateTime(orderLog.getCreateTime())
                        .setState(orderLog.getAfterState())
                        .setStatus(deliveryStatusEnum.getName())
                        .setDescribe(deliveryStatusEnum.getContent())
                        .setSort(deliveryStatusEnum.getSort());
            }).sorted(Comparator.comparing(TrdOrderDeliveryDTO::getSort, Comparator.nullsFirst(Integer::compare))).collect(Collectors.toList());
        } else {
          List<TrdExpressStatus> trdExpressStatusList = trdExpressStatusMapper.selectBySupplierOrderNo(supplierOrderNo);
            orderDeliveryList = trdExpressStatusList.stream().map(orderLog -> {
                ReceiveLogisticsStatusEnum statusEnum = ReceiveLogisticsStatusEnum.getReceiveLogisticsStatus(orderLog.getLogisticsStatus());
                return new TrdOrderDeliveryDTO().setCreateTime(orderLog.getCreateTime())
                        .setState(orderLog.getLogisticsStatus().longValue())
                        .setStatus(statusEnum.getName())
                        .setDescribe(statusEnum.getName())
                        .setSort(statusEnum.getCode());
            }).sorted(Comparator.comparing(TrdOrderDeliveryDTO::getSort, Comparator.nullsFirst(Integer::compare))).collect(Collectors.toList());
        }
        return orderDeliveryList;
    }


    /**
     * 查询返回订单信息组装
     * @param respVo
     */
    public void getTrdOrderRespDTOAssembleResult(TrdOrderRespDTO respVo){
        BranchDTO branch = trdCacheService.getBranchDTO(respVo.getBranchId());
        ColonelDTO colonel = new ColonelDTO();
        if (ToolUtil.isNotEmpty(respVo.getColonelId())) {
            colonel = trdCacheService.getColonelDTO(respVo.getColonelId());
        }
        Map<Long, BigDecimal> settleMap;
        if (ToolUtil.isNotEmpty(SecurityUtils.getLoginUser()) && ToolUtil.isNotEmpty(SecurityUtils.getLoginUser().getColonelId())) {
            List<TrdSettle> settleList = trdSettleService.getTrdSettleListByOrderIdAndMerchant(respVo.getOrderId(),MerchantTypeEnum.COLONEL.getType(), SecurityUtils.getLoginUser().getColonelId());
            settleMap = CollectionUtils.convertMap(settleList, TrdSettle::getSupplierOrderDtlId,TrdSettle::getSettleAmt);
        } else {
            settleMap = new HashMap<>();
        }

        respVo.setBranchName(branch.getBranchName())
                .setBranchAddr(branch.getBranchAddr())
                .setLatitude(branch.getLatitude())
                .setLongitude(branch.getLongitude())
                .setContactName(branch.getContactName())
                .setContactPhone(branch.getContactPhone())
                .setColonelName(colonel.getColonelName())
                .setColonelAvatarImages(colonel.getAvatarImages())
                .setOrderAmt(BigDecimal.ZERO)
                .setPayAmt(BigDecimal.ZERO)
                .setDiscountAmt(BigDecimal.ZERO)
                .setLifecycleStage(branch.getLifecycleStage());

        respVo.getSupplierOrderList().forEach(tso -> {
            // 展示入驻商头像
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(tso.getSupplierId());
            if (ToolUtil.isNotEmpty(supplierDTO))
                tso.setAvatar(supplierDTO.getAvatar());
            tso.setSubAmt(BigDecimal.ZERO);
            tso.setSubNum(NumberPool.LONG_ZERO);

            tso.setSupplierOrderDtlDTOList(
                    tso.getSupplierOrderDtlDTOList().stream()
                            .sorted(Comparator.comparing(TrdSupplierOrderDtlDTO::getGiftFlag))
                            .collect(Collectors.toList())
            );
            tso.getSupplierOrderDtlDTOList().forEach(tsod -> {
                SpuDTO spuDTO = trdCacheService.getSpuDTO(tsod.getSpuId());
                SkuDTO skuDTO = trdCacheService.getSkuDTO(tsod.getSkuId());

                if (ToolUtil.isNotEmpty(spuDTO))
                    tsod.setSpuNo(spuDTO.getSpuNo())
                            .setMinUnit(spuDTO.getMinUnit())
                            ; // 商品编码

                if (ToolUtil.isNotEmpty(skuDTO)) {
                    tsod.setItemBarcode(skuDTO.getBarcode()); // 商品条码
                    tsod.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties())); // sku规格
                }
                tso.setSubAmt(tso.getSubAmt().add(tsod.getTotalAmt()));
                tso.setSubNum(tso.getSubNum() + tsod.getTotalNum());

                respVo.setOrderAmt(respVo.getOrderAmt().add(tsod.getSaleAmt()))
                        .setPayAmt(respVo.getPayAmt().add(tsod.getTotalAmt()))
                        .setDiscountAmt(respVo.getDiscountAmt().add(tsod.getDiscountAmt()));

                tsod.setAfterPromptInfo(
                        getItemAfterPromptInfo(tsod.getIsAfterSales(), tsod.getAfterSalesTime())
                );
                // 订单明细数据渲染结算信息
                if (settleMap.containsKey(tsod.getSupplierOrderDtlId())) {
                    tsod.setSettleAmt(settleMap.get(tsod.getSupplierOrderDtlId()));
                }

                // 展示分类格式化
                assembleProduceDateFormat(tsod, spuDTO);
            });
            // 查询订单状态流程信息
            tso.setOrderDeliveryDTOList(getSupplierOrderDeliveryLog(tso.getSupplierId(), tso.getSupplierOrderDtlDTOList().get(0).getSupplierOrderDtlId(), tso.getSupplierOrderNo()));
            // 入驻商订单详情组装快递信息
            getTrdOrderRespDTOAssembleExpressResult(tso);


        });
        // 当前订单不是在线支付未支付时才执行
        if (Objects.equals(respVo.getPayState(), PayStateEnum.PAY_NOT_ONLINE.getCode())) {
            // 在线支付未支付订单显示支付到期时间
            OrderSettingPolicyDTO orderSetting = trdCacheService.getOrderSettingPolicyInfo(respVo.getDcId());
            respVo.setExpirePayTime(DateUtils.getDateAddSecond(respVo.getCreateTime(), orderSetting.getOrderExpiryDateSecond()));
        }
    }

    /**
     * PC 订单查询返回结果组装
     */
    public void getOperatorOrderRespAssembleResult(DcSupplierOrderPageRespVO data,DcOrderPageReqVO dcOrderPageReqVO){
        // 查询订单操作信息
        data.setOrderDeliveryDTOList(getSupplierOrderDeliveryLog(data.getSupplierId(), data.getSupplierOrderDtlRespVOList().get(0).getSupplierOrderDtlId(), data.getSupplierOrderNo()));

        SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(data.getSupplierId());
        if (ObjectUtil.isNotNull(supplierDTO)) {
            data.setSupplierName(supplierDTO.getSupplierName());
            data.setContactPhone(supplierDTO.getContactPhone());
        }

        BranchDTO branchDTO = trdCacheService.getBranchDTO(data.getBranchId());
        if (ObjectUtil.isNotNull(branchDTO)) {
            data.setBranchName(branchDTO.getBranchName());
            data.setBranchAddr(branchDTO.getBranchAddr());
            data.setBranchContactName(branchDTO.getContactName());
            data.setBranchContactPhone(branchDTO.getContactPhone());
            data.setBranchImages(branchDTO.getBranchImages());
        }
        TrdDriverRatingRespVO driverRatingRespVO =   trdDriverRatingService.getTrdDriverRating(data.getDriverRatingId());
        if (ToolUtil.isNotEmpty(driverRatingRespVO)) {
            data.setDriverRatingTime(driverRatingRespVO.getCreateTime());
        }
        if (ObjectUtil.isNotNull(data.getColonelId())) {
            ColonelDTO colonelDTO = trdCacheService.getColonelDTO(data.getColonelId());
            if (ObjectUtil.isNotNull(colonelDTO)) {
                data.setColonelName(colonelDTO.getColonelName());
                data.setColonelPhone(colonelDTO.getColonelPhone());
            }
        }
        // 上级业务员信息
        if (ObjectUtil.isNotNull(data.getPcolonelId())) {
            ColonelDTO colonelDTO = trdCacheService.getColonelDTO(data.getPcolonelId());
            if (ObjectUtil.isNotNull(colonelDTO)) {
                data.setPcolonelName(colonelDTO.getColonelName());
                data.setPcolonelPhone(colonelDTO.getColonelPhone());
            }
        }

        data.setSaleTotalAmt(BigDecimal.ZERO);
        data.setDiscountAmt(BigDecimal.ZERO);
        data.setPayTotalAmt(BigDecimal.ZERO);
        data.setDemandTotalNum(NumberPool.LONG_ZERO);
        data.setAfterReturnTotalAmt(BigDecimal.ZERO);

        if(Objects.nonNull(dcOrderPageReqVO) && Objects.nonNull(dcOrderPageReqVO.getOrderType()) && dcOrderPageReqVO.getOrderType() == 99){
            data.setErrorState(1);
            String orderDeliveryOvertimeDate = DateUtils.timeDistance(data.getPayTime(),new Date());
            data.setErrorMemo("超时未发货"+orderDeliveryOvertimeDate);
        }

        data.getSupplierOrderDtlRespVOList().forEach(dataDtl -> {
            data.setSaleTotalAmt(data.getSaleTotalAmt().add(dataDtl.getSaleTotalAmt()))
                    .setDiscountAmt(data.getDiscountAmt().add(dataDtl.getDiscountAmt()))
                    .setPayTotalAmt(data.getPayTotalAmt().add(dataDtl.getDemandAmt()))
                    .setDemandTotalNum(data.getDemandTotalNum() + dataDtl.getDemandNum())
                    .setAfterReturnTotalAmt(data.getAfterReturnTotalAmt().add(dataDtl.getAfterReturnAmt()))
            ;

//            //补充售后差异信息
//            BigDecimal afterReturnAmtAfter = BigDecimal.ZERO;
//            List<DcSupplierOrderAfterDtlRespVO> afterDtlList = trdSupplierAfterDtlMapper.selectAfterDtlBySupplierOrderDtlId(dataDtl.getSupplierOrderDtlId(), 2);
//            if (ObjectUtil.isNotEmpty(afterDtlList)) {
//                List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
//                Map<String, SysDictData> unitMap = Optional.ofNullable(dictCache).orElse(Collections.emptyList()).stream()
//                    .collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));
//
//                afterDtlList.forEach(afterDtl -> {
//                    afterDtl.setAfterType("售后");
//                    afterDtl.setUnitName(unitMap.getOrDefault(afterDtl.getAfterUnit(), new SysDictData()).getDictLabel());
//                    //订单类型校验  兼容原订单类型
//                    if("SHJ".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHJ.equals(afterDtl.getAfterOrderType())){
//                        afterDtl.setAfterOrderType("拒收退货");
//                    }else if("SHS".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHS.equals(afterDtl.getAfterOrderType())){
//                        afterDtl.setAfterOrderType("销售退货");
//                    }
//                });
//                afterReturnAmtAfter = afterDtlList.stream()
//                .map(DcSupplierOrderAfterDtlRespVO::getAfterAmt)
//                .reduce(BigDecimal.ZERO, (subtotal, element) -> subtotal.add(element));
//
//                dataDtl.setAfterDtlRespVOList(afterDtlList);
//                dataDtl.setAfterReturnAmtAfter(afterReturnAmtAfter);
//            }
//
//            List<DcSupplierOrderAfterDtlRespVO> beforeAfterDtlList = trdSupplierAfterDtlMapper.selectAfterDtlBySupplierOrderDtlId(dataDtl.getSupplierOrderDtlId(), 1);
//            if (ObjectUtil.isNotEmpty(beforeAfterDtlList)) {
//                List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
//                Map<String, SysDictData> unitMap = Optional.ofNullable(dictCache).orElse(Collections.emptyList()).stream()
//                    .collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));
//                beforeAfterDtlList.forEach(afterDtl -> {
//                    afterDtl.setAfterType("售后");
//                    afterDtl.setUnitName(unitMap.getOrDefault(afterDtl.getAfterUnit(), new SysDictData()).getDictLabel());
//                    //订单类型校验  兼容原订单类型
//                    if("SHJ".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHJ.equals(afterDtl.getAfterOrderType())){
//                        afterDtl.setAfterOrderType("拒收退货");
//                    }else if("SHS".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHS.equals(afterDtl.getAfterOrderType())){
//                        afterDtl.setAfterOrderType("销售退货");
//                    }
//                });
//                dataDtl.setBeforeAfterDtlRespVOList(beforeAfterDtlList);
//            }

            SpuDTO spuDTO = trdCacheService.getSpuDTO(dataDtl.getSpuId());
            if (ObjectUtil.isNotNull(spuDTO)) {
                dataDtl.setSpuName(spuDTO.getSpuName());
                dataDtl.setSpuNo(spuDTO.getSpuNo());
                dataDtl.setThumb(spuDTO.getThumb());
            }

            SkuDTO skuDTO = trdCacheService.getSkuDTO(dataDtl.getSkuId());
            if (ObjectUtil.isNotNull(skuDTO)) {
                dataDtl.setBarcode(skuDTO.getBarcode());
                String properties = PropertyAndValDTO.getProperties(skuDTO.getProperties());
                if (ToolUtil.isNotEmpty(properties)) {
                    dataDtl.setSpuName(dataDtl.getSpuName() + "-" + properties);
                    dataDtl.setSkuProperties(properties);
                }
            }

            dataDtl.setAfterPromptInfo(
                    getItemAfterPromptInfo(dataDtl.getIsAfterSales(), dataDtl.getAfterSalesTime())
            );
        });
    }

    /**
     * 补充订单明细售后差异信息
     * @param data
     */
    public void getSupplierOrderDtlAfterAssembleResult(DcSupplierOrderPageRespVO data) {
        data.getSupplierOrderDtlRespVOList().forEach(dataDtl -> {
            //补充售后差异信息
            BigDecimal afterReturnAmtAfter = BigDecimal.ZERO;
            List<DcSupplierOrderAfterDtlRespVO> afterDtlList = trdSupplierAfterDtlMapper.selectAfterDtlBySupplierOrderDtlId(dataDtl.getSupplierOrderDtlId(), 2);
            if (ObjectUtil.isNotEmpty(afterDtlList)) {
                List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
                Map<String, SysDictData> unitMap = Optional.ofNullable(dictCache).orElse(Collections.emptyList()).stream()
                        .collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));

                afterDtlList.forEach(afterDtl -> {
                    afterDtl.setAfterType("售后");
                    afterDtl.setUnitName(unitMap.getOrDefault(afterDtl.getAfterUnit(), new SysDictData()).getDictLabel());
                    //订单类型校验  兼容原订单类型
                    if("SHJ".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHJ.equals(afterDtl.getAfterOrderType())){
                        afterDtl.setAfterOrderType("拒收退货");
                    }else if("SHS".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHS.equals(afterDtl.getAfterOrderType())){
                        afterDtl.setAfterOrderType("销售退货");
                    }
                });
                afterReturnAmtAfter = afterDtlList.stream()
                        .map(DcSupplierOrderAfterDtlRespVO::getAfterAmt)
                        .reduce(BigDecimal.ZERO, (subtotal, element) -> subtotal.add(element));

                dataDtl.setAfterDtlRespVOList(afterDtlList);
                dataDtl.setAfterReturnAmtAfter(afterReturnAmtAfter);
            }

            List<DcSupplierOrderAfterDtlRespVO> beforeAfterDtlList = trdSupplierAfterDtlMapper.selectAfterDtlBySupplierOrderDtlId(dataDtl.getSupplierOrderDtlId(), 1);
            if (ObjectUtil.isNotEmpty(beforeAfterDtlList)) {
                List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
                Map<String, SysDictData> unitMap = Optional.ofNullable(dictCache).orElse(Collections.emptyList()).stream()
                        .collect(Collectors.toMap(SysDictData::getDictValue, Function.identity()));
                beforeAfterDtlList.forEach(afterDtl -> {
                    afterDtl.setAfterType("售后");
                    afterDtl.setUnitName(unitMap.getOrDefault(afterDtl.getAfterUnit(), new SysDictData()).getDictLabel());
                    //订单类型校验  兼容原订单类型
                    if("SHJ".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHJ.equals(afterDtl.getAfterOrderType())){
                        afterDtl.setAfterOrderType("拒收退货");
                    }else if("SHS".equals(afterDtl.getAfterOrderType()) || SheetTypeConstants.SHS.equals(afterDtl.getAfterOrderType())){
                        afterDtl.setAfterOrderType("销售退货");
                    }
                });
                dataDtl.setBeforeAfterDtlRespVOList(beforeAfterDtlList);
            }
        });
    }

    private String getItemAfterPromptInfo(Integer isAfterSale, Long afterSalesTime) {
        if (Objects.equals(isAfterSale, NumberPool.INT_ZERO)) { // 不可售后
            return "该商品不支持退货";
        } else if (ToolUtil.isEmpty(afterSalesTime) || afterSalesTime <= NumberPool.LONG_ZERO) {
            return "该商品不可退货";
        }
        return ToolUtil.turnDayHourMinuteString(afterSalesTime.intValue()) + "内无理由退货";
    }


    /**
     *  校验订单是否已发送第三方， 如果已发送，则抛出异常
     * @param supplierId 入驻商信息
     * @param orderIds 订单Id集合
     */
    private void checkSupplierOrderPushStatus(Long supplierId, List<Long> orderIds) {
            //获取入驻商配置
        OpensourceDto opensourceDto = trdCacheService.getOpensourceByMerchantId(supplierId);
        if (ToolUtil.isNotEmpty(opensourceDto) && ToolUtil.isNotEmpty(opensourceDto.getOpensourceId())) {
            throw exception(TRD_ORDER_NOT_OPERATE, supplierId);
        }
//        List<TrdSupplierOrder> trdSupplierOrderList = trdSupplierOrderMapper.selectListBySupplierIdAndOrderIds(supplierId, orderIds);
//        StringBuffer errorMsg = new StringBuffer();
//        trdSupplierOrderList.forEach(trdSupplierOrder -> {
//            errorMsg.append("订单【")
//                    .append(trdSupplierOrder.getOrderNo()).append("】已发送第三方！");
//        });
//        if (errorMsg.length() > 0)
//            throw new ServiceException(errorMsg.toString());
    }


    /**
     * 入驻商订单详情组装快递信息
     * @param tso
     */
    private void getTrdOrderRespDTOAssembleExpressResult(TrdSupplierOrderDTO tso) {

        // 过滤出全国商品 用于查询快递信息
        Set<Long> supplierDtlIds = tso.getSupplierOrderDtlDTOList().stream()
                        .filter((orderDtl -> Objects.equals(orderDtl.getItemType(), ProductType.GLOBAL.getCode())))
                        .map(TrdSupplierOrderDtlDTO::getSupplierOrderDtlId)
                        .collect(Collectors.toSet());
        // 当数据不为空时，查询订单快递信息
        if (ToolUtil.isNotEmpty(supplierDtlIds) && !supplierDtlIds.isEmpty()) {
            List<TrdOrderExpress> expressList = trdOrderExpressMapper.getOrderExpressInfoByOrderDtlIds(supplierDtlIds);
            if (ToolUtil.isNotEmpty(expressList) && !expressList.isEmpty()) {
                Map<Long, List<TrdOrderExpress>> expressMap = expressList.stream().collect(Collectors.groupingBy(TrdOrderExpress::getSupplierOrderDtlId));

                tso.getSupplierOrderDtlDTOList().stream()
                        .filter((orderDtl -> Objects.equals(orderDtl.getItemType(), ProductType.GLOBAL.getCode())))
                        .forEach(tsod -> {
                            List<TrdOrderExpress> expresses = expressMap.get(tsod.getSupplierOrderDtlId());
                            if (ToolUtil.isNotEmpty(expresses))
                                tsod.setOrderExpressResDTOList(TrdOrderExpressConvert.INSTANCE.convertList(expresses));
                        });
            }
        }
    }

    //!@回调支付 - 8、发送 订单加单通知
    private void sendAddOrderCommand(TrdOrder order) {
        List<TrdSupplierOrderDtl> supplierOrderDtlList = trdSupplierOrderDtlMapper.selectListByOrderId(order.getOrderId());

        // 下单SKU数量
        Long skuQty = supplierOrderDtlList.stream()
                .map(TrdSupplierOrderDtl::getSkuId)
                .distinct()
                .count();
        // 普通加单指令 集合
        List<Long> commandIds = supplierOrderDtlList.stream()
                .map(TrdSupplierOrderDtl::getCommandId)
                .filter(commandId -> ToolUtil.isNotEmpty(commandId) && commandId > NumberPool.LOWER_GROUND_LONG)
                .distinct()
                .collect(Collectors.toList());
        // 发送加单通知
        tradeMqProducer.sendAddOrderCommand(
                CommandAddOrderVO.builder()
                        .branchId(order.getBranchId())
                        .createTime(order.getCreateTime())
                        .type((ToolUtil.isEmpty(commandIds) || commandIds.isEmpty()) ? StatusConstants.COMMAND_OPERATE_1 : StatusConstants.COMMAND_OPERATE_4) // 加单类型：1-增加锚点指令，4-更新普通执行执行结果（下单更新）
                        .commandIds(commandIds)
                        .orderAmt(order.getPayAmt())
                        .orderSkuQty(skuQty)
                        .memo(StringUtils.format("由订单【{}】新增加单锚点", order.getOrderNo()))
                        .build()
        );
    }

    /**
     * 欠款订单查询
     * @param dcOrderPageReqVO 查询条件
     * @return 欠款订单
     */
    @Override
    public PageResult<DcSupplierOrderPageRespVO> getDebtOrderPageList(DcOrderPageReqVO dcOrderPageReqVO) {
        PageResult<DcSupplierOrderPageRespVO> result = new PageResult<>();
        dcOrderPageReqVO.setPageNo((dcOrderPageReqVO.getPageNo() - 1) * dcOrderPageReqVO.getPageSize());
        if (ToolUtil.isNotEmpty(dcOrderPageReqVO.getEndTime())) {
            dcOrderPageReqVO.setEndTime(DateUtil.endOfDay(dcOrderPageReqVO.getEndTime()));
        }

        if (ToolUtil.isEmpty(dcOrderPageReqVO.getDcId())){
            Long dcId = SecurityUtils.getLoginUser().getDcId();
            dcOrderPageReqVO.setDcId(dcId);
        }

        if (ToolUtil.isEmpty(dcOrderPageReqVO.getSupplierId())){
            Long supplierId = SecurityUtils.getLoginUser().getSysUser().getSupplierId();
            dcOrderPageReqVO.setSupplierId(supplierId);
        }

        dcOrderPageReqVO.setOrderDeliveryOvertimeDay(null);
        HomePagesCurrentSalesDataRespDTO salesDataRespDTO = trdOrderMapper.getDebtOrderPageListCount(dcOrderPageReqVO);
        Long total = salesDataRespDTO.getDebtOrderTotalQty();
        if (total <= NumberPool.LONG_ZERO) { // 没有查询到数据，直接返回
            result.setList(new ArrayList<>());
            result.setTotal(0L);
            return result;
        }
        List<DcSupplierOrderPageRespVO> resultData =  trdOrderMapper.getDebtOrderPageList(dcOrderPageReqVO);
        resultData.forEach(data -> {
            getOperatorOrderRespAssembleResult(data, dcOrderPageReqVO);
        });
        result.setList(resultData);
        result.setTotal(total);
        return result;
    }

    /**
     * 获取欠款金额
     * @param dcOrderPageReqVO
     * @return
     */
    @Override
    public BigDecimal getDebtOrderAmt(DcOrderPageReqVO dcOrderPageReqVO) {
        if (ToolUtil.isNotEmpty(dcOrderPageReqVO.getEndTime())) {
            dcOrderPageReqVO.setEndTime(DateUtil.endOfDay(dcOrderPageReqVO.getEndTime()));
        }
        if (ToolUtil.isEmpty(dcOrderPageReqVO.getDcId())){
            Long dcId = SecurityUtils.getLoginUser().getDcId();
            dcOrderPageReqVO.setDcId(dcId);
        }

        if (ToolUtil.isEmpty(dcOrderPageReqVO.getSupplierId())){
            Long supplierId = SecurityUtils.getLoginUser().getSysUser().getSupplierId();
            dcOrderPageReqVO.setSupplierId(supplierId);
        }
        HomePagesCurrentSalesDataRespDTO salesDataRespDTO = trdOrderMapper.getDebtOrderPageListCount(dcOrderPageReqVO);
        return salesDataRespDTO.getDebtTotalAmt();
    }

    @Override
    public List<SupplierOrderDtlInfoExportVO> getDebtSupplierOrderDtlInfoExport(DcOrderPageReqApiVO dcOrderPageReqVO) {
        if (ToolUtil.isNotEmpty(dcOrderPageReqVO.getEndTime())) {
            dcOrderPageReqVO.setEndTime(DateUtil.endOfDay(dcOrderPageReqVO.getEndTime()));
        }
        dcOrderPageReqVO.setIsDebtOrder(1);
        List<Long> spuIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        List<SupplierOrderDtlInfoExportVO> list = trdOrderMapper.getSupplierOrderDtlInfoExport(dcOrderPageReqVO, spuIds, skuIds);
        // 获取单位字典缓存
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));
        //获取支付方式字典信息
        List<SysDictData> payWayList = DictUtils.getDictCache("sys_order_pay_way");
        Map<String, String> payWayMap = payWayList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        //支付状态
        List<SysDictData> payStatusList = DictUtils.getDictCache("sys_pay_status");
        Map<String, String> payStatusMap = payStatusList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        list.stream().forEach((item)->{

            SpuDTO spuDTO = trdCacheService.getSpuDTO(item.getSpuId());
            if (ToolUtil.isEmpty(spuDTO)){
                spuDTO = new SpuDTO();
            }
            item.setSpuName(spuDTO.getSpuName())
                    .setSaleUnit(unitMap.containsKey(item.getOrderUnit() + "") ? unitMap.get(item.getOrderUnit() + "").getDictLabel() : "")
            ;

            //区域城市
            AreaDTO threeArea = trdCacheService.getByAreaId(item.getAreaId());
            if (Objects.nonNull(threeArea)) {
                assignmentArea(item,threeArea);
                if (!"1".equals(String.valueOf(threeArea.getLevel()))){
                    AreaDTO twoArea = trdCacheService.getByAreaId(threeArea.getPid());
                    if (Objects.nonNull(twoArea)) {
                        assignmentArea(item,twoArea);
                        if (!"1".equals(String.valueOf(twoArea.getLevel()))){
                            AreaDTO oneArea = trdCacheService.getByAreaId(twoArea.getPid());
                            if (Objects.nonNull(oneArea)) {
                                assignmentArea(item, oneArea);
                            }
                        }

                    }
                }
            }
            BranchDTO branchDTO = trdCacheService.getBranchDTO(item.getBranchId());
            if (Objects.isNull(branchDTO)){
                branchDTO = new BranchDTO();
            }
            //地址 省市县
            if (Objects.nonNull(branchDTO.getThreeAreaCityId())) {
                SysAreaCityRespVO three = areaCityApi.getById(branchDTO.getThreeAreaCityId()).getCheckedData();
                if (Objects.nonNull(three)) {
                    SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                    SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();

                    item.setCounty(three.getName());
                    item.setCity(second.getName());
                    item.setProvince(first.getName());
                }
            }
            //入驻商
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(item.getSupplierId());
            if (Objects.nonNull(supplierDTO)) {
                item.setSupplierCode(supplierDTO.getSupplierCode());
                item.setSupplierName(supplierDTO.getSupplierName());
            }
            //门店
            item.setBranchName(branchDTO.getBranchName());
            item.setBranchNo(branchDTO.getBranchNo());
            //订单所属的下单门店地址
            item.setBranchAddr(branchDTO.getBranchAddr());
            //经纬度
            item.setLongitude(branchDTO.getLongitude());
            item.setLatitude(branchDTO.getLatitude());
            //渠道
            if (Objects.nonNull(branchDTO.getChannelId())){
                ChannelDTO channelDTO = trdCacheService.getChannelDTO(branchDTO.getChannelId());
                if (Objects.nonNull(channelDTO)){
                    item.setChannelName(channelDTO.getChannelName());
                }
            }

            //业务员
            if (Objects.nonNull(item.getColonelId())){
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(item.getColonelId());
                if (Objects.nonNull(colonelDTO)){
                    item.setColonelName(colonelDTO.getColonelName());
                }
            }
            // 上级业务员信息
            if (ObjectUtil.isNotNull(item.getPcolonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(item.getPcolonelId());
                if (ObjectUtil.isNotNull(colonelDTO)) {
                    item.setPcolonelName(colonelDTO.getColonelName());
                }
            }
            item.setDeliveryState(item.getDelivery());
            item.setPayTotalAmt(item.getDemandAmt());
            //支付方式
            item.setPayWay(payWayMap.get(item.getPayWay()));
            //订单类型
            String orderType = item.getOrderType();
            if ("0".equals(item.getOrderType())){
                item.setOrderType("全国");
            }else if ("1".equals(item.getOrderType())){
                item.setOrderType("本地");
            }
            //支付状态
            if (StringUtils.isNotEmpty(item.getPayState())){
                item.setPayState(payStatusMap.get(item.getPayState()));
            }
        });

        return list;
    }

    /**
     * 订单详情组装优惠信息
     * @param respVO
     */
    private void getTrdOrderRespDTOAssembleDiscountResult(DcSupplierOrderPageRespVO respVO) {
        // 根据订单入驻商订单ID取出订单优惠信息
        List<TrdOrderDiscountDtl> discountList = trdOrderDiscountDtlMapper.selectGiftListBySupplierId(respVO.getSupplierOrderId());
        if (ToolUtil.isEmpty(discountList)) {
            return;
        }
        // 订单优惠信息列表 转换 map
        Map<Long,List<TrdOrderDiscountDtl>> discountMap = discountList.stream()
                .filter(dtl -> ObjectUtil.isNotNull(dtl.getSupplierOrderDtlId())) // 过滤掉促销数据 订单详情ID的数据  如：赠品优惠劵
                .collect(Collectors.groupingBy(TrdOrderDiscountDtl::getSupplierOrderDtlId));
        // 订单明细详情促销优惠信息组装
        respVO.getSupplierOrderDtlRespVOList().stream()
                .filter(tsod -> discountMap.containsKey(tsod.getSupplierOrderDtlId())) // 过滤掉没有促销数据的商品信息
                .forEach(tsod -> {
                    // 获取改订单商品数据的促销信息
                    List<TrdOrderDiscountDtl> discountDtlList = discountMap.get(tsod.getSupplierOrderDtlId());
                    // 组装促销信息
                    tsod.setDcSupplierOrderDtlDiscountRespVOList(
                            discountDtlList.stream().map(dtlDiscount -> {
                                return DcSupplierOrderDtlDiscountRespVO.builder()
                                        .discountType(dtlDiscount.getDiscountType())
                                        .discountTypeName(TrdDiscountTypeEnum.getDiscountType(dtlDiscount.getDiscountType()).getName())
                                        .discountName(dtlDiscount.getDiscountName())
                                        .discountAmt(dtlDiscount.getActivityDiscountAmt().add(dtlDiscount.getCouponDiscountAmt()).add(dtlDiscount.getCouponDiscountAmt2()))
                                        .build();
                            }).collect(Collectors.toList())
                    );
                });

        // 初始化订单优惠信息集合
        respVO.setDcSupplierOrderRespVOList(new ArrayList<>());
        // 订单优惠信息组装
        discountList.stream()
                .filter(dtl -> ObjectUtil.isNotNull(dtl.getSupplierOrderDtlId())) // 过滤掉促销数据 订单详情ID的数据  如：赠品优惠劵
                .collect(Collectors.groupingBy(TrdOrderDiscountDtl::getDiscountId))
                .forEach((key, value) -> {
                    // 获取优惠活动总金额
                    BigDecimal discountAmt = value.stream()
                            .map(disoucnt -> disoucnt.getActivityDiscountAmt().add(disoucnt.getCouponDiscountAmt()).add(disoucnt.getCouponDiscountAmt2())).reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    // 组装促销信息
                    respVO.getDcSupplierOrderRespVOList().add(
                            DcSupplierOrderDtlDiscountRespVO.builder()
                                    .discountType(value.get(NumberPool.INT_ZERO).getDiscountType())
                                    .discountTypeName(TrdDiscountTypeEnum.getDiscountType(value.get(NumberPool.INT_ZERO).getDiscountType()).getName())
                                    .discountName(value.get(NumberPool.INT_ZERO).getDiscountName())
                                    .discountAmt(discountAmt)
                                    .build()
                    );
                });
    }

    /**
     * 获取支付方式处理器
     * @param platform
     * @return
     */
    private ITrdOrderPayWayHandlerService getPayWayHandlerService(String platform) {
        return trdOrderPayWayHandlerServices.stream().filter(handler -> handler.isPlatform(platform))
                .findFirst().orElse(trdOrderPayWayHandlerServices.get(0));
    }

    /**
     * 获取支付方式处理器
     * @param platform
     * @return
     */
    private ITrdOrderPayWayHandlerService getPayWayHandlerService(String platform, String payWay) {
        return trdOrderPayWayHandlerServices.stream().filter(handler -> handler.isPlatform(platform, payWay))
                .findFirst().orElse(trdOrderPayWayHandlerServices.get(0));
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给"TODO 补充编号"设置一个错误码编号！！！
    // ========== 订单 TODO 补充编号 ==========
    // ErrorCode TRD_ORDER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "订单不存在");
    @Override
    public Boolean getAreaIdExistOrder(Long areaId, Long sysCode) {
        return trdOrderMapper.getAreaIdExistOrder(areaId,sysCode);
    }

    /**
     * 根据上架商品数据, 格式化订单商品生产日期格式
     * @param tsod      订单商品数据
     * @param spuDTO    SPU信息
     */
    private void assembleProduceDateFormat(TrdSupplierOrderDtlDTO tsod, SpuDTO spuDTO) {
        // 如果没有生产日期
        if (Objects.isNull(spuDTO.getLatestDate())) {
            tsod.setOldestDateFormat(StringPool.EMPTY);
            tsod.setLatestDateFormat(StringPool.EMPTY);
            return;
        }
        if (Objects.nonNull(tsod.getAreaItemId())) {
            AreaItemDTO areaItemDTO = trdCacheService.getAreaItemDTO(tsod.getAreaItemId());
            if (Objects.nonNull(areaItemDTO)) {
                AreaClassDTO classDTO = trdCacheService.getAreaClassDTO(areaItemDTO.getAreaClassId());
                if (Objects.isNull(classDTO) || classDTO.getShowProduceDate() == NumberPool.INT_ZERO) {
                    tsod.setLatestDateFormat(StringPool.EMPTY);
                    tsod.setOldestDateFormat(StringPool.EMPTY);
                    return;
                }
                if (Objects.isNull(classDTO)
                        || (classDTO.getShowProduceDate() == NumberPool.INT_ONE && StringUtils.isEmpty(classDTO.getProduceDateFormat()))
                        || (classDTO.getShowProduceDate() == NumberPool.INT_ZERO)
                ) {
                    tsod.setLatestDateFormat(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                    tsod.setOldestDateFormat(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                } else {
                    tsod.setLatestDateFormat(DateUtil.format(spuDTO.getLatestDate(), classDTO.getProduceDateFormat()));
                    tsod.setOldestDateFormat(DateUtil.format(spuDTO.getOldestDate(), classDTO.getProduceDateFormat()));
                }
            }
        }
        if (Objects.nonNull(tsod.getSupplierItemId())) {
            SupplierItemDTO supplierItemDTO = trdCacheService.getSupplierItemDTO(tsod.getSupplierItemId());
            if (Objects.nonNull(supplierItemDTO)) {
                SaleClassDTO classDTO = trdCacheService.getSaleClassDTO(supplierItemDTO.getSaleClassId());
                if (Objects.isNull(classDTO) || classDTO.getShowProduceDate() == NumberPool.INT_ZERO) {
                    tsod.setLatestDateFormat(StringPool.EMPTY);
                    tsod.setOldestDateFormat(StringPool.EMPTY);
                    return;
                }
                if (Objects.isNull(classDTO)
                        || (classDTO.getShowProduceDate() == NumberPool.INT_ONE && StringUtils.isEmpty(classDTO.getProduceDateFormat()))
                        || (classDTO.getShowProduceDate() == NumberPool.INT_ZERO)
                ) {
                    tsod.setLatestDateFormat(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                    tsod.setOldestDateFormat(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                } else {
                    tsod.setLatestDateFormat(DateUtil.format(spuDTO.getLatestDate(), classDTO.getProduceDateFormat()));
                    tsod.setOldestDateFormat(DateUtil.format(spuDTO.getOldestDate(), classDTO.getProduceDateFormat()));
                }
            }
        }
    }
    
    /**
     * 根据订单ID获取分佣金额
     * @param orderId 订单ID
     * @return 分佣金额（门店分佣金额合计）
     */
    private BigDecimal getProfitAmountByOrderId(Long orderId) {
        if (orderId == null) {
            return BigDecimal.ZERO;
        }
        
        // 根据订单ID查询入驻商订单结算信息
        List<TrdSupplierOrderSettle> settleList = supplierOrderSettleMapper.selectListByOrderId(orderId);
        
        if (ToolUtil.isEmpty(settleList)) {
            return BigDecimal.ZERO;
        }
        
        // 合计门店分佣金额
        BigDecimal totalBranchAmt = settleList.stream()
        .filter(settle -> settle.getBranchAmt() != null)
        .map(TrdSupplierOrderSettle::getBranchAmt)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalBranchAmt;
    }

    /**
     * 查询返回订单信息组装
     * @param respVoHead
     */
    public TrdOrderMiniHeadRespDTO getTrdOrderMiniRespDTOAssembleResult(TrdOrderMiniHeadRespDTO respVoHead, TrdOrderPageReqDTO headReqVO){
        if(null == respVoHead || null == respVoHead.getOrderId()){
            return respVoHead;
        }
        TrdOrderPageReqDTO orderPageReqVO = new TrdOrderPageReqDTO();
        orderPageReqVO.setOrderId(respVoHead.getOrderId());
        orderPageReqVO.setDeliveryState(headReqVO.getDeliveryState());

        List<TrdOrderMiniRespDTO> detailDtoList = trdOrderMapper.selectPageAllNew2(orderPageReqVO);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(detailDtoList)){
            return respVoHead;
        }
        TrdOrderMiniRespDTO respVo = detailDtoList.get(0);
        BranchDTO branch = trdCacheService.getBranchDTO(respVo.getBranchId());
        ColonelDTO colonel = new ColonelDTO();
        if (ToolUtil.isNotEmpty(respVo.getColonelId())) {
            colonel = trdCacheService.getColonelDTO(respVo.getColonelId());
        }

        respVo.setBranchName(branch.getBranchName())
                .setColonelName(colonel.getColonelName())
                .setColonelAvatarImages(colonel.getAvatarImages())
                .setOrderAmt(BigDecimal.ZERO)
                .setPayAmt(BigDecimal.ZERO)
                .setDiscountAmt(BigDecimal.ZERO)
                .setIsPayOnline(branch.getIsPayOnline())
        ;
        
        // 获取分佣金额
        BigDecimal profitAmount = getProfitAmountByOrderId(respVoHead.getOrderId());
        respVo.setProfitAmount(profitAmount);
        
        respVo.getSupplierOrderList().forEach(tso -> {
            // 展示入驻商头像
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(tso.getSupplierId());
            if (ToolUtil.isNotEmpty(supplierDTO)) {
                tso.setAvatar(supplierDTO.getAvatar());
            }

            tso.setSubAmt(BigDecimal.ZERO);
            tso.setSubNum(NumberPool.LONG_ZERO);


            tso.getSupplierOrderDtlDTOList().forEach(tsod -> {
                tso.setSubAmt(tso.getSubAmt().add(tsod.getTotalAmt()));
                tso.setSubNum(tso.getSubNum() + tsod.getTotalNum());

                respVo.setOrderAmt(respVo.getOrderAmt().add(tsod.getSaleAmt()))
                        .setPayAmt(respVo.getPayAmt().add(tsod.getTotalAmt()))
                        .setDiscountAmt(respVo.getDiscountAmt().add(tsod.getDiscountAmt()));
            });

            tso.setSupplierOrderDtlDTOList(
                    tso.getSupplierOrderDtlDTOList().stream().limit(9).collect(Collectors.toList())
            );


        });
        // 当前订单不是在线支付未支付时才执行
        if (Objects.equals(respVo.getPayState(), PayStateEnum.PAY_NOT_ONLINE.getCode())) {
            // 在线支付未支付订单显示支付到期时间
            OrderSettingPolicyDTO orderSetting = trdCacheService.getOrderSettingPolicyInfo(respVo.getDcId());
            respVo.setExpirePayTime(DateUtils.getDateAddSecond(respVo.getCreateTime(), orderSetting.getOrderExpiryDateSecond()));
        }

        return TrdOrderConvert.INSTANCE.convert2TrdOrderMiniHeadRespDTO(respVo);
    }
    @Override
    public Boolean getBranchIdExistOrder(Long branchId) {
        return trdOrderMapper.getBranchIdExistOrder(branchId);
    }

    @Override
    public List<TrdOrder> getBranchLatestOrderByBranchIdList(Long sysCode) {
        return trdOrderMapper.getBranchLatestOrderByBranchIdList(sysCode);
    }

    @Override
    public TrdOrder getBranchLatestOrderByBranchId(Long branchId) {
        return trdOrderMapper.getBranchLatestOrderByBranchId(branchId);
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.trade.enums.ErrorCodeConstants 类中。注意，请给"TODO 补充编号"设置一个错误码编号！！！
    // ========== 订单 TODO 补充编号 ==========
    // ErrorCode TRD_ORDER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "订单不存在");


    /**
     * 根据订单号查询入住商订单详情
     * @param orderNo
     * @return
     */
    public List<TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNo(String orderNo){
        return trdSupplierOrderDtlMapper.getSupplierOrderDtlByOrderNo(orderNo);
    }

    public List<TrdSupplierOrderDtlVO> getSupplierOrderDtlByOrderNos(List<String> orderNos) {
        return trdSupplierOrderDtlMapper.getSupplierOrderDtlByOrderNos(orderNos);
    }

    @Override
    public OrderCutAmtDTO getOrderCntAmt(OrderCutAmtDTO.CacheKey cacheKey) {
        // 计算当前时间有没有到达截团时间
        DateTime endDate = DateUtil.parse(StringUtils.format("{} {}", cacheKey.getDate(), cacheKey.getCutTime()), OrderCutAmtDTO.CUT_FORMAT);
        DateTime startDate = DateUtil.offsetDay(endDate, -1);
        OrderCutAmtDTO cutAmtDTO = trdOrderMapper.selectOrderCntAmt(startDate, endDate, cacheKey);
        if (Objects.isNull(cutAmtDTO)) {
            cutAmtDTO = new OrderCutAmtDTO();
        }
        return cutAmtDTO;
    }

    /**
     * 查询入驻商订单数据
     * @param param
     * @return
     */
    public List<SupplierOrderVO> selectSupplierOrder(DcOrderPageReqVO param){
        Long dcId = param.getDcId();
        String orderDeliveryOvertimeDay = "3";
        if(ToolUtil.isNotEmpty(dcId)){
            OrderSettingPolicyDTO partnerPolicy =partnerPolicyApi.getOrderSettingPolicy(dcId).getCheckedData();
            if(ToolUtil.isNotEmpty(partnerPolicy) && ToolUtil.isNotEmpty(partnerPolicy.getOrderDeliveryOvertimeDay()))
                orderDeliveryOvertimeDay = partnerPolicy.getOrderDeliveryOvertimeDay();
        }

        param.setOrderDeliveryOvertimeDay(orderDeliveryOvertimeDay);
        List<SupplierOrderVO> supplierOrderVOS = trdOrderMapper.selectSupplierOrder(param);

        supplierOrderVOS.forEach(t -> {
            //门店
            BranchDTO branchDTO = trdCacheService.getBranchDTO(t.getBranchId());
            if (ObjectUtil.isNotNull(branchDTO)) {
                t.setBranchName(branchDTO.getBranchName());
                t.setBranchAddr(branchDTO.getBranchAddr());
                t.setBranchContactName(branchDTO.getContactName());
                t.setBranchContactPhone(branchDTO.getContactPhone());
                t.setBranchImages(branchDTO.getBranchImages());
            }
            //业务员
            if (ObjectUtil.isNotNull(t.getColonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(t.getColonelId());
                if (ObjectUtil.isNotNull(colonelDTO)) {
                    t.setColonelName(colonelDTO.getColonelName());
                    t.setColonelPhone(colonelDTO.getColonelPhone());
                }
            }
            // 上级业务员信息
            if (ObjectUtil.isNotNull(t.getPcolonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(t.getPcolonelId());
                if (ObjectUtil.isNotNull(colonelDTO)) {
                    t.setPcolonelName(colonelDTO.getColonelName());
                    t.setPcolonelPhone(colonelDTO.getColonelPhone());
                }
            }

            //状态
            if(Objects.nonNull(param) && Objects.nonNull(param.getOrderType()) && param.getOrderType() == 99){
                t.setErrorState("异常");
                String orderDeliveryOvertimeDate = DateUtils.timeDistance(t.getPayTime(),new Date());
                t.setErrorMemo("超时未发货"+orderDeliveryOvertimeDate);
            }
        });

        return supplierOrderVOS;
    }
    
    @Override
    public List<TrdSupplierOrder> selectSupplierOrder(String orderNo){
        return trdSupplierOrderMapper.selectList(new LambdaQueryWrapper<TrdSupplierOrder>().eq(TrdSupplierOrder::getOrderNo,orderNo));
    }

    public List<SupplierOrderExportVO> selectSupplierOrderExport(DcOrderPageReqVO param){
        log.info("订单导出参数selectSupplierOrderExport={}",JsonUtils.toJsonString(param));
        if (ToolUtil.isNotEmpty(param.getEndTime())) {
            param.setEndTime(DateUtil.endOfDay(param.getEndTime()));
        }

        List<SupplierOrderExportVO> list = trdOrderMapper.selectSupplierOrderExport(param);

        //获取支付方式字典信息
        List<SysDictData> payWayList = DictUtils.getDictCache("sys_order_pay_way");
        //入驻商订单状态
        List<SysDictData> supplierOrderStatusList = DictUtils.getDictCache(DictTypeConstants.SUPPLIER_ORDER_STATUS);
        Map<String, String> supplierOrderStatusMap = supplierOrderStatusList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        //支付方式
        List<SysDictData> orderPayWayList = DictUtils.getDictCache(DictTypeConstants.SYS_ORDER_PAY_WAY);
        Map<String, String> orderPayWayMap = orderPayWayList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        Map<String, String> payWayMap = payWayList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        //支付状态
        List<SysDictData> payStatusList = DictUtils.getDictCache("sys_pay_status");
        Map<String, String> payStatusMap = payStatusList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        list.forEach(t -> {
            //门店
            BranchDTO branchDTO = trdCacheService.getBranchDTO(t.getBranchId());
            if (ObjectUtil.isNotNull(branchDTO)) {
                t.setBranchName(branchDTO.getBranchName());
                t.setBranchAddr(branchDTO.getBranchAddr());
                t.setBranchNo(branchDTO.getBranchNo());
            }
            //业务员
            if (ObjectUtil.isNotNull(t.getColonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(t.getColonelId());
                if (ObjectUtil.isNotNull(colonelDTO)) {
                    t.setColonelName(colonelDTO.getColonelName());
                }
            }
            // 上级业务员信息
            if (ObjectUtil.isNotNull(t.getPcolonelId())) {
                ColonelDTO colonelDTO = trdCacheService.getColonelDTO(t.getPcolonelId());
                if (ObjectUtil.isNotNull(colonelDTO)) {
                    t.setPcolonelName(colonelDTO.getColonelName());
                }
            }

            //状态
            if(Objects.nonNull(param) && Objects.nonNull(param.getOrderType()) && param.getOrderType() == 99){
                t.setErrorState("异常");
                String orderDeliveryOvertimeDate = DateUtils.timeDistance(t.getPayTime(),new Date());
                t.setErrorMemo("超时未发货"+orderDeliveryOvertimeDate);
            }
            //打印状态
            if (StringUtils.isNotEmpty(t.getPrintState())){
                if ("1".equals(t.getPrintState())){
                    t.setPrintState("已打印");
                }else {
                    t.setPrintState("未打印");
                }
            }
            //支付方式
            t.setPayWay(payWayMap.get(t.getPayWay()));
            //订单类型
            if ("0".equals(t.getOrderType())){
                t.setOrderType("全国");
            }else if ("1".equals(t.getOrderType())){
                t.setOrderType("本地");
            }
            //支付状态
            if (StringUtils.isNotEmpty(t.getPayState())){
                t.setPayState(payStatusMap.get(t.getPayState()));
            }
            //入驻商订单状态
            if (StringUtils.isNotEmpty(t.getDeliveryState())){
                t.setDeliveryState(supplierOrderStatusMap.get(t.getPayState()));
            }
            //同步标识
            if (String.valueOf(OpenApiConstants.ORDER_SYNC_FLAG_0).equals(t.getPushStatus())){
                t.setPushStatus("未推送");
            }else if (String.valueOf(OpenApiConstants.ORDER_SYNC_FLAG_1).equals(t.getPushStatus())){
                t.setPushStatus("已推送");
            }else if (String.valueOf(OpenApiConstants.ORDER_SYNC_FLAG_2).equals(t.getPushStatus())){
                t.setPushStatus("已接收");
            }
            //入驻商
            SupplierDTO supplierDTO = trdCacheService.getSupplierDTO(t.getSupplierId());
            if (Objects.nonNull(supplierDTO)) {
                t.setSupplierCode(supplierDTO.getSupplierCode());
                t.setSupplierName(supplierDTO.getSupplierName());
            }
            //运营商
            DcDTO dcDTO = trdCacheService.getDcDTO(t.getDcId());
            if (Objects.nonNull(dcDTO)) {
                t.setDcCode(dcDTO.getDcCode());
                t.setDcName(dcDTO.getDcName());
            }
            //渠道
            if (Objects.nonNull(branchDTO.getChannelId())){
                ChannelDTO channelDTO = trdCacheService.getChannelDTO(branchDTO.getChannelId());
                if (Objects.nonNull(channelDTO)){
                    t.setChannelName(channelDTO.getChannelName());
                }
            }

            //区域城市
            AreaDTO threeArea = trdCacheService.getByAreaId(t.getAreaId());
//            AreaDTO threeArea = areaApi.getAreaByAreaId(item.getAreaId()).getCheckedData();
            if (Objects.nonNull(threeArea)) {
                assignmentArea(t,threeArea);
                if (!"1".equals(String.valueOf(threeArea.getLevel()))&&threeArea.getPid()!=null){
                    AreaDTO twoArea = trdCacheService.getByAreaId(threeArea.getPid());
                    if (Objects.nonNull(twoArea)) {
                        assignmentArea(t,twoArea);
                        if (!"1".equals(String.valueOf(twoArea.getLevel()))&&twoArea.getPid()!=null){
                            AreaDTO oneArea = trdCacheService.getByAreaId(twoArea.getPid());
                            if (Objects.nonNull(oneArea)) {
                                assignmentArea(t, oneArea);
                            }
                        }

                    }
                }
            }

            //地址 省市县
            if (Objects.nonNull(branchDTO.getThreeAreaCityId())) {
                SysAreaCityRespVO three = areaCityApi.getById(branchDTO.getThreeAreaCityId()).getCheckedData();
                if (Objects.nonNull(three)) {
                    SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                    SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();

                    t.setCounty(three.getName());
                    t.setCity(second.getName());
                    t.setProvince(first.getName());
                }
            }

        });



        return list;
    }

    @Override
    public List<SupplierOrderCountRespVO> countSupplierOrder(SupplierOrderCountPageReqVO pageReqVO) {
        if(StringUtils.isNotEmpty(pageReqVO.getSaasTenantCode())){
            PartnerDto partnerDto = partnerApi.getBySaasTenantCode(pageReqVO.getSaasTenantCode()).getCheckedData();
            if(null != partnerDto){
                pageReqVO.setSysCode(partnerDto.getSysCode());
            }else {
                return null;
            }
        }

        //传了开始日期没有传结束时间，设置默认当前日期+1
        if(null != pageReqVO.getStartDate() && null == pageReqVO.getEndDate()){
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 在当前日期上加一天
            LocalDate tomorrow = today.plusDays(1);
            pageReqVO.setEndDate(tomorrow);
        }
        List<SupplierOrderCountRespVO> countSupplierOrderList = trdOrderMapper.countSupplierOrder(pageReqVO);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(countSupplierOrderList)){
            return countSupplierOrderList;
        }
        Map<Long, String> partnerDtoMap = new HashMap<>();
        countSupplierOrderList.forEach(vo->{
            if(partnerDtoMap.containsKey(vo.getSysCode())){
                vo.setSaasTenantCode(partnerDtoMap.get(vo.getSysCode()));
                return;
            }
            //平台商信息 获取关联saas租户
            PartnerDto partnerDto = trdCacheService.getPartnerDto(vo.getSysCode() + "");
//            log.info("trdCacheService.getPartnerDto,{},vo:{}",JsonUtils.toJsonString(partnerDto),JsonUtils.toJsonString(vo));

            //缓存没有，取接口
            if(null == partnerDto || org.apache.commons.lang3.StringUtils.isEmpty(partnerDto.getSaasTenantCode())){
                partnerDto = partnerApi.getBySysCode(Long.valueOf(vo.getSysCode())).getCheckedData();
//                log.info(" partnerApi.getBySysCode,{}",JsonUtils.toJsonString(partnerDto));
            }

            if(null != partnerDto){
                vo.setSaasTenantCode(partnerDto.getSaasTenantCode());
                partnerDtoMap.put(vo.getSysCode(), partnerDto.getSaasTenantCode());
            }

        });

        return countSupplierOrderList;
    }

    @Override
    public List<SupplierOrderCountAllRespVO> countSupplierOrderAll(SupplierOrderCountAllPageReqVO pageReqVO) {
        if(StringUtils.isNotEmpty(pageReqVO.getSaasTenantCode())){
            PartnerDto partnerDto = partnerApi.getBySaasTenantCode(pageReqVO.getSaasTenantCode()).getCheckedData();
            if(null != partnerDto){
                pageReqVO.setSysCode(partnerDto.getSysCode());
            }else {
                return null;
            }
        }

        //传了开始日期没有传结束时间，设置默认当前日期+1
        if(null != pageReqVO.getStartDate() && null == pageReqVO.getEndDate()){
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 在当前日期上加一天
            LocalDate tomorrow = today.plusDays(1);
            pageReqVO.setEndDate(tomorrow);
        }
        List<SupplierOrderCountAllRespVO> countSupplierOrderList = trdOrderMapper.countSupplierOrderAll(pageReqVO);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(countSupplierOrderList)){
            return countSupplierOrderList;
        }
        Map<Long, String> partnerDtoMap = new HashMap<>();
        countSupplierOrderList.forEach(vo->{
            if(partnerDtoMap.containsKey(vo.getSysCode())){
                vo.setSaasTenantCode(partnerDtoMap.get(vo.getSysCode()));
                return;
            }
            //平台商信息 获取关联saas租户
            PartnerDto partnerDto = trdCacheService.getPartnerDto(vo.getSysCode() + "");
//            log.info("trdCacheService.getPartnerDto,{},vo:{}",JsonUtils.toJsonString(partnerDto),JsonUtils.toJsonString(vo));
            //缓存没有，取接口
            if(null == partnerDto || org.apache.commons.lang3.StringUtils.isEmpty(partnerDto.getSaasTenantCode())){
                partnerDto = partnerApi.getBySysCode(Long.valueOf(vo.getSysCode())).getCheckedData();
//                log.info(" partnerApi.getBySysCode,{}",JsonUtils.toJsonString(partnerDto));
            }

            if(null != partnerDto){
                vo.setSaasTenantCode(partnerDto.getSaasTenantCode());
                partnerDtoMap.put(vo.getSysCode(), partnerDto.getSaasTenantCode());
            }

        });

        return countSupplierOrderList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteOrder(Long orderId) {
        /**
         * 1、删除主订单
         */
        trdOrderMapper.deleteById(orderId);

        /**
         * 2、删除入驻商订单
         */
        trdSupplierOrderMapper.delete(new LambdaQueryWrapper<TrdSupplierOrder>().eq(TrdSupplierOrder::getOrderId, orderId));

        /**
         *  3、删除入驻商订单明细
         */
        List<TrdSupplierOrderDtl> trdSupplierOrderDtls = trdSupplierOrderDtlMapper.selectListByOrderId(orderId);
        trdSupplierOrderDtlMapper.delete(new LambdaQueryWrapper<TrdSupplierOrderDtl>().eq(TrdSupplierOrderDtl::getOrderId, orderId));

        /**
         * 4、删除入驻商订单结算明细
         */
        supplierOrderSettleMapper.delete(new LambdaQueryWrapper<TrdSupplierOrderSettle>().eq(TrdSupplierOrderSettle::getOrderId, orderId));

        /**
         * 5、删除订单明细日志流水
         */
        List<Long> supplierOrderDtlIds = trdSupplierOrderDtls.stream().map(TrdSupplierOrderDtl::getSupplierOrderDtlId).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(supplierOrderDtlIds)) {
            orderLogMapper.delete(new LambdaQueryWrapper<TrdOrderLog>().in(TrdOrderLog::getSupplierOrderDtlId, supplierOrderDtlIds));
        }

        /**
         * 6、删除订单优惠信息
         */
        trdOrderDiscountDtlMapper.delete(new LambdaQueryWrapper<TrdOrderDiscountDtl>().eq(TrdOrderDiscountDtl::getOrderId, orderId));
    }

    @Override
    public void sendOrderHdfkSuccess(RemoteSaveOrderVO orderVo) {
        /**
         * 发送货到付款订单支付成功消息 执行订单支付成功回调流程
         */
        TrdOrderSaveVO orderSaveVo = orderVo.getOrderSaveVo();
        // 货到付款支付方式标识
        boolean hdfkPayFlag = ToolUtil.isNotEmpty(orderSaveVo.getPayWay()) && orderSaveVo.getPayWay().equals(OrderPayWayEnum.HDFK.getPayWay());
        if (hdfkPayFlag) {
            TrdPayOrderPageVO pageVo = new TrdPayOrderPageVO();
            pageVo.setOrderNo(orderSaveVo.getOrderNo());
            pageVo.setPayWay(OrderPayWayEnum.HDFK.getPayWay());
            pageVo.setSuccessTime(DateUtils.getNowDate());
            pageVo.setPayAmt(orderSaveVo.getPayAmt());
            // 获取支付平台
            PayConfigDTO payConfigDTO = trdCacheService.getPayConfigDTO(orderSaveVo.getSysCode());
            pageVo.setPayPlatform(payConfigDTO.getStoreOrderPayPlatform());  // 订单支付平台
            // 发送货到付款订单支付成功消息 执行订单支付成功回调流程
            tradeMqProducer.sendOrderHdfkSuccessEvent(pageVo);
        }
    }

    @Override
    public HomePagesCurrentSalesDataRespVO getHomePagesCurrentDaySales(HomePagesCurrentSalesDataReqVO reqVO) {
        return trdOrderMapper.getHomePagesCurrentDaySales(reqVO);
    }
    
    @Override
    public void orderO2OGenerateSettleDivideDtl(O2OGenerateSettleParamVO paramVO) {
    
    }
    
}