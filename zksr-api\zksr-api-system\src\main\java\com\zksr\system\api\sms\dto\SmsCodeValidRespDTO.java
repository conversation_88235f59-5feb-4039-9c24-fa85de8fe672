package com.zksr.system.api.sms.dto;

import com.zksr.common.core.utils.ServletUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:   验证验证码是否正确
 * @date 2024/4/30 9:57
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "验证码短信验证码结果")
public class SmsCodeValidRespDTO {

    @ApiModelProperty("是否验证成功")
    private Boolean success;

    @ApiModelProperty("失败原因")
    private String errMsg;

    public static SmsCodeValidRespDTO success() {
        return new SmsCodeValidRespDTO(Boolean.TRUE, "验证成功");
    }

    public static SmsCodeValidRespDTO fail(String msg) {
        return new SmsCodeValidRespDTO(Boolean.FALSE, msg);
    }
}
