package com.zksr.promotion.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p>
 * bpm 系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== promotion 模块 1-016-000-000 ==========
    ErrorCode CHANEL_BRANCH_ONLY_ONE = new ErrorCode(1_016_000_001, "指定渠道和指定门店二选一");
    ErrorCode START_TIME_GREATER_THAN_END_TIME = new ErrorCode(1_016_000_002, "开始时间不能大于结束时间");
    ErrorCode PRO_CHECK_LADDER_FLAG = new ErrorCode(1_016_000_003, "该模板不为阶梯类型,只能设置一种规则");
    ErrorCode STATUS_NOT_UPDATE = new ErrorCode(1_016_000_004, "促销活动已启用禁止修改");
    ErrorCode ACTIVITY_COLLIDE = new ErrorCode(1_016_000_005, "促销活动冲突");
    ErrorCode ACTIVITY_ID_IS_NULL = new ErrorCode(1_016_000_006, "促销ID不能为空");
    ErrorCode ACTIVITY_IS_NULL = new ErrorCode(1_016_000_007, "未查询到促销活动");
    ErrorCode END_TIME_GREATER_THAN_NOW_TIME = new ErrorCode(1_016_000_008, "结束时间不能小于当前时间");
    ErrorCode START_TIME_GREATER_THAN_NOW_TIME = new ErrorCode(1_016_000_009, "开始时间不能小于当前时间");

    ErrorCode START_TIME_IS_NULL = new ErrorCode(1_016_000_010, "开始时间或结束时间不能为空");

    ErrorCode SALESPERSON_QUOTA_NOT_FOUND = new ErrorCode(1_016_000_011, "调整业务员额度,未查询到业务员信息!");
    ErrorCode SUPPLIER_LIST_IS_EMPTY = new ErrorCode(1_016_000_012, "入驻商列表不能为空");

    // ==========  coupon 模块 1-016-001-000 ==========
    ErrorCode COUPON_TIME01 = new ErrorCode(1_016_001_001, "优惠券有效期异常, 开始时间不能大于结束时间");
    ErrorCode SPU_SCOPE_ERR01 = new ErrorCode(1_016_001_002, "非全场券需要指定具体使用范围");
    ErrorCode RECEIVE_SCOPE_ERR01 = new ErrorCode(1_016_001_003, "领取范围非全部可领取, 需要指定具体可领取范围");
    ErrorCode PARAMS_DEFICIENCY01 = new ErrorCode(1_016_001_004, "优惠券折扣必须大于0且小于10");
    ErrorCode PARAMS_DEFICIENCY02 = new ErrorCode(1_016_001_005, "最高折扣金额必须大于0");
    ErrorCode RECEIVE_MQ_CHANNEL_ERR = new ErrorCode(1_016_001_006, "领取通道异常");
    ErrorCode COUPON_NOT_EXITS = new ErrorCode(1_016_001_007, "优惠券不存在");
    ErrorCode COUPON_IS_DISABLE = new ErrorCode(1_016_001_008, "优惠券已停用");
    ErrorCode COUPON_RECEIVE_LIMIT = new ErrorCode(1_016_001_009, "领取超过限制");
    ErrorCode COUPON_STOCK_ERR = new ErrorCode(1_016_001_010, "优惠券已经被抢光啦");
    ErrorCode COUPON_RECEIVE_RESULT_PAST = new ErrorCode(1_016_001_011, "请到优惠券列表查看领取结果");
    ErrorCode COUPON_REPEAT = new ErrorCode(1_016_001_012, "优惠券数据重复");
    ErrorCode COUPON_UNSUPPORTED_ORDER = new ErrorCode(1_016_001_014, "优惠券不支持");
    ErrorCode COUPON_CHANGE_STATE_ERR = new ErrorCode(1_016_001_015, "订单用券异常请稍后重试~");
    ErrorCode COUPON_CHANGE_STATE_ERR01 = new ErrorCode(1_016_001_016, "只有未发布的优惠券可以启用~");
    ErrorCode COUPON_ACTIVITY_EXIST = new ErrorCode(1_016_001_017, "已存在有效的注册发券优惠券活动,请先停用~");
    ErrorCode PARAMS_DEFICIENCY03 = new ErrorCode(1_016_001_018, "折扣类型优惠券同类型优惠券是否排它设置只能为是");

    // ==========  买赠 模块 1-016-002-000 ==========
    ErrorCode ACTIVITY_DEMO = new ErrorCode(1_016_002_001, "示例异常");
    ErrorCode RULE_QTY_REPETITION = new ErrorCode(1_016_002_002, "买赠触发赠送数量不能重复");

    // ==========  满赠 模块 1-016-003-000 ==========
    ErrorCode ACTIVITY_DEMO1 = new ErrorCode(1_016_003_001, "示例异常");
    ErrorCode FULL_AMT_REPETITION = new ErrorCode(1_016_003_002, "满赠金额不能重复");
    ErrorCode FULL_AMT_SKU_REPETITION = new ErrorCode(1_016_003_003, "相同满赠金额规则下,不能存在多个商品编号和规格一致的商品");

    ErrorCode FULL_CONDITIONS_CHECK = new ErrorCode(1_016_003_004, "满赠设置条件异常，请检查购买品项数、赠送方式、赠送单位数量配置");

    ErrorCode FULL_ORDER_SAVE_CHECK1 = new ErrorCode(1_016_003_005, "订单下单选择的满赠赠品数据异常，已选择的赠品不属于赠送规则内");

    ErrorCode FULL_ORDER_SAVE_CHECK2 = new ErrorCode(1_016_003_006, "订单下单选择的满赠赠品数据异常，购买品项数不符合赠送条件");

    ErrorCode FULL_ORDER_SAVE_CHECK3 = new ErrorCode(1_016_003_007, "订单下单选择的满赠赠品数据异常，该满赠活动为仅赠送一种赠品，不能多选赠品信息");

    ErrorCode FULL_ORDER_SAVE_CHECK4 = new ErrorCode(1_016_003_008, "订单下单选择的满赠赠品数据异常，该满赠活动为任选赠送赠品，请确认选择的赠品信息是否符合标准");


    // ==========  满减 模块 1-016-004-000 ==========
    ErrorCode ACTIVITY_DEMO2 = new ErrorCode(1_016_004_001, "示例异常");


    // ==========  秒杀 模块 1-016-005-000 ==========
    ErrorCode ACTIVITY_DEMO3 = new ErrorCode(1_016_005_001, "示例异常");
    ErrorCode ACTIVITY_STATUS_EDIT_ERROR = new ErrorCode(1_016_005_003, "编辑失败,活动开启后数据不能更改");
    ErrorCode SECKILL_IMPORT_ERROR  = new ErrorCode(1_016_005_004, "导入秒杀商品数据不能为空!");

    ErrorCode BRANCH_IMPORT_ERROR  = new ErrorCode(1_016_005_005, "导入门店信息数据不能为空!");

    ErrorCode ITEM_IMPORT_ERROR  = new ErrorCode(1_016_005_006, "导入商品信息数据不能为空!");

    // ==========  特价 模块 1-016-006-000 ==========
    ErrorCode ACTIVITY_DEMO4 = new ErrorCode(1_016_006_001, "示例异常");
    ErrorCode SPECIAL_OFFER_IMPORT_ERROR = new ErrorCode(1_016_006_002, "导入特价商品数据不能为空!");

    // ==========  批量发券模块 1-016-007-000 ==========
    ErrorCode BATCH_COUPON_DELETE_ERROR = new ErrorCode(1_016_007_001, "该批量发券已审核,无法删除");

    ErrorCode BATCH_COUPON_SAVE_ERROR = new ErrorCode(1_016_007_002, "批量发券保存失败");

    ErrorCode BATCH_COUPON_STORE_ERROR = new ErrorCode(1_016_007_003, "该账号平台商账号下没有门店信息");

    // ========== 组合促销模块 1-016-008-000 ==========
    ErrorCode COMBINATION_SKU_REPETITION = new ErrorCode(1_016_008_001, "组合商品内不能存在多个商品编号和规格一致的商品");
}
