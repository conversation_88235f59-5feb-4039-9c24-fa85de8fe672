package com.zksr.product.api.catgory.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量删除分类返回类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CatgoryDeleteDTO {

    /**
     * 有商品关联的三级类别
     */
    private List<Long> thirdCategoryIds;

    /**
     * 有商品关联的三级类别名称
     */
    private List<String> thirdCategoryNames;

    /**
     * 三级类别关联的上架商品
     */
    private List<String> thirdCategoryPrdtSpuIds;


    /**
     * 三级类别关联的上架商品名称
     */
    private List<String> thirdCategoryPrdtSpuNames;


    /**
     * 有关联订单的三级类别
     */
    private List<Long> orderCategoryIds;

    /**
     * 有关联订单的三级类别名称
     */
    private List<String> orderCategoryNames;
}
