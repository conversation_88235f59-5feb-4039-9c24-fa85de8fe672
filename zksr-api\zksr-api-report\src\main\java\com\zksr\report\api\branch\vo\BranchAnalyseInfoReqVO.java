package com.zksr.report.api.branch.vo;

import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店数据分析详情 request
 * @date 2024/11/23 9:07
 */
@Data
@ApiModel(description = "门店数据分析详情 page request")
public class BranchAnalyseInfoReqVO {

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("月份格式yyyy-MM")
    private String monthDate;

    public String getYear() {
        return monthDate.split("-")[0];
    }

    public String getMonth() {
        return monthDate.split("-")[1];
    }

    public Integer getMonthId() {
        return Integer.parseInt(getMonthDate().replace(StringPool.DASH, StringPool.EMPTY));
    }
}
