package com.zksr.member.api.branchLifecycle.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* 业务员APP- 业务员管理门店生命周期数量信息
* @date 2025/3/13 14:41
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ColonelBranchLifecycleQtyDTO {

    @ApiModelProperty(value = "业务员管理客户总数量")
    private Integer branchQty;

    @ApiModelProperty(value = "新客数量")
    private Integer newCustomerQty;

    @ApiModelProperty(value = "活跃数量")
    private Integer activeQty;

    @ApiModelProperty(value = "沉默数量")
    private Integer silentQty;

    @ApiModelProperty(value = "流失数量")
    private Integer lostProtectionPeriodQty;

}
