package com.zksr.system.api.invoice.dto;

import lombok.Data;

/**
 * @Description: 航班信息DTO
 * @Date: 2025/07/16
 */
@Data
public class FlightInfoDTO {

    /**
     * 飞机票仓位等级
     * 头等舱商务舱、舒适经济舱、经济舱、公务舱、明珠经济舱、高端经济舱、
     * 超级经济舱、高级经济舱、空中公务舱、特价专享、商务经济舱、特价舱位、
     * 超值公务舱、精品经济舱
     */
    private String className;

    /**
     * 出发站
     */
    private String fromStation;

    /**
     * 到达站
     */
    private String toStation;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 乘机日期
     */
    private String travelDate;

    /**
     * 乘机时间
     */
    private String travelTime;

    /**
     * 座位等级
     */
    private String seatLevel;

    /**
     * 承运人
     */
    private String carrier;
}
