package com.zksr.account.model.pay.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 美的付请求分账
 */
@Data
@ApiModel(description = " 美的付请求分账")
@NoArgsConstructor
public class PayMideaPayCreateDivideReqVO extends CreateDivideReqVO{

    public PayMideaPayCreateDivideReqVO(String tradeNo, Long merchantId, String merchantType) {
        super(tradeNo, merchantId, merchantType);
    }
    
}
