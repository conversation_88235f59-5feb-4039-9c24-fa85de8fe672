package com.zksr.trade.api.order;

import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderPageReqDTO;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.homePages.dto.HomePagesOrderAfterDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesCurrentSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesOrderSalesDataRespDTO;
import com.zksr.report.api.homePages.dto.HomePagesSalesTop10DataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import com.zksr.trade.api.order.dto.ColonelOrderDaySettleDTO;
import com.zksr.trade.api.order.vo.ColonelOrderDaySettleVO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderDtlVO;
import com.zksr.trade.enums.ApiConstants;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易模块订单跨服务调用
 */
@FeignClient(
        contextId = "remoteOrderApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
@SuppressWarnings("all")
public interface OrderApi {
    String PREFIX = ApiConstants.PREFIX + "/order";
    
    /**
     * 根据入驻商订单号查询包裹和包裹明细
     * @param trdPackageReq 请求参数
     * @return 包裹和包裹明细信息
     */
    @PostMapping(value = PREFIX + "/getPackInfoBySupplierNo")
    CommonResult<List<TrdPackageRepDto>> getPackInfoBySupplierNo(@RequestBody TrdPackageReq trdPackageReq);
    
    /**
     * 保存订单
     *
     * @param trdOrderSaveReqVo
     * @return
     */
    @PostMapping(value = PREFIX + "/saveOrder")
    public CommonResult<TrdOrderResDto> saveOrder(@RequestBody RemoteSaveOrderVO trdOrderSaveReqVo);

    /**
     * 获取订单入驻商 订单信息
     *
     * @param trdSupplierPageVo
     * @return
     */
    @PostMapping(value = PREFIX + "/getSupplierPayInfoBySheetNo")
    public CommonResult<OrderPayInfoRespDTO> getSupplierPayInfo(@RequestBody TrdSupplierPageVO trdSupplierPageVo);


    /**
     * 订单付款成功回调
     *
     * @param pageVo
     * @return
     */
    @PostMapping(value = PREFIX + "/orderPaySuccessCallback")
    public CommonResult<Boolean> orderPaySuccessCallback(@RequestBody TrdPayOrderPageVO pageVo);

    /**
     * 订单退款回调
     *
     * @param pageVo
     * @return
     */
    @PostMapping(value = PREFIX + "/orderRefundCallback")
    public CommonResult<Boolean> orderRefundCallback(@RequestBody TrdPayOrderPageVO pageVo);

    /**
     * @Description: 分页获取订单数据
     * @Author: liuxingyu
     * @Date: 2024/3/30 10:15
     */
    @PostMapping(value = PREFIX + "/pageOrderList")
    PageResult<TrdOrderRespDTO> pageOrderList(@RequestBody TrdOrderPageReqDTO orderPageReqVO);

    /**
     * @Description: 分页获取订单数据
     * @Author: chenmingqing
     * @Date: 2025/3/26 10:15
     */
    @PostMapping(value = PREFIX + "/miniPageOrderList")
    CommonResult<PageResult<TrdOrderMiniHeadRespDTO>> miniPageOrderList(@RequestBody TrdOrderPageReqDTO orderPageReqVO);


    /**
     * @Description: 获取订单详情数据
     * @Author: 陈明清
     * @Date: 2024/7/31 10:15
     */
    @PostMapping(value = PREFIX + "/getOrderInfo")
    CommonResult<TrdOrderRespDTO> getOrderInfo(@RequestBody TrdOrderPageReqDTO orderPageReqVO);

    /**
     * 取消订单
     *
     * @param orderNo
     * @return
     */
    @PostMapping(value = PREFIX + "/orderCancel")
    public CommonResult<Boolean> orderCancel(@RequestParam("orderNo") String orderNo);

    /**
     * 订单确认收货定时任务
     *
     * @return
     */
    @PostMapping(value = PREFIX + "/orderTakeDelivery")
    CommonResult<Boolean> orderTakeDelivery(@RequestBody TrdOrderTakeDeliveryVO takeDeliveryVO);


    /**
     * 订单完成定时任务
     *
     * @return
     */
    @PostMapping(value = PREFIX + "/orderComplete")
    CommonResult<Boolean> orderComplete(@RequestBody TrdOrderTakeDeliveryVO takeDeliveryVO);

    /**
     * 订单结算 生成转账单或提交分账请求 定时任务
     * @param takeDeliveryVO
     * @return
     */
    @PutMapping(value = PREFIX + "/orderCreateSettleTransfer")
    CommonResult<Boolean> orderCreateSettleTransfer(@RequestParam("sysCode") Long sysCode, @RequestParam(value = "orderId", required = false) Long orderId);
    
    /**
     * O2O订单生成结算和分账定时任务
     * @param takeDeliveryVO
     * @return
     */
    @PostMapping(value = PREFIX + "/orderO2OGenerateSettleDivideDtl")
    CommonResult<Boolean> orderO2OGenerateSettleDivideDtl(@RequestBody O2OGenerateSettleParamVO paramVO);
    /**
     * O2O订单结算 提交分账请求 定时任务
     * @param takeDeliveryVO
     * @return
     */
    @PostMapping(value = PREFIX + "/orderO2OCreateSettle")
    CommonResult<Boolean> orderO2OSignAfterSettle(@RequestBody O2OSettleTaskParamVO settleTaskParamVO);
    
    
    /**
     * 更新订单结算流水状态
     *
     * @return
     */
    @PostMapping(value = PREFIX + "/updateOrderSettleState")
    CommonResult<Boolean> updateOrderSettleState(@RequestBody List<Long> settleIds);

    /**
     * 更新订单分账结算流水状态 （实际支付单号：订单单号 或 货到付款单号）
     *
     * @return
     */
    @PutMapping(value = PREFIX + "/updateOrderDivideSettleState")
    CommonResult<Boolean> updateOrderDivideSettleState(@RequestParam("orderNo") String orderNo);

    /**
     * 更新订单分账结算流水状态 （根据入驻商订单号和商户类型）
     *
     * @return
     */
    @PutMapping(value = PREFIX + "/updateDivideSettleState")
    CommonResult<Boolean> updateDivideSettleState(@RequestParam("supplierOrderNo") String supplierOrderNo, @RequestParam("merchantType") String merchantType);


    /**
     * 获取订单状态数量的角标
     *
     * @param branchId 门店ID
     * @return
     */
    @PostMapping(value = PREFIX + "/getOrderStatus")
    OrderStatusVO getOrderStatus(@RequestBody OrderStatusReqVO reqVO);

    /**
     * 获取订单金额统计
     *
     * @param branchId 门店ID
     * @return
     */
    @GetMapping(value = PREFIX + "/getOrderAmountStatistics")
    OrderAmountStatisticsVO getOrderAmountStatisticsVO(@RequestParam("branchId") Long branchId);

    /**
     * 订单全国商品确认收货
     *
     * @return
     */
    @PostMapping(value = PREFIX + "/nationwideProductConfirmReceive")
    CommonResult<Boolean> nationwideProductConfirmReceive(@RequestParam("supplierOrderDtlId") Long supplierOrderDtlId);

    /**
     * @Description: 业务员app获取订单列表
     * @Author: liyi
     * @Date: 2024/4/26 15:15
     */
    @PostMapping(value =PREFIX + "/coloneAppPageOrderList")
    CommonResult<ColonelAppOrderListTotalDTO> coloneAppPageOrderList(@RequestBody TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 获取常购商品列表
     * @Author: liuxingyu
     * @Date: 2024/5/7 14:32
     */
    @PostMapping(value = PREFIX + "/getEsStoreProductList")
    PageResult<StoreProductRespVO> getEsStoreProductList(@RequestBody StoreProductRequest storeProductRequest);

    /**
     * @Description: 业务员app获取订单列表详情
     * @Author: liyi
     * @Date: 2024/4/26 15:15
     */
    @PostMapping(value =PREFIX + "/getMemColoneAppOrderDetail")
    public List<TrdColonelAppOrderDetailRespVO> getMemColonelAppOrderDetail(@RequestParam("orderId") Long orderId);

    /**
     * @Description: 订单发货出库
     * @param trdOrderOperDTO
     * @return
     */
    @PostMapping(value =PREFIX + "/orderOutbound")
    CommonResult<Boolean> orderOutbound(@RequestBody TrdOrderOperDTO trdOrderOperDTO);

    /**
     * @Description: 单据配送完成（收货）
     * @param trdOrderOperDTO
     * @return
     */
    @PostMapping(value =PREFIX + "/orderTakeDelivery1")
    CommonResult<Boolean> orderTakeDelivery(@RequestBody TrdOrderOperDTO trdOrderOperDTO);

    @GetMapping(value = PREFIX + "/getOrderByOrderId")
    TrdOrder getOrderByOrderId(@RequestParam("orderId")Long orderId);

    /**
     * @Description: 根据条件获取某段时间的订单销售金额
     * @Author: liyi
     * @Date: 2024/5/14 15:15
     */
    @PostMapping(value =PREFIX + "/getSaleAmount")
    BigDecimal getSaleAmount (@RequestBody TrdColonelAppOrderListPageReqVO reqVO);

    @PostMapping(value =PREFIX + "/getCustomApiMaster")
    CustomApiMaster getCustomApiMaster(@RequestBody CustomApiMaster customApiMaster);

    @PostMapping(value =PREFIX + "/getCustomApiDetail")
    List<CustomApiDetail> getCustomApiDetail(@RequestParam ("apiNo") String apiNo);

    /**
     * 获取优惠券拓展表数据统计
     * @param couponTemplateIdList  优惠券模版ID集合
     * @return
     */
    @PostMapping(value =PREFIX + "/getCouponExtendTotal")
    CommonResult<List<CouponExtendTotalVO>> getCouponExtendTotal (@RequestBody List<Long> couponTemplateIdList);

    /**
     * 获取该优惠劵模板 和 参与客户的 使用优惠劵订单商品汇总数据
     * @param couponTemplateId 优惠券模版ID
     * @param customerIdList 使用优惠劵的客户ID集合
     * @return
     */
    @GetMapping(value =PREFIX + "/getCouponCustomerUseTotal")
    CommonResult<List<CouponCustomerOrderUseTotalDTO>> getCouponCustomerUseTotal (@RequestParam("couponTemplateId") Long couponTemplateId, @RequestParam("customerIdList") List<Long> customerIdList);

    /**
     *  根据活动id集合查询使用活动的订单数据汇总
     * @param activityIds
     * @return
     */
    @GetMapping(value =PREFIX + "/getPrmActivityByActivityIds")
    CommonResult<List<ActivityOrderTotalDTO>> getPrmActivityByActivityIds(@RequestParam("activityIds") List<Long> activityIds);

    /**
     *  根据订单编号查询订单信息
     * @param orderNo
     * @return
     */
    @GetMapping(value =PREFIX + "/getOrderByOrderNo")
    CommonResult<TrdOrder> getOrderByOrderNo(@RequestParam("orderNo") String orderNo);

    @GetMapping(value =PREFIX + "/selectOrderBySupplierOrderNo")
    TrdSupplierOrder selectOrderBySupplierOrderNo(@RequestParam("supplierOrderNo")String supplierOrderNo);

    @GetMapping(value =PREFIX + "/getOrderBySupplierOrderId")
    CommonResult<TrdSupplierOrder> getOrderBySupplierOrderId(@RequestParam("supplierOrderId") Long supplierOrderId);

    /**
     * @Description: 获取业务员 需日结 计算数据
     * @param reqVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getColonelOrderBusinessSettle")
    CommonResult<List<ColonelOrderDaySettleDTO>> getColonelOrderBusinessSettle(@RequestBody ColonelOrderDaySettleVO reqVO);

    @PostMapping(value =PREFIX + "/getBranchOrderSales")
    CommonResult<List<EsBranchOrderSalesRespDTO>> getBranchOrderSales(@RequestBody EsBranchOrderSalesReqVO reqVO);
    @PostMapping(value =PREFIX + "/getOrdersWithDelay")
    CommonResult<PageResult<SyncOrderCallDTO>> getOrdersWithDelay(@RequestBody SyncOrderPageReqDTO reqVO);

    @PostMapping(value =PREFIX + "/getAfterOrdersWithDelay")
    CommonResult<PageResult<SyncAfterOrderCallDTO>> getAfterOrdersWithDelay(@RequestBody SyncOrderPageReqDTO reqDTO);

    /**
     * @Description: 根据（销售/售后）订单获取订单(收款/退款)信息
     * @param reqVo
     * @return
     */
    @PostMapping(value =PREFIX + "/getOrderReceiptInfoBySupplierOrderNo")
    CommonResult<List<OrderReceiptRespDTO>> getOrderReceiptInfoBySupplierOrderNo(@RequestBody SyncReceiptSendDTO reqVo);

    /**
     * @Description: 主动获取收款单 （销售/售后）订单获取订单(收款/退款)信息
     * @param reqVo
     * @return
     */
    @PostMapping(value =PREFIX + "/getReceiptBySupplierOrderNo")
    CommonResult<List<OrderReceiptRespDTO>> getReceiptBySupplierOrderNo(@RequestBody SyncReceiptSendDTO reqVo);

    /**
     * 分享用户订单
     * @param saveReqVO
     * @return
     */
    @PostMapping(value =PREFIX + "/shareOrder")
    CommonResult<String> shareOrder(@RequestBody TrdOrderShareSaveReqVO saveReqVO);

    /**
     * 获取分享订单
     * @param shareKey
     * @return
     */
    @GetMapping(value =PREFIX + "/getShareOrder")
    CommonResult<TrdOrderShareRespVO> getShareOrder(@RequestParam("shareKey") String shareKey);

    /**
     * 获取入驻商订单详情
     * @param supplierOrderDtlId    入驻商订单详情
     * @return
     */
    @GetMapping(value = PREFIX + "/getSupplierOrderDtl")
    CommonResult<TrdSupplierOrderDtlDTO> getSupplierOrderDtl(@RequestParam("supplierOrderDtlId") Long supplierOrderDtlId);

    /**
     * 获取入驻商订单详情集合
     * @param supplierOrderDtlIdList    入驻商详情订单ID
     * @return
     */
    @PostMapping(value = PREFIX + "/getSupplierOrderDtlBatch")
    CommonResult<List<TrdSupplierOrderDtlDTO>> getSupplierOrderDtlBatch(@RequestBody List<Long> supplierOrderDtlIdList);

    /**
     * 获取门店某个指定上架商品ID, 已支付未完成收货的订单数量
     * @param branchTransitQtyReqVO 在途订单数量合计请求
     * @return  在途数量
     */
    @PostMapping(value = PREFIX + "/getBranchTransitQty")
    CommonResult<Long> getBranchTransitQty(@RequestBody BranchTransitQtyReqVO branchTransitQtyReqVO);

    @PostMapping(value = PREFIX + "/getLastOrderTime")
    CommonResult<List<SupplierOrderDtlInfoExportVO>> getLastOrderTime(@RequestBody List<Long> branchIds);

    /**
     * 获取首页销售、欠款、退单实时数据
     * @param reportOrderVO
     * @return
     */
    @PostMapping(value = PREFIX + "/getHomePagesCurrentSalesData")
    CommonResult<List<HomePagesCurrentSalesDataRespDTO>> getHomePagesCurrentSalesData(@RequestBody HomePagesReqVO reqVO);

    /**
     *  获取首页订单销售数据
     * @param reqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/getHomePagesOrderSalesData")
    CommonResult<List<HomePagesOrderSalesDataRespDTO>> getHomePagesOrderSalesData(@RequestBody HomePagesReqVO reqVO);

    /**
     *  获取订单退货销售数据
     * @param reqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/getHomePagesOrderAfterData")
    CommonResult<List<HomePagesOrderAfterDataRespDTO>> getHomePagesOrderAfterData(@RequestBody HomePagesReqVO reqVO);

    /**
     * 根据类型查询销售数据TOP10数据
     * @param reqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/getHomePagesSalesTop10Data")
    CommonResult<List<HomePagesSalesTop10DataRespDTO>> getHomePagesSalesTop10Data(@RequestBody HomePagesReqVO reqVO);
    /**
     * 入驻商订单数据导出
     * @return
     */
    @PostMapping(value = PREFIX + "/getSupplierOrderDtlInfoExport")
    CommonResult<List<SupplierOrderDtlInfoExportVO>> getSupplierOrderDtlInfoExport(@RequestBody DcOrderPageReqApiVO dcOrderPageReqApiVO);


    /**
     * 根据 用户账号或门店账号查询出是否下过订单
     * @param type member：用户，branch：门店
     * @param ids 用户或门店账号ID
     * @return 返回下过单的用户或门店ID
     */
    @GetMapping(value = PREFIX + "/checkMemberOrBranchExistsOrderSaleInfo")
    CommonResult<List<Long>> checkMemberOrBranchExistsOrderSaleInfo(@RequestParam("type") String type, @RequestParam("sysCode") Long sysCode, @RequestParam("ids") List<Long> ids);

    /**
     * @Description: 分页获取欠款订单数据从订单角度
     */
    @PostMapping(value = PREFIX + "/getTrdDebtOrderExportListGroupByOrder")
    CommonResult<List<DebtSupplierOrderDtlInfoExportVO>> getTrdDebtOrderExportListGroupByOrder(@RequestBody DcOrderPageReqApiVO debtOrderExportVO);


    /**
     * 根据门店ID获取业务员APP首页该客户订单信息
     *
     * @param branchId
     * @return
     */
    @GetMapping(value =PREFIX + "/getColonelAppBranchOrder")
    CommonResult<List<ColonelAppBranchOrderDTO>> getColonelAppBranchOrder(@RequestParam("branchId") Long branchId);

    /**
     * 根据业务员ID获取业务员APP首页的订单信息（当天）
     *
     * @param colonelId
     * @return
     */
    @GetMapping(value =PREFIX + "/getColonelAppPageOrder")
    CommonResult<List<ColonelAppPageOrderDTO>> getColonelAppPageOrder(@RequestParam("colonelId") Long colonelId);

    /**
     *  查询该areaId是否生成过数据
     */
    @GetMapping(value =PREFIX + "/getAreaIdExistOrder")
    CommonResult<Boolean> getAreaIdExistOrder(@RequestParam("areaId") Long areaId,@RequestParam("sysCode") Long sysCode);

    /**
     *  查询该areaId是否生成过数据
     */
    @GetMapping(value =PREFIX + "/getBranchIdExistOrder")
    CommonResult<Boolean>  getBranchIdExistOrder(@RequestParam("branchId") Long branchId);
    /**
     * 根据订单号查询入住商订单详情
     * @param orderNo
     * @return
     */
    @GetMapping(value =PREFIX + "/getSupplierOrderDtlByOrderNo")
    CommonResult<List<TrdSupplierOrderDtlVO>> getSupplierOrderDtlByOrderNo(@RequestParam("orderNo") String orderNo);

    /**
     * 根据订单号查询入住商订单详情
     * @param orderNo
     * @return
     */
    @GetMapping(value =PREFIX + "/getSupplierOrderDtlByOrderNos")
    CommonResult<List<TrdSupplierOrderDtlVO>> getSupplierOrderDtlByOrderNos(@RequestParam("orderNos") List<String> orderNos);


    /**
     * 获取门店当前截团日已下单金额
     */
    @PostMapping(value = PREFIX + "/getOrderCntAmt")
    CommonResult<OrderCutAmtDTO> getOrderCntAmt(@RequestBody OrderCutAmtDTO.CacheKey cacheKey);
    /**
     *  根据门店ID集合 平台商ID 查询 这些门店最后一次下单的订单信息
     */
    @PostMapping(value =PREFIX + "/getBranchLatestOrderByBranchIdList")
    CommonResult<List<TrdOrder>> getBranchLatestOrderByBranchIdList(@RequestParam("sysCode") Long sysCode);

    /**
     *  根据门店ID 查询 该门店最后一次下单的订单信息
     */
    @PostMapping(value =PREFIX + "/getBranchLatestOrderByBranchId")
    CommonResult<TrdOrder> getBranchLatestOrderByBranchId(@RequestParam("branchId") Long branchId);

    @PostMapping(value = PREFIX + "/selectSupplierOrder")
    CommonResult<List<SupplierOrderExportVO>> selectSupplierOrder(@RequestBody OrderPageReqVO param);

    /**
     * 回滚删除订单
     *
     * @param orderId
     * @return
     */
    @DeleteMapping(value = PREFIX + "/deleteOrder/{orderId}")
    public CommonResult<Boolean> deleteOrder(@PathVariable("orderId") Long orderId);

    @PostMapping(value = PREFIX + "/sendOrderHdfkSuccess")
    CommonResult<Boolean> sendOrderHdfkSuccess(@RequestBody RemoteSaveOrderVO orderVo);


    
    @GetMapping(value = PREFIX + "/getAllSupplierOrderNosByOrderNo")
    CommonResult<List<String>> getAllSupplierOrderNosByOrderNo(@RequestParam("orderNo")String orderNo);
    
    @GetMapping(value = PREFIX + "/getPayRefundReq")
    CommonResult<PayRefundReqVO> getPayRefundReq(@RequestParam("supplierOrderNo") String supplierOrderNo);
}
