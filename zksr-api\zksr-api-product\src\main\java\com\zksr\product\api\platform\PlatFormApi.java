package com.zksr.product.api.platform;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.platform.form.PlatFormImportForm;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "platFormApi", value = ApiConstants.NAME)
public interface PlatFormApi {

    String PREFIX = ApiConstants.PREFIX + "/platFormApi";

    @PostMapping(value = PREFIX + "/importDataEvent")
    CommonResult<String> importDataEvent(PlatFormImportForm form);

}
