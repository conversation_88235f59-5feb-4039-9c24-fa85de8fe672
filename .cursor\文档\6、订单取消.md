
### 订单取消的核心逻辑如下：

`TrdNormalOrderHandlerServiceImpl#orderCancel`
1. **参数校验**  
   首先判断 `trdSupplierOrder` 是否为空，为空则抛出“订单不存在”异常。

2. **获取主订单信息**  
   通过子单号查找主订单信息。

3. **未付款订单取消**
    - 如果订单支付状态为“未付款”，则直接将子订单状态更新为“已取消”，商品明细状态也更新为“已取消”。
    - 检查该主订单下所有子单是否都已取消，若是，则主订单状态也更新为“已取消”，并删除未支付订单的缓存，防止重复处理。
    - 调用 `orderNotPayCancel` 做未支付取消的后续处理。
    - 返还商品库存（调用 redisStockService 增加库存）。

4. **已付款订单取消**
    - 如果订单已支付，且支付时间小于10分钟，不允许取消（抛异常）。
    - 否则，将子订单状态更新为“取消中”，商品明细状态也更新为“取消中”。
    - 判断是否需要推送ERP取消：
        - 如果未推送ERP，或未对接安得ERP，直接模拟回调，调用 `orderCancelReceiveCallback`。
        - 否则，调用安得ERP接口发起取消请求，若失败则抛出“订单不能取消”异常。

5. **日志与库存释放**
    - 添加订单明细日志。
    - 如果需要，释放ERP预占库存（调用 `releaseErpStock`）。

**总结：**
- 未付款订单直接本地取消，返还库存，更新状态。
- 已付款订单需判断是否允许取消，部分需推送ERP，状态先置为“取消中”，ERP回调后再彻底取消。
- 整个过程有详细的状态流转和异常处理，保证订单取消的幂等性和一致性。

