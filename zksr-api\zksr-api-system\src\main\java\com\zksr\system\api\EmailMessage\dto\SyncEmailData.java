package com.zksr.system.api.EmailMessage.dto;

import lombok.*;

/**
* 同步数据Email实体
* @date 2024/7/18 15:32
* <AUTHOR>
*/
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncEmailData {

    /** 模板名称 */
    private String templateName;

    /** 请求头 */
    private String header;

    /** 请求体 */
    private String body;

    /** 验签 加密后的数据*/
    private String verify;

    /** 错误信息 */
    private String errMessage;

    public SyncEmailData(String header, String body, String verify, String errMessage) {
        this.header = header;
        this.body = body;
        this.verify = verify;
        this.errMessage = errMessage;
    }

}
