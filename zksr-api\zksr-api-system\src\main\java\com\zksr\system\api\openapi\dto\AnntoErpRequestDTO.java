package com.zksr.system.api.openapi.dto;

import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.RequestType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AnntoErpRequestDTO {
    @ApiModelProperty("api地址")
    private String api;
    @ApiModelProperty("请求数据")
    private Object data;
    @ApiModelProperty("请求ID，通常为UUID")
    private String reqId;
    @ApiModelProperty("入驻商ID")
    private Long supplierId;
    @ApiModelProperty("ERP请求类型")
    private RequestType requestType;
    @ApiModelProperty("B2B请求类型")
    private B2BRequestType b2bRequestType;
    @ApiModelProperty("操作类型")
    private OperationType operationType;
    @ApiModelProperty("当入驻商没有对接安得ERP，该次请求是否默认为自动失败")
    private boolean autoFail;
}
