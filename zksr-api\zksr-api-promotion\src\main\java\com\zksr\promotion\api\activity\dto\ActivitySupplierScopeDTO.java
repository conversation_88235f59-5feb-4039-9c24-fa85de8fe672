package com.zksr.promotion.api.activity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivitySupplierScopeDTO {

    /**
     * 活动id
     */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /**
     * 渠道id
     */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /**
     * 1-白名单 0-黑名单
     */
    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer whiteOrBlack = NumberPool.INT_ONE;
}
