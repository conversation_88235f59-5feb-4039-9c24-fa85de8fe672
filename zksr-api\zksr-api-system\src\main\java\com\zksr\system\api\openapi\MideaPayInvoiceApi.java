package com.zksr.system.api.openapi;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.invoice.dto.TransBlueInvoiceRequest;
import com.zksr.system.api.invoice.dto.TransRedInvoiceRequest;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 美的付智慧票API
 */
@FeignClient(
        contextId = "remoteMideaPayInvoiceApi",
        value = ApiConstants.NAME
)
public interface MideaPayInvoiceApi {
    String PREFIX = ApiConstants.PREFIX + "/mideaPayInvoice";

    /**
     * 蓝票开具
     * @param
     * @return
     */
    @PostMapping(value = PREFIX + "/transBlue")
    CommonResult<Boolean> transBlue(@RequestBody TransBlueInvoiceRequest request);

    /**
     * 红票开具
     * @param
     * @return
     */
    @PostMapping(value = PREFIX + "/transRed")
    CommonResult<Boolean> transRed(@RequestBody TransRedInvoiceRequest request);
}
