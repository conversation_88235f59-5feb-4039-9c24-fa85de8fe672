package com.zksr.system.api.commonMessage.dto;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.CommonMessagePushModeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.commonMessage.vo.SubscribeMsgConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 消息模版
 * @date 2024/6/13 18:21
 */
@Data
public class MessageTemplateDTO {

    /**
     * 消息模版id
     */
    @ApiModelProperty(value = "接收对象 branch-门店,colonel-业务员")
    private Long messageTemplateId;


    @ApiModelProperty(value = "消息模版平台商")
    private Long sysCode;

    /**
     * 0-停用,1-启用
     */
    @Excel(name = "0-停用,1-启用")
    @ApiModelProperty(value = "0-停用,1-启用")
    private Integer status;

    /**
     * 模版ID
     */
    @Excel(name = "模版ID")
    @ApiModelProperty(value = "模版ID")
    private String templateId;

    /**
     * 模版配置
     */
    @Excel(name = "模版配置")
    @ApiModelProperty(value = "模版配置")
    private SubscribeMsgConfigVO msgConfigVO;

    /**
     * 消息场景 0-用户下单,1-订单开始配送
     * {@link com.zksr.common.core.enums.CommonMessageSceneEnum}
     * */
    @Excel(name = "消息场景 0-用户下单,1-订单开始配送")
    private Integer scene;

    /**
     * 接收对象 branch-门店,colonel-业务员
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @Excel(name = "接收对象 branch-门店,colonel-业务员")
    private String receiveMerchant;

    /**
     * 0-小程序,1-公众号,2-微信门店助手,3-用户APP站内
     * {@link com.zksr.common.core.enums.CommonMessagePushModeEnum}
     */
    private Integer pushMode;

    public Map<String, String> buildSubscribeMessageDTO(MessageTable contextDTO) {
        String regex = "diy\\d+";
        Pattern pattern = Pattern.compile(regex);

        List<SubscribeMsgConfigVO.SubscribeField> configVOFields = this.msgConfigVO.getFields();
        HashMap<String, String> body = new HashMap<>();
        for (SubscribeMsgConfigVO.SubscribeField field : configVOFields) {
            if (StringUtils.isNotEmpty(field.getCustom()) && pattern.matcher(field.getVariable()).matches()) {
                body.put(field.getName(), field.getCustom());
            } else {
                // 动态获取上上下文参数
                String value = BeanUtil.getProperty(contextDTO.getContext(), field.getVariable());
                if (StringUtils.isNotEmpty(value)) {
                    body.put(field.getName(), value);
                }
            }
        }
        return body;
    }

    public CommonMessagePushModeEnum pushMode() {
        return CommonMessagePushModeEnum.fromValue(pushMode);
    }

    public List<MerchantTypeEnum> merchantList() {
        if (StringUtils.isEmpty(this.receiveMerchant)) {
            return new ArrayList<>();
        }
        return Arrays.stream(this.receiveMerchant.split(StringPool.COMMA)).map(MerchantTypeEnum::fromValue).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
