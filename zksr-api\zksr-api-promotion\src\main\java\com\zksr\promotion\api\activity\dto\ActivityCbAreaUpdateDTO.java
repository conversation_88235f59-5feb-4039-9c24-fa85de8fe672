package com.zksr.promotion.api.activity.dto;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/1/4 11:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCbAreaUpdateDTO {

    @ApiModelProperty("城市ID")
    private Long areaId;

    @ApiModelProperty("平台商ID")
    private Long sysCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActivityCbAreaUpdateDTO that = (ActivityCbAreaUpdateDTO) o;
        return Objects.equals(areaId, that.areaId) && Objects.equals(sysCode, that.sysCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(areaId, sysCode);
    }

    public boolean local() {
        return areaId == NumberPool.LOWER_GROUND_LONG;
    }

    public boolean global() {
        return areaId != NumberPool.LOWER_GROUND_LONG;
    }
}
