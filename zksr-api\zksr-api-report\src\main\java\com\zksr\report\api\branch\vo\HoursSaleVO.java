package com.zksr.report.api.branch.vo;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 14:53
 */
@Data
@NoArgsConstructor
public class HoursSaleVO {

    @ApiModelProperty("小时")
    private String hour;

    @ApiModelProperty("下单门店数量")
    private Long branchNum = NumberPool.LONG_ZERO;

    @ApiModelProperty("订单数量")
    private Long orderQty = NumberPool.LONG_ZERO;

    public HoursSaleVO(String hour) {
        this.hour = hour;
    }
}
