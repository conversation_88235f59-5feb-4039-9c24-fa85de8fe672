package com.zksr.member.api.member;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户地址对象 mem_member_address
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@ApiModel("用户地址 - mem_member_address Response VO")
public class MemMemberAddressRespDTO {
    /** ID主键 */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    @ApiModelProperty("一级区域城市ID, 省市区关联(省份)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long firstAreaCityId;

    @ApiModelProperty("二级区域城市ID, 省市区关联(城市)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long secondAreaCityId;

    /** 三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId) */
    @Excel(name = "三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long threeAreaCityId;

    /** 省份 */
    @Excel(name = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    private String districtName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 详细收货地址 */
    @Excel(name = "详细收货地址")
    private String deliveryAddress;

    /** 备注 */
    private String remark;

}
