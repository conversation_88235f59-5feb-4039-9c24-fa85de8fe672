package com.zksr.system.api.dc.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.system.api.partnerPolicy.dto.DcOtherSettingPolicyDTO;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 运营商对象 sys_dc
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Data
@ApiModel("运营商 - sys_dc Response VO")
public class SysDcRespVO {
    private static final long serialVersionUID = 1L;

    /**
     * 运营商编号
     */
    @ApiModelProperty(value = "联系电话")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /**
     * 平台编号
     */
    @Excel(name = "平台编号")
    @ApiModelProperty(value = "平台编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /**
     * 运营商状态0=正常,1=停用（0正常 1停用）
     */
    @Excel(name = "运营商状态0=正常,1=停用", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "运营商状态0=正常,1=停用")
    private String status;

    /**
     * 运营商备注
     */
    @Excel(name = "运营商备注")
    @ApiModelProperty(value = "运营商备注")
    private String memo;

    /**
     * 运营商地址
     */
    @Excel(name = "运营商地址")
    @ApiModelProperty(value = "运营商地址")
    private String address;

    /**
     * 运营商编号
     */
    @Excel(name = "运营商编号")
    @ApiModelProperty(value = "运营商编号")
    private String dcCode;

    /**
     * 运营商名称
     */
    @Excel(name = "运营商名称")
    @ApiModelProperty(value = "运营商名称")
    private String dcName;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contractName;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contractPhone;

    @Excel(name = "业务区域(城市)名称")
    @ApiModelProperty(value = "业务区域(城市)名称")
    private String areaName;

    /**
     * 业务区域(城市)ID
     */
    @Excel(name = "业务区域(城市)ID")
    @ApiModelProperty(value = "业务区域(城市)ID", required = true, example = "123456")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> sysAreaId;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    @ApiModelProperty(value = "公司名称", required = true, example = "xxxx公司")
    private String companyName;

    /**
     * 账号
     */
    @Excel(name = "账号")
    @ApiModelProperty(value = "账号", required = true, example = "xxxxx")
    private String userName;

    /** 银行名称 */
    @ApiModelProperty(value = "银行名称", required = true, example = "浦发银行")
    private String bankName;

    /** 银行支行 */
    @ApiModelProperty(value = "银行支行", required = true, example = "浦发银行麓谷支行")
    private String bankBranch;

    /** 银行卡号 */
    @ApiModelProperty(value = "银行卡号", required = true, example = "***************")
    private String accountNo;

    @ApiModelProperty("钱包充值软件商分佣比例, 最大1, 14% = 0.14, 最大0.29")
    private String walletSoftwareRate;

    //软件商支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;

    @ApiModelProperty("运营商其他配置")
    private DcOtherSettingPolicyDTO dcOtherSettingPolicyDTO;

    /**
     * 本地起送价
     */
    @ApiModelProperty(value = "本地起送价")
    private BigDecimal minAmt;

    /**
     * 全国起送价
     */
    @ApiModelProperty(value = "全国起送价")
    private BigDecimal globalMinAmt;
}
