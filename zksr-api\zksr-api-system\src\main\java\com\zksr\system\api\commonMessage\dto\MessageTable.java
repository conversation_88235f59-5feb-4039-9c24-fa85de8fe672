package com.zksr.system.api.commonMessage.dto;

import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.system.enums.MessageParamEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: {@link CommonMessageSceneEnum#NEW_ORDER_PUBLISH}
 * @date 2024/6/13 16:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "订阅通知参数容器")
public class MessageTable implements Serializable {

    @ApiModelProperty("参数容器")
    @lombok.Builder.Default
    private Map<String, String> context = new HashMap<>();

    /**
     * 自定义字段
     * @param paramEum  字段类型
     * @param param     字段值
     */
    public MessageTable put(MessageParamEum paramEum, String param) {
        context.put(paramEum.getFiled(), param);
        return this;
    }

    // 订单号
    public MessageTable setOrderNo(String param) {
        put(MessageParamEum.ORDER_NO, param);
        return this;
    }

    // 下单用户
    public MessageTable setMemberName(String param) {
        put(MessageParamEum.MEMBER_NAME, param);
        return this;
    }

    // 下单用户手机号
    public MessageTable setMemberPhone(String param) {
        put(MessageParamEum.MEMBER_PHONE, param);
        return this;
    }

    // 门店名称
    public MessageTable setBranchName(String param) {
        put(MessageParamEum.BRANCH_NAME, param);
        return this;
    }

    // 门店地址
    public MessageTable setBranchAddr(String param) {
        put(MessageParamEum.BRANCH_ADDR, param);
        return this;
    }

    // 商品名称
    public MessageTable setSpuName(String param) {
        put(MessageParamEum.SPU_NAME, param);
        return this;
    }

    // 订单金额
    public MessageTable setPayAmt(String param) {
        put(MessageParamEum.PAY_AMT, param);
        return this;
    }

    // 下单时间
    public MessageTable setOrderCreateTime(String param) {
        put(MessageParamEum.ORDER_CREATE_TIME, param);
        return this;
    }

    // 入驻商名称
    public MessageTable setSupplierName(String param) {
        put(MessageParamEum.SUPPLIER_NAME, param);
        return this;
    }

    // 入驻商联系方式
    public MessageTable setSupplierContactPhone(String param) {
        put(MessageParamEum.SUPPLIER_CONTACT_PHONE, param);
        return this;
    }
}
