package com.zksr.member.api.command.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel("操作指令 -  CommandAddOrderSkuListRespDTO 加单指令SKU商品清单列表 DTO")
public class CommandAddOrderSkuRespDTO {
    @ApiModelProperty(value = "普通加单指令ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long commandId;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店联系人名称")
    private String branchContactName;

    @ApiModelProperty(value = "门店联系电话")
    private String branchContactPhone;

    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

//    @ApiModelProperty(value = "普通指令返回数据集合")
//    private List<SkuPageRespVO> commandAddOrderExecDTOList;

    @Data
    @ApiModel("加单指令SKU商品清单明细")
    public static class skuDtl {
        @ApiModelProperty(value = "本地商品上架ID")
        private Long areaItemId;

        @ApiModelProperty(value = "skuId")
        private Long skuId;

        @ApiModelProperty(value = "spuId")
        private Long spuId;

        @ApiModelProperty(value = "sku单位大小；1-小；2-中；3-大")
        private Integer unitType;

        @ApiModelProperty(value = "sku单位加购数量")
        private Long skuQty;


        @ApiModelProperty(value = "spu产品名称")
        private String spuName;

    }
}
