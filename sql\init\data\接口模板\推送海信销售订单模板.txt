{
    "shopOrder": {
        "orderCode": "${supplierOrderNo}",
        "supplierId": "${supplierId}",
        "supplierName": "${supplierName}",
        "payTime": "${payTimeString}",
        "buyerNote": "${memo}",
        "shopId": "${branchId}",
        #set($subDiscountAmtFen = $subDiscountAmt * 100)
        "subDiscountAmt": ${subDiscountAmtFen},
        #set($subPayAmtFen = ($subOrderAmt - $subDiscountAmt) * 100)
        "subPayAmt": ${subPayAmtFen},
        #set($subOrderAmtFen = $subOrderAmt * 100)
        "subOrderAmt": ${subOrderAmtFen},
        "shopName": "${branchName}"
    },
    #if("$!{receiver}")
    "receiver": {
        "mobile": "${receiver.contactPhone}",
        "userId": "${receiver.memberId}",
        "receiveUserName": "${receiver.contactName}",
        #if("$!{receiver.threeAreaCityId}")
        "regionCode": "${receiver.threeAreaCityId}",
        #end
        #if("$!{receiver.districtName}")
        "region": "${receiver.districtName}",
        #end
        #if("$!{receiver.provinceName}")
        "province": "${receiver.provinceName}",
        #end
        #if("$!{receiver.firstAreaCityId}")
        "provinceCode": "${receiver.firstAreaCityId}",
        #end
        #if("$!{receiver.cityName}")
        "city": "${receiver.cityName}",
        #end
        #if("$!{receiver.secondAreaCityId}")
        "cityCode": "${receiver.secondAreaCityId}",
        #end
        "detail": "${receiver.deliveryAddress}"
    },
    #end
    #if("$!{invoice}")
    "invoice": {
        "title": "${invoice.invoiceTitle}",
        "phoneNumber": "${invoice.contactPhone}",
        #if("$!{invoice.invoiceType}")
        #if($invoice.invoiceType == 10)
        "type": 1,
        #elseif($invoice.invoiceType == 20)
        "type": 2,
        #end
        #end
        #if("$!{invoice.titleType}")
        #if($invoice.titleType == 10)
        "titleType": 1,
        #elseif($invoice.titleType == 20)
        "titleType": 2,
        #end
        #end
        "taxRegisterNo": "${invoice.taxpayerCode}",
        "registerAddress": "${invoice.companyAddress}",
        "registerPhone": "${invoice.companyPhone}",
        "registerBank": "${invoice.bankName}",
        "bankAccount": "${invoice.bankAccount}"
    },
    #end
    "skuOrders": [
        #foreach( $sub in $detailList)
        {
            #set($qty = $sub.orderUnitQty)
            #set($originalPrice = $sub.orderUnitPrice)
            #set($orderSalesUnitPriceFen = $sub.orderSalesUnitPriceFen)
            #set($dtlDiscountAmt = $sub.subOrderAmt-$sub.totalAmt)
            "erpItemNo": "${sub.itemSourceNo}",
            "lineNum": ${sub.lineNum},
            "quantity": ${qty},
            #if("$!{orderSalesUnitPriceFen}")
            "orderUnitPrice": ${orderSalesUnitPriceFen},
            #end
            #if("$!{originalPrice}")
            #set($originalPriceFen = $originalPrice * 100)
            "originFee": ${originalPriceFen},
            #end
            #if("$!{dtlDiscountAmt}")
            #set($dtlDiscountAmtFen = $dtlDiscountAmt * 100)
            "discount": ${dtlDiscountAmtFen},
            #end
            "skuCode": "${sub.skuId}",
            "skuName": "${sub.spuName}"
        }#if($foreach.hasNext),#end
        #end
    ]
}