package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 芯烨配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XpYunSettingPolicyDTO {

    /**
     * 芯烨平台key
     */
    @ApiModelProperty("芯烨平台key")
    private String key;

    /**
     * 芯烨云平台注册用户名（开发者 ID）
     */
    @ApiModelProperty("芯烨云平台注册用户名（开发者 ID）")
    private String user;

    /**
     * 打印机编号
     */
    @ApiModelProperty("打印机编号")
    private String sn;

    /**
     * 打印数量
     */
    @ApiModelProperty("打印数量")
    private String quantity;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    @ApiModelProperty("入驻商ID")
    private String supplierId;
}
