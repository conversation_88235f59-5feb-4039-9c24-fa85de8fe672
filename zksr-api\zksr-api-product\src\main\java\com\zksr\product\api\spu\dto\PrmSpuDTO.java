package com.zksr.product.api.spu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * 促销商品DTO
* <AUTHOR>
* @date 2024/5/21 10:56
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrmSpuDTO {

    /** 上架商品id */
    private Long itemId;

    /** 展示分类id */
    private Long classId;

    /** 展示分类名称 */
    private String className;

    /** 城市上架商品id */
    private Long areaItemId;

    /** 入驻商上架商品id */
    private Long supplierItemId;

    /** 城市展示分类id */
    private Long areaClassId;

    /** 城市展示分类名称 */
    private String areaClassName;

    /** 平台商展示分类id */
    private Long saleClassId;

    /** 平台商展示分类名称 */
    private String saleClassName;

    /** 平台商id */
    private Long sysCode;

    /** 城市id */
    private Long areaId;

    /** 上下架状态 */
    private Integer shelfStatus;

    /** 商品SPU id */
    private Long spuId;

    /** 商品sku id */
    private Long skuId;

    /** 入驻商id */
    private Long supplierId;

    /** 平台商管理分类id */
    private Long catgoryId;

    /** 平台商品牌id */
    private Long brandId;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    private String spuNo;

    /** 商品SPU名称 */
    private String spuName;

    /** 库存数量 */
    private Long stock;

    /** 是否删除 1-是 0-否 */
    private Long isDelete;

    /** keyword */
    private String keyword;

    /** 国际条码 */
    private String barcode;

    /** 单位-数据字典（sys_prdt_unit） */
    private Long unit;

    /** 属性数组，JSON 格式 */
    private String properties;

    /** 标准价 */
    private BigDecimal markPrice;

    /** 建议零售价 */
    private BigDecimal suggestPrice;

    /** 保质期 */
    private Integer expirationDate;		 // 保质期

    /** 参考进价 */
    private BigDecimal referencePrice;

    /** 参考售价 */
    private BigDecimal referenceSalePrice;

    /** 平台商管理分类名称 */
    private String catgoryName;

    /** 平台商品牌名称 */
    private String brandName;

    /** 排序序号 */
    private Integer sortNum;

    /** 上架时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 列表排序方式 */
    private String sortord;

    /** 列表排序顺序 */
    private Long sortOrder;

    /** 状态(数据字典 sys_common_status) */
    private Long status;

    /** 入驻商名称 */
    private String supplierName;


    /** 封面图（url） */
    private String thumb;

    /** 商品规格 */
    private String specName;

    /**
     * 商品类型 0：全国商品 1：本地商品
     */
    private Integer itemType;
}
