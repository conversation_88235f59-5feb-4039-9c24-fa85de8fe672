-- -----------------------------------  system mq 增加配置 start ---------------------------------
# Spring
spring:
  cloud:
    #spring-cloud-stream 配置
    stream:
      function:
        # spring-cloud-stream规范 函数式编程定义的BEAN name（这里只定义了消费者入口）
        definition: "openapi_batch_create_yh;"
      bindings:
        # 门店要货单补货数据
        openapi_batch_create_yh-out-0:
          destination: openapi_batch_create_yh
        openapi_batch_create_yh-in-0:
          destination: openapi_batch_create_yh
          group: openapi_batch_create_yh_group
-- -----------------------------------  system mq 增加配置 end ---------------------------------
-- -----------------------------------  product mq 增加配置 start ---------------------------------
# Spring
spring:
  cloud:
    #spring-cloud-stream 配置
    stream:
      function:
        # spring-cloud-stream规范 函数式编程定义的BEAN name（这里只定义了消费者入口）
        definition: "yhDataMatch"
      bindings:
        # 门店要货单数据匹配
        yhDataMatch-out-0:
          destination: yhDataMatch
        yhDataMatch-in-0:
          destination: yhDataMatch
          group: yhDataMatchGroup
-- -----------------------------------  product mq 增加配置 end ---------------------------------