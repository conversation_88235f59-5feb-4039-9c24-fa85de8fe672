package com.zksr.product.api.model.event;

import com.zksr.product.api.model.AbstractProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架事件
 * @date 2024/2/29 19:56
 */
@Data
public class EsSkuUpdateProductEvent  extends AbstractProductEvent {

    @ApiModelProperty("skuId")
    private List<Long> skuId;

    public EsSkuUpdateProductEvent() {
    }

    public EsSkuUpdateProductEvent(Long spuId) {
        this.skuId = Collections.singletonList(spuId);
    }

    public EsSkuUpdateProductEvent(List<Long> spuIds) {
        this.skuId = spuIds;
    }
}
