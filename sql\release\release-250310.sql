ALTER TABLE `zksr_product`.`prdt_spu`
    ADD COLUMN `pricing_way` TINYINT(1) DEFAULT 1 COMMENT '商品计价方式类型：1-普通商品，2-称重商品'
;

-- 新增商品计价方式类型字典
INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES ('商品计价方式类型', 'spu_pricing_way', '0', 'zksr', '2025-03-01 09:49:14', 'zksr', '2025-03-01 09:50:09', '商品计价方式类型');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '普通商品', '1', 'spu_pricing_way', NULL, 'default', 'N', '0', 'zksr', '2025-03-01 09:49:40', '', NULL, '商品新增默认普通商品');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '称重商品', '2', 'spu_pricing_way', NULL, 'default', 'N', '0', 'zksr', '2025-03-01 09:49:33', '', NULL, '称重商品默认单位为（千克）且只会有一个最小单位');

-- 新增商品单位类型字典 - KG 用于称重商品
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
    VALUES (0, '千克（KG）', '999', 'sys_prdt_unit', NULL, 'default', 'N', '0', 'zksr', '2025-03-01 09:49:33', '', NULL, '称重商品单独使用且只能是称重商品使用（不要私自调整，会导致商品异常）');

-- 更新入驻商售后订单明细表字段 类型为小数
ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    MODIFY COLUMN original_return_qty DECIMAL(12,4) DEFAULT 0 COMMENT '原申请退货最小单位数量',
    MODIFY COLUMN return_qty DECIMAL(12,4) DEFAULT 0 COMMENT '退货数量',
    MODIFY COLUMN return_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '售后单位数量',
    MODIFY COLUMN return_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '售后单位换算数量',
    MODIFY COLUMN order_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '订单购买单位数量'
;

-- 更新入驻商售后订单明细结算表字段 类型为小数
ALTER TABLE `zksr_trade`.`trd_supplier_after_settle`
    MODIFY COLUMN return_qty DECIMAL(12,4) DEFAULT 0 COMMENT '退货数量'
;

-- 更新入驻商售后订单明细优惠表字段 类型为小数
ALTER TABLE `zksr_trade`.`trd_after_discount_dtl`
    MODIFY COLUMN gift_qty DECIMAL(12,4) DEFAULT 0 COMMENT '赠品退货数量'
;

-- 更新入驻商订单明细表字段 类型为小数
ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    MODIFY COLUMN cancel_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货前取消数量',
    MODIFY COLUMN send_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货单位数量',
    MODIFY COLUMN send_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货数量',
    MODIFY COLUMN reject_unit_qty DECIMAL(12,4) DEFAULT 0 COMMENT '拒收单位数量',
    MODIFY COLUMN reject_qty DECIMAL(12,4) DEFAULT 0 COMMENT '拒收数量',
    MODIFY COLUMN total_num DECIMAL(12,4) DEFAULT 0 COMMENT '商品最小单位数量',
    MODIFY COLUMN order_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '订单购买单位换算数量',
    MODIFY COLUMN cancel_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '发货前取消单位换算数量',
    MODIFY COLUMN reject_unit_size DECIMAL(12,4) DEFAULT 0 COMMENT '拒收单位换算数量'
;

-- 更新入驻商订单表字段 类型为小数
ALTER TABLE `zksr_trade`.`trd_supplier_order`
    MODIFY COLUMN sub_cancel_qty DECIMAL(12,4) DEFAULT 0 COMMENT '发货前订单取消数量'
;

-- 更新商品SKU表字段 类型为小数
ALTER TABLE `zksr_product`.`prdt_sku`
    MODIFY COLUMN synced_qty DECIMAL(16,4) DEFAULT 0 COMMENT '已同步库存',
    MODIFY COLUMN sale_qty DECIMAL(16,4) DEFAULT 0 COMMENT '已售数量, 库存 - 已售 = 剩余',
    MODIFY COLUMN stock DECIMAL(16,4) DEFAULT 0 COMMENT '库存数量'
;

ALTER TABLE `zksr_product`.`prdt_spu`
    MODIFY COLUMN mid_size DECIMAL(12,4) DEFAULT 0 COMMENT '中单位换算数量（换算成最小单位）',
    MODIFY COLUMN large_size DECIMAL(12,4) DEFAULT 0 COMMENT '大单位换算数量（换算成最小单位）'
;

ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    MODIFY COLUMN item_qty DECIMAL(12,4) DEFAULT 0 COMMENT '商品最小单位数量'
;