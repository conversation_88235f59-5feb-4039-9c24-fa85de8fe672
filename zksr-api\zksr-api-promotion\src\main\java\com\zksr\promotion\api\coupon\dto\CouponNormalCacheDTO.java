package com.zksr.promotion.api.coupon.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.CouponReceiveScope;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    用户可领取优惠券缓存
 * @date 2024/3/31 14:51
 */
@Data
@Accessors(chain = true)
public class CouponNormalCacheDTO implements Serializable {

    @ApiModelProperty("优惠券模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty("平台ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    @ApiModelProperty("1-全国商品,2-本地商品")
    private Integer funcScope;

    /** 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（返券规则表）3-新用户注册  4-门店积分兑换 */
    @Excel(name = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", readConverterExp = "返=券规则表")
    private Integer receiveType;

    /** 入驻商ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("优惠券可领取开始时间")
    private Date templateStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("优惠券可领取结束时间")
    private Date templateEndDate;

    @ApiModelProperty(value = "有效期类型(数据字典);0-固定日期1-领取之后")
    private Integer expirationType;

    @ApiModelProperty("优惠券有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateStart;

    @ApiModelProperty("优惠券有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateEnd;

    @ApiModelProperty("领取后 disableDays 至 expireDays 日之间可用, expirationType=1时有效")
    private Integer disableDays;

    @ApiModelProperty("领取后 disableDays 至 expireDays 日之间可用, expirationType=1时有效")
    private Integer expireDays;

    @ApiModelProperty("优惠券总数量")
    private Integer couponQty;

    @ApiModelProperty("每人限领数量")
    private Integer limit;

    @ApiModelProperty("优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "数据字典coupon_spu_scope;0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券）")
    private Integer spuScope;

    @ApiModelProperty(value = "数据字典coupon_receive_scope领取范围; 0-全部可领取,1-指定渠道,2-指定城市,3-指定门店")
    private Integer receiveScope;

    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券")
    private Integer discountType;

    @ApiModelProperty(value = "优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用")
    private BigDecimal triggerAmt;

    @ApiModelProperty(value = "优惠券面额;满多少元可以减多少元(满减券设置)")
    private BigDecimal discountAmt;

    @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    private BigDecimal discountPercent;

    /** 最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。 */
    @Excel(name = "最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。")
    private BigDecimal discountLimitAmt;

    /**
     * 获取缓存有效期建议
     * @return
     */
    public Long expireTimes() {
        // 缓存key有效期为过期时间 + 3天
        if (Objects.isNull(this.templateEndDate)) {
            return 0L;
        }
        return ((this.getTemplateEndDate().getTime() - System.currentTimeMillis()) / 1000L)+ (3 * 86400);
    }

    public boolean isGlobal() {
        return Objects.nonNull(funcScope) && funcScope == NumberPool.INT_ONE;
    }

    public boolean isLocal() {
        return Objects.nonNull(funcScope) && funcScope == NumberPool.INT_TWO;
    }

    /**
     * 是否包含使用范围
     */
    public boolean hashSpuScope(CouponSpuScopeEnum couponSpuScopeEnum) {
        if (Objects.isNull(this.getSpuScope())) {
            return false;
        }
        // 获取使用范围
        String spuScopes = String.valueOf(this.getSpuScope());
        Set<String> set = Arrays.stream(spuScopes.split(StringPool.COMMA)).collect(Collectors.toSet());
        return set.contains(couponSpuScopeEnum.getScope().toString());
    }

    public boolean hashReceiveScope(CouponReceiveScope couponReceiveScope) {
        if (Objects.isNull(this.getReceiveScope())) {
            return false;
        }
        // 获取使用范围
        String receiveScopes = String.valueOf(this.getReceiveScope());
        Set<String> set = Arrays.stream(receiveScopes.split(StringPool.COMMA)).collect(Collectors.toSet());
        return set.contains(couponReceiveScope.getScope().toString());
    }
}
