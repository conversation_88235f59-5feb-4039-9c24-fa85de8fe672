package com.zksr.promotion.api.coupon.handler.applySpuScope;

import cn.hutool.core.util.NumberUtil;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.promotion.api.coupon.handler.CouponSpuScopeStrategy;
import com.zksr.promotion.api.coupon.vo.CouponSpuScopeValidReqVO;
import com.zksr.promotion.utils.CouponScopeUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 多范围验证
 * @date 2024/4/3 16:03
 */
@SuppressWarnings("all")
public class CouponCouponMultiSpuScopeStrategy implements CouponSpuScopeStrategy {

    @Override
    public void productFilter(List<OrderValidItemDTO> items, Set<CouponDTO> coupons, Map<Long, CouponTemplateDTO> couponTemplateMap, Set<CouponDTO> availableSet) {
        // 订单商品数据按入驻商分组
        Map<Long, List<OrderValidItemDTO>> supplierItemMap = items.stream().collect(Collectors.groupingBy(OrderValidItemDTO::getSupplierId));
        // 开始筛选符合要求的优惠券
        coupons.stream().filter(coupon -> Objects.nonNull(coupon.getSupplierId())).forEach(coupon -> {
            CouponTemplateDTO couponTemplate = couponTemplateMap.get(coupon.getCouponId());
            List<Long> supplierIdList = couponTemplate.getSupplierIdList();
            // 多入驻商兼容旧数据
            if (CollectionUtils.isEmpty(supplierIdList)) {
                supplierIdList = new ArrayList<>();
                supplierIdList.add(couponTemplate.getSupplierId());
            }
            // 表达式用
            List<Long> finalSupplierIdList = supplierIdList;
            // 获取当前优惠券的入驻商的商品
            List<OrderValidItemDTO> validItemDTOS = supplierItemMap.entrySet().stream().filter(t -> finalSupplierIdList.contains(t.getKey()))
                    .map(t -> t.getValue()).flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validItemDTOS)) {
                return;
            }
            // 过滤满赠条件的商品
            // 获取范围ID
            BigDecimal scopeAmt = BigDecimal.ZERO;
            // 验证商品
            for (OrderValidItemDTO itemDTO : validItemDTOS) {
                // 按照使用范围分组
                Map<Integer, List<CouponSpuScopeDTO>> scopeMap = coupon.getSpuScopeAs().stream().collect(Collectors.groupingBy(CouponSpuScopeDTO::getSpuScope));
                if (CouponScopeUtil.conditionSpuCopeApplyId(new CouponSpuScopeValidReqVO(itemDTO), scopeMap)) {
                    scopeAmt = scopeAmt.add(itemDTO.getAmt());
                }
            }
            if (NumberUtil.isGreaterOrEqual(scopeAmt, couponTemplate.getTriggerAmt())) {
                // 达到启用金额
                availableSet.add(coupon);
            }
        });
        coupons.removeAll(availableSet);
    }

}
