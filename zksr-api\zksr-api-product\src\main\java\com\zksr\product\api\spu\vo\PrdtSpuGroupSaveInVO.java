package com.zksr.product.api.spu.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.product.api.spu.vo.PrdtSpuSaveReqVO;
import com.zksr.product.api.property.vo.PrdtPropertySpuGroupSaveInVO;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupSaveInVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
*
 * 商品基本信息新增保存 请求实体
* <AUTHOR>
* @date 2024/3/2 11:17
*/
@Data
@ApiModel("商品基本信息--保存 请求实体")
public class PrdtSpuGroupSaveInVO {

    /** Spu对象 */
    @Excel(name = "Spu保存对象")
    @ApiModelProperty(value = "Spu保存对象",required = true)
    private PrdtSpuSaveReqVO spu;

    /** Sku集合 */
    @Excel(name = "Sku集合")
    @ApiModelProperty(value = "Sku集合" ,required = true)
    private List<PrdtSkuSpuGroupSaveInVO> skuList;

    /** 规格名称集合 */
    @Excel(name = "规格名称集合")
    @ApiModelProperty(value = "规格名称集合" ,required = true)
    private List<PrdtPropertySpuGroupSaveInVO> propertyList;

    @ApiModelProperty("平台商品SPU_ID")
    private Long platformSpuId;

    @ApiModelProperty("是否分享, false-不分享, true-分析")
    private Boolean shareFlag;
}
