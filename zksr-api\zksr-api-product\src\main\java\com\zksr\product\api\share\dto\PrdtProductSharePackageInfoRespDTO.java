package com.zksr.product.api.share.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/6 14:42
 * @注释
 */
@Data
@ApiModel("商品分享转发封装信息")
public class PrdtProductSharePackageInfoRespDTO {

    /** 上架商品ID */
    @ApiModelProperty("上架商品ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;


    /** 入驻商ID */
    @ApiModelProperty("入驻商ID ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 商品类型, local本地, global 全国 */
    @ApiModelProperty("商品类型, local本地, global 全国")
    private String type;

    /** SPU ID */
    @ApiModelProperty("SPU ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** SKU ID */
    @ApiModelProperty("SKU ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 封面图片 */
    @ApiModelProperty("封面图片")
    private String thumb;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 单位 */
    @ApiModelProperty("单位尺寸")
    private Integer unitSize;

    /** 销量 */
    @ApiModelProperty("销量")
    private Long saleQty;

    /** 平台展示分类ID */
    private Long classId;


    /** 库存 */
    @ApiModelProperty("库存")
    private Long stock;

    /** 起订 */
    @ApiModelProperty(value = "起订", example = "1")
    private Long minOq;

    /** 订货组数 */
    @ApiModelProperty(value = "订货组数", example = "1")
    private Long jumpOq;

    /** 限购 */
    @ApiModelProperty("限购数量")
    private Long maxOq;

    @ApiModelProperty(value = "商品促销标签列表")
    private List<SpuActivityLabelRespDTO> spuActivityLabelList;

    @ApiModelProperty(value = "优惠券活动标签")
    private List<CouponLabelVO> couponLabelList;


    /** 商品名称 */
    @ApiModelProperty("商品名称")
    private String spuName;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最旧生产日期", example = "2024-03-01 00:00:00")
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最新生产日期", example = "2024-03-31 00:00:00")
    private Date latestDate;


    @ApiModelProperty(value = "市场价", required = true, example = "99.99")
    private BigDecimal markPrice;

    @ApiModelProperty(value = "促销 价格", required = true, example = "99.99") // 通过会员等级，计算出折扣后价格
    private BigDecimal promotionPrice;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;

    @ApiModel(description = "促销互动标签")
    @Data
    public static class CouponLabelVO {
        @ApiModelProperty("优惠券名称")
        private String couponName;

        @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券")
        private Integer discountType;

        @ApiModelProperty(value = "优惠券面额;满多少元可以减多少元(满减券设置)")
        private BigDecimal discountAmt;

        @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
        private BigDecimal discountPercent;
    }
}
