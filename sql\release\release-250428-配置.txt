# 通过es kibana，开发工具，执行es索引命令
PUT /yh_data
{
    "settings":
    {
        "index":
        {
            "refresh_interval": "1s",
            "number_of_shards": "1",
            "number_of_replicas": "0"
        }
    },
    "mappings":
    {
        "properties":
        {
            "areaItemId":
            {
                "type": "long"
            },
            "batchYmd":
            {
                "type": "long"
            },
            "branchId":
            {
                "type": "long"
            },
            "checked":
            {
                "type": "integer"
            },
            "itemType":
            {
                "type": "long"
            },
            "lastSubmitQty":
            {
                "type": "integer"
            },
            "lastTime":
            {
                "type": "keyword"
            },
            "mallUnitType":
            {
                "type": "integer"
            },
            "pos30dayAvgSales":
            {
                "type": "integer"
            },
            "posBarcode":
            {
                "type": "keyword"
            },
            "posSafetyDays":
            {
                "type": "integer"
            },
            "posSafetyStock":
            {
                "type": "integer"
            },
            "posSalesQty":
            {
                "type": "integer"
            },
            "posSuggestQty":
            {
                "type": "integer"
            },
            "posUnitName":
            {
                "type": "keyword"
            },
            "skuId":
            {
                "type": "keyword"
            },
            "sourceYhQty":
            {
                "type": "integer"
            },
            "spuId":
            {
                "type": "keyword"
            },
            "spuName":
            {
                "type": "keyword"
            },
            "supplierId":
            {
                "type": "long"
            },
            "threeSaleClassId":
            {
                "type": "long"
            },
            "transitQty":
            {
                "type": "integer"
            }
        }
    }
}