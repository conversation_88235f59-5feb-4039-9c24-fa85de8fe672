package com.zksr.system.api.invoice.dto;


/**
 * 发票类型枚举
 */
public enum InvoiceTypeEnum {
    ELECTRONIC_GENERAL(1, "电子普票"),
    ELECTRONIC_SPECIAL(2, "电子专票"),
    PAPER_GENERAL(3, "纸质普票"),
    PAPER_SPECIAL(4, "纸质专票");

    private final Integer code;
    private final String desc;

    InvoiceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
