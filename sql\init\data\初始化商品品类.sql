
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503280006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503290001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503300002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503300003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '屈臣氏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503300004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '八宝粥' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '香飘飘' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '三养' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '科罗娜' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '喜力' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '乐醋坊' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '芙丝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '芙丝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504090001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '阿华田' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '金银花露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150013' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150016' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150017' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150018' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '集景' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150019' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '集景' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150020' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '集景' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150021' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150022' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150023' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150024' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150025' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504190001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504190002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '延中' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504230001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504290001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '果子熟了' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '兰芳园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505020002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505060001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505090001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '会稽山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505110001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505110002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505110003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290013' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290016' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290017' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '纯悦' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290018' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290019' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290020' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '福佳白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290021' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '喜力' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290022' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '旺旺' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290023' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '旺旺' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290024' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290025' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290026' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '三养' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290027' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '名仁' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290028' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '名仁' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290029' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290030' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290031' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290032' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290033' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '嘉士伯' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290034' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290035' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290036' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290037' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290038' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290039' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290040' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290041' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '柚香谷' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290042' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290043' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290044' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290046' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290047' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290048' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290049' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290050' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290051' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290052' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290053' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290054' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000000' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '农夫山泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '农夫山泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '农夫山泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000023' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000052' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000053' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000054' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000055' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '巴黎水' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000056' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000058' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000059' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '信远斋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000060' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408240008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408260001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '名仁' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202408260002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202409140001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202409190001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202409190002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202409190003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '果子熟了' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202409190004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '果子熟了' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202409190005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202410160001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202410160002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '依云' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202411010002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202411010003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '乌毡帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202411110001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202411190001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202411190002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '乌毡帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202411190003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202412060001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202412180001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '金星' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202412310001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '金星' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202412310002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '金星' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202412310003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501010001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '东鹏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501050001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501080001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501080002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501080003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501080004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501080007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501120001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202501140001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '胖东来' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502090001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502110001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502110002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '东鹏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502110005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '东鹏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502110006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '东鹏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502110007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '东鹏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502110008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '斐泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502190001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三麟' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502190002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '奈雪' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502190003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '好望水' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502190006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '成央记' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502190007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '乐天' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502190009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '天润' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502240001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '西域春' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502240002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '西域春' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502240003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '西域春' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202502240004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503010001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '杨掌柜' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503020001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '东北冻梨' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050016' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503050017' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060016' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060018' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060019' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060020' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060021' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060025' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060026' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060027' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060028' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060029' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060030' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060031' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503060033' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '崂山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2025030749966' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090013' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503090015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '百岁山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '百岁山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '景田' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '景田' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '景田' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100013' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '景田' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503100015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503260001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503260002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503270001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503270002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503280002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503280003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503280004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503280005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503280006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503290001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503300002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503300003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '屈臣氏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503300004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '八宝粥' and t3.catgory_name = '达利园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '香飘飘' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '三养' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '科罗娜' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '喜力' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '乐醋坊' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202503310009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '芙丝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '芙丝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504040009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504090001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '阿华田' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '金银花露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150013' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150016' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150017' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150018' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '集景' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150019' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '集景' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150020' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '集景' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150021' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150022' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150023' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150024' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '星巴克' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504150025' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504190001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504190002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '延中' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504230001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202504290001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '海河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '果子熟了' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '兰芳园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505010006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505020002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505060001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505090001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '会稽山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505110001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505110002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505110003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G202505120004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290001' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290003' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290004' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290005' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290006' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290007' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290008' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290010' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290011' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290012' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290013' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290014' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290015' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290016' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290017' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '纯悦' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290018' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290019' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290020' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '福佳白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290021' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '喜力' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290022' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '旺旺' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290023' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '旺旺' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290024' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290025' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290026' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '三养' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290027' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '名仁' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290028' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '名仁' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290029' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290030' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290031' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290032' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '杨协成' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290033' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '嘉士伯' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290034' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290035' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290036' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290037' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290038' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290039' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290040' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290041' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '柚香谷' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290042' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290043' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290044' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290046' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290047' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290048' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290049' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290050' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290051' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290052' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290053' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '大窑' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'G2504290054' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000000' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '农夫山泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000002' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '农夫山泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000009' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '农夫山泉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000023' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000052' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000053' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000054' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000055' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '巴黎水' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000056' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000058' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000059' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '信远斋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000060' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000061' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000062' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000063' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000064' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000065' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000066' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000067' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000068' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000069' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000070' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000071' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000072' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000073' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000074' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000075' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000076' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000077' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000078' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000079' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000080' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000081' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000082' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000083' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000084' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000085' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000086' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000087' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000088' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000089' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000090' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000091' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000092' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000093' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000094' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000097' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000101' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000102' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000103' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000104' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000105' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000108' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000109' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000110' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '佳得乐' and t3.catgory_name = '百事' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000111' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '美年达' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000112' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000113' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000114' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000115' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000116' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000117' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000118' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000119' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000120' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000121' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000122' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000123' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000124' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000125' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000126' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000127' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000128' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000129' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000130' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '八宝粥' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000132' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '八宝粥' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000133' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '八宝粥' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000134' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000135' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000136' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000137' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000138' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '怡宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000139' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000140' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000141' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000142' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000143' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000144' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000145' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000146' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000147' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000148' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000149' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000151' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000152' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000153' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000154' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000155' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000156' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000157' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000158' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000159' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000160' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000161' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000162' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000163' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000164' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000165' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000167' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000171' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000172' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000173' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000174' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000175' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000176' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000177' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000178' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '芬达' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000179' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000180' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000181' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000182' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000183' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000184' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000185' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '可口可乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000186' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000189' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000190' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000191' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000192' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000193' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000194' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000196' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000197' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000198' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '脉动' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000199' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '李子园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000200' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '李子园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000201' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '李子园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000202' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '李子园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000203' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '李子园' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000204' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '天喔' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000205' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '天喔' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000207' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000208' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000209' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000210' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000211' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000212' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000213' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000214' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000215' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000216' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000217' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000218' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000219' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000220' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000222' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000223' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000224' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '气泡水' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000226' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000227' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000228' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '元气森林' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000229' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000230' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '中沃' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000231' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000232' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000233' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000234' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000235' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000236' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000237' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '碳酸饮料' and t3.catgory_name = '北冰洋' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000238' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000239' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000240' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000241' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000242' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000243' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '维他' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000244' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '百岁山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000245' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '百岁山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000246' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '凉茶' and t3.catgory_name = '加多宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000247' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '凉茶' and t3.catgory_name = '王老吉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000248' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '凉茶' and t3.catgory_name = '王老吉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000249' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '凉茶' and t3.catgory_name = '王老吉' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000250' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '凉茶' and t3.catgory_name = '加多宝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000251' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '椰树' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000252' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '椰树' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000253' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '椰树' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000254' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = '屈臣氏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000255' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '东鹏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000256' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '红牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000257' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '红牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000258' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '红牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000259' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '椰树' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000260' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '乌龙茶' and t3.catgory_name = '三得利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000261' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '旺旺' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000262' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '雀巢' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000263' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '功能饮料' and t3.catgory_name = '宝矿力' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000264' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000265' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000266' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000267' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000268' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000270' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000271' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000272' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000273' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000274' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000275' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000277' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000278' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000279' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000280' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000281' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000282' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000283' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000284' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000285' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000287' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000288' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000290' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000291' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000292' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000294' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000295' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000296' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '咖啡' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000297' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000299' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000300' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000301' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '调制乳品' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000302' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000303' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬水' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000304' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000305' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000306' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000307' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '茶饮' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000308' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '柠檬茶' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000309' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '酸奶' and t3.catgory_name = '莫斯利安' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000310' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '纯牛奶' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000311' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000314' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '安慕希' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000315' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '安慕希' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000316' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000317' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000318' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '伊利' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000319' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '安慕希' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000320' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '纯牛奶' and t3.catgory_name = '金典' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000321' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '纯牛奶' and t3.catgory_name = '金典' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000322' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '安慕希' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000323' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '纯牛奶' and t3.catgory_name = '特仑苏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000324' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000325' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000326' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '纯牛奶' and t3.catgory_name = '特仑苏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000327' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '纯牛奶' and t3.catgory_name = '特仑苏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000328' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000329' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '蒙牛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000331' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '八宝粥' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000335' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '依云' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000337' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '依云' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000338' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '苏打水' and t3.catgory_name = 'Chang泰象' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000339' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = 'IF' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000340' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '果味饮料' and t3.catgory_name = 'IF' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000341' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '方便速食' and t2.catgory_name = '绿豆汤' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000355' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '方便速食' and t2.catgory_name = '八宝粥' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000356' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '牛奶' and t2.catgory_name = '调制乳品' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000357' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '方便速食' and t2.catgory_name = '八宝粥' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000358' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '方便速食' and t2.catgory_name = '八宝粥' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000359' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '方便速食' and t2.catgory_name = '八宝粥' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000360' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '方便速食' and t2.catgory_name = '八宝粥' and t3.catgory_name = '银鹭' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000361' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000362' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '水饮' and t2.catgory_name = '矿泉水' and t3.catgory_name = '娃哈哈' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000364' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '五粮液' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000367' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '五粮液' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000368' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '剑南春' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000369' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '剑南春' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000370' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '国窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000371' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '国窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000372' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '习酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000373' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '汾酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000375' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '茅台' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000382' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '茅台' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000383' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '舍得' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000384' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '郎酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000385' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '水井坊 ' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000386' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '水井坊 ' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000387' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000388' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000389' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000390' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000391' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000392' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000393' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000394' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '洋河' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000395' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '古井贡酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000396' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '古井贡酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000397' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '口子窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000403' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '口子窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000404' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '汾酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000408' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '汾酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000409' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '迎驾贡酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000418' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '迎驾贡酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000420' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000423' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000425' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000426' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000427' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000428' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000429' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000430' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '牛栏山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000431' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红星二锅头' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000432' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红星二锅头' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000433' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红星二锅头' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000434' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红星二锅头' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000435' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红星二锅头' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000436' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红星二锅头' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000437' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '小糊涂仙' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000439' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '小糊涂仙' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000440' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '泸州老窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000443' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '泸州老窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000444' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '泸州老窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000445' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '泸州老窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000446' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '泸州老窖' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000447' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '郎酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000450' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '郎酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000453' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '郎酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000454' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '劲酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000455' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '劲酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000456' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '劲酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000457' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '西凤' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000458' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '江小白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000462' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '江小白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000463' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '江小白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000464' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '江小白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000465' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '老村长' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000470' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '老村长' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000471' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '衡水老白干' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000472' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '尖庄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000476' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '五粮醇' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000477' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '五粮醇' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000478' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '五粮醇' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000480' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '绵竹大曲' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000481' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000482' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000483' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000484' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000485' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000486' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '真露' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000487' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '梅见' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000488' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '梅见' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000489' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '宣酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000491' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '神仙' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000494' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '神仙' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000495' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '红高粱' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000505' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '贵州习水大曲' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000508' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '白酒' and t3.catgory_name = '东北坊' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000511' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000514' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000515' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000516' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '百威' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000517' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '朝日' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000518' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '朝日' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000519' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '蓝带' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000520' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '蓝带' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000521' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '麒麟一番' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000522' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '麒麟一番' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000523' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '喜力' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000524' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '福佳白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000526' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '福佳白' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000527' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '科罗娜' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000528' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '1664' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000529' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '1664' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000530' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '1664' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000531' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '1664' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000532' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000533' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000534' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000535' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000536' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000537' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000538' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000539' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000540' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000541' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '雪花' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000542' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000543' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000544' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000545' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000546' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000547' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000548' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000549' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000550' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '青岛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000551' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000552' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000553' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000554' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000555' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '光明' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000556' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '哈尔滨啤酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000557' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '乌苏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000559' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '乌苏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000560' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '乌苏' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000561' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '范佳乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000562' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '范佳乐' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000563' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000564' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000565' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000566' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000567' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000568' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000569' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000570' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000571' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000572' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000573' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000574' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000575' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '啤酒' and t2.catgory_name = '啤酒' and t3.catgory_name = '锐澳' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000576' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '古越龙山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000577' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '古越龙山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000578' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '古越龙山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000580' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '古越龙山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000583' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '石库门' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000585' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '石库门' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000586' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '石库门' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000587' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '石库门' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000588' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000590' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000591' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000592' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000593' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000594' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000595' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000596' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '和酒' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000597' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '乌毡帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000598' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '乌毡帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000599' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '乌毡帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000601' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '乌毡帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000602' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '会稽山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000603' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '会稽山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000604' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '会稽山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000605' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '古越龙山' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000606' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '沙洲优黄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000609' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '沙洲优黄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000611' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '沙洲优黄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000612' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '沙洲优黄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000613' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '沙洲优黄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000614' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '沙洲优黄' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000615' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '国标十年' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000620' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '嘉善元红' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000621' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '侬好' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000622' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '黄酒' and t3.catgory_name = '绍兴花雕' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000624' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '杰克丹尼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000632' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '百龄坛' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000633' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '芝华士' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000635' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '金宾波本' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000636' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '金宾波本' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000637' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '绝对' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000640' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '尊尼获加' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000641' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '尊尼获加' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000642' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '孟买蓝宝石' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000643' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '野格' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000644' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '百加得' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000645' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '百加得' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000646' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '摩根船长' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000647' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '摩根船长' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000648' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '摩根船长' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000649' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = '御虎' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000651' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '洋酒' and t3.catgory_name = 'SKYY' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000652' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '奔富' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000663' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '奔富' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000666' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '奔富' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000667' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '张裕' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000678' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '长城' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000679' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '长城' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000680' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = 'Dynasty经典王朝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000682' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = 'Dynasty经典王朝' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000683' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '奥兰小红帽' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000684' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '加州乐事 ' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000685' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '红魔鬼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000686' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '红魔鬼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000687' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '红魔鬼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000688' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '红魔鬼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000689' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '红魔鬼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000690' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '中外名酒' and t2.catgory_name = '红酒' and t3.catgory_name = '红魔鬼' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000691' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000698' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000699' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000700' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000701' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000702' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000703' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000704' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000705' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000706' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000707' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000708' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000709' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000710' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000711' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000712' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000713' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000714' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000715' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000716' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000717' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000718' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000719' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000720' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000721' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000722' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000723' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000724' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000725' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000726' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000727' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000728' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000729' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000730' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000731' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '康师傅' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000732' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000733' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000734' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000735' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000736' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000737' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000738' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000739' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000740' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '袋装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000741' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '统一' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000742' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '休闲食品' and t2.catgory_name = '干脆面' and t3.catgory_name = '脆司令' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z000978' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '休闲食品' and t2.catgory_name = '袋装' and t3.catgory_name = '三养' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z001177' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '休闲食品' and t2.catgory_name = '酸辣粉' and t3.catgory_name = '食族人' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z001178' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '泡面' and t2.catgory_name = '桶装' and t3.catgory_name = '合味道' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z001179' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '休闲食品' and t2.catgory_name = '酸辣粉' and t3.catgory_name = '食族人' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z001186' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
update prdt_spu set catgory_id = (select t3.catgory_id from prdt_catgory t1, prdt_catgory t2, prdt_catgory t3 where t1.sys_code = 615051269697175552 and t1.catgory_name = '休闲食品' and t2.catgory_name = '酸辣粉' and t3.catgory_name = '正文' and t1.level = 1 and t2.level = 2 and t3.level = 3 and t3.pid = t2.catgory_id and t2.pid = t1.catgory_id) where spu_no = 'Z001187' and sys_code = 615051269697175552 and supplier_id = 615802236138946560;
