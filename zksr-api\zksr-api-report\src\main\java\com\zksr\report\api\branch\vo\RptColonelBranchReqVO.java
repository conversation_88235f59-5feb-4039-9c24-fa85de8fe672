package com.zksr.report.api.branch.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 统计业务员门店月销售数据 req
 * @date 2024/11/25 9:26
 */
@Data
@NoArgsConstructor
public class RptColonelBranchReqVO {

    @ApiModelProperty("业务员ID集合")
    private List<Long> colonelIdList;

    @ApiModelProperty("月ID, 格式yyyyMM")
    private Integer monthId;

    public RptColonelBranchReqVO(List<Long> colonelIdList, Integer monthId) {
        this.colonelIdList = colonelIdList;
        this.monthId = monthId;
    }
}
