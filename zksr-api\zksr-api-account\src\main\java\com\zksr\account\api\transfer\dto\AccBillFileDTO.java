package com.zksr.account.api.transfer.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户账单文件备份实体
 * @date 2024/10/30 11:45
 */
@Data
public class AccBillFileDTO {
    /** 账单日期 */
    @Excel(name = "账单日期")
    private String date;

    /** 文件 */
    @Excel(name = "文件")
    private String file;

    /** 商户号 */
    @Excel(name = "商户号")
    private String altNo;

    /** 文件类型 */
    private String type;

    /** 创建时间*/
    private Date createTime;
}
