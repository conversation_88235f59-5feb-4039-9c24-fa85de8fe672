package com.zksr.account.model.merchant.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付平台商户余额
 * @date 2024/4/20 15:36
 */
@Data
@ApiModel(description = "支付平台商户余额")
public class PayPlatformAccountVO {

    @ApiModelProperty("账户余额")
    private BigDecimal balance = BigDecimal.ZERO;

    @ApiModelProperty("冻结余额")
    private BigDecimal frozenBalance = BigDecimal.ZERO;

    @ApiModelProperty("可结算金额, 入驻商可实际提现金额")
    private BigDecimal settleAmt = BigDecimal.ZERO;

    @ApiModelProperty("当日交易冻结资金")
    private BigDecimal d0BalanceAmt = BigDecimal.ZERO;

    /**
     * {@link com.zksr.common.core.enums.PayChannelEnum}
     */
    @ApiModelProperty("支付平台")
    private String platform;

    public static PayPlatformAccountVO DEFAULT = new PayPlatformAccountVO();
}
