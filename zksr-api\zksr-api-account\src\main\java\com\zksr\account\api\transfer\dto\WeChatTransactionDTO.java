package com.zksr.account.api.transfer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatTransactionDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transactionTime; // 交易时间
    private String publicAccountId; // 公众账号ID
    private String merchantId; // 交易商户号
    private String weChatOrderNo; // 微信订单号
    private String merchantOrderNo; // 商户订单号
    private String userIdentifier; // 用户标识
    private String transactionStatus; // 交易状态
    private String weChatRefundNo; // 微信退款单号
    private String merchantRefundNo; // 商户退款单号
    private BigDecimal refundAmount; // 退款金额
    private String refundStatus; // 退款状态
    private String productName; // 商品名称
    private String merchantData; // 商户数据包
    private BigDecimal technicalServiceFee; // 技术服务费
    private String serviceFeeRate; // 技术服务费费率
    private BigDecimal orderAmount; // 订单金额
    private BigDecimal refundRequestAmount; // 申请退款金额
    private String settlementStatus; // 结算状态
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settlementTime; // 结算时间
}