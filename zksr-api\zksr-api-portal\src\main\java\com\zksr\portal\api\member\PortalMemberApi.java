package com.zksr.portal.api.member;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.portal.api.dto.BranchRegisterReqDTO;
import com.zksr.portal.api.dto.BranchRegisterRespDTO;
import com.zksr.portal.api.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(
        contextId = "remotePortalMemberApi",
        value = ApiConstants.NAME
)
public interface PortalMemberApi {

    String PREFIX = ApiConstants.PREFIX + "/member";

    /**
     * 门店注册
     * @param request
     * @return
     */
    @PostMapping(PREFIX + "/registerBranch")
    CommonResult<BranchRegisterRespDTO> registerBranch(@RequestBody BranchRegisterReqDTO request) ;

}
