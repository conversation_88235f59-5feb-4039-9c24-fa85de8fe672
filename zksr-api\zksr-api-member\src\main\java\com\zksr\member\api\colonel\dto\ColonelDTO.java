package com.zksr.member.api.colonel.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024年03月28日 09:31
 * @description: ColonelDTO
 */
@Data
@NoArgsConstructor
public class ColonelDTO implements Serializable {
    /** 业务员id */
    private Long colonelId;

    /** 平台商id */
    private Long sysCode;

    /** 城市id */
    private Long areaId;

    /** 业务员手机号 */
    private String colonelPhone;

    /** 业务员名 */
    private String colonelName;

    /** 业务员级别（职务） */
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    private Long pcolonelId;

    /** 上级业务员名称 */
//    @Excel(name = "上级业务员名称")
    private String pcolonelName;

    /** 性别（数据字典） */
    private Integer sex;

    /** 状态 1正常 0停用 */
    private Integer status;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date birthday;

    /** 籍贯 */
    @Excel(name = "籍贯")
    private String birthplace;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryDate;

    /** 学历(数据字典) */
    private String edu;

    /** 身份证号 */
    private String idcard;

    /** 提成系数 */
    private BigDecimal percentageRate;

    /** 联系地址 */
    private String contactAddr;

    /** 备注 */
    private String memo;

    /** 是否是业务管理员（Y:是，N:否） */
    private String isColonelAdmin;

    /** 部门 */
    private Long deptId;

    /** APP下单改价（Y:是，N:否） */
    private String appOrderPriceAdjust;

    /** APP退货改价（Y:是，N:否） */
    private String appAfterPriceAdjust;

    /** 下单自动审核（Y:是，N:否） */
    private String orderAutoApprove;

    /** 用户ID */
    private Long userId;

    /** 审核状态 0待审核 1审核通过 2审核不通过 */
    private Integer auditState;

    /** 审核备注 */
    private String auditMemo;

    /** 业务员头像 */
    private String avatarImages;

    /** 入职时长 */
    private String entryDuration;

    /** 签名密钥 （公钥） */
    private String signSecret;

    /** 签名密钥 (私钥)*/
    private String signSecretPrivate;


    /** 业务员对应平台所绑定的小程序APPId */
    private String appId;

    /** 业务员对应平台所绑定的小程序 跳转地址*/
    private String miniJumpAddress;

    /**
     * 公众号openid
     */
    private String publishOpenid;

    /**
     * 平台名称（业务员APP使用）
     */
    private String partnerName;

    /** 运营商Id（业务员APP使用） */
    private Long dcId;

    /**
     * 运营商名称（业务员APP使用）
     */
    private String dcName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ColonelDTO that = (ColonelDTO) o;
        return Objects.equals(colonelId, that.colonelId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(colonelId);
    }
}
