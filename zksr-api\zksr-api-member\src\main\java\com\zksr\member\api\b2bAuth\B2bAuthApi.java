package com.zksr.member.api.b2bAuth;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @time 2024/8/15
 * @desc    微信b2b门店认证授权
 */

@FeignClient(
        contextId = "b2bAuthApi",
        value = ApiConstants.NAME
)
public interface B2bAuthApi {

    String PREFIX = ApiConstants.PREFIX + "/b2bAuthApi";

    /**
     * 获取微信是否绑定门店授权
     *
     * @param appid     小程序appid
     * @param openid    小程序openid
     * @param branchId  门店ID
     * @return  授权记录
     */
    @GetMapping(PREFIX + "/getBind")
    CommonResult<MemMemberOpenAuthDTO> getBind(@RequestParam("appid") String appid, @RequestParam("openid") String openid, @RequestParam("branchId") Long branchId);

    /**
     * 更新认证授权
     * @param authDTO
     */
    @PostMapping(PREFIX + "/updateAuth")
    void updateAuth(@RequestBody MemMemberOpenAuthDTO authDTO);
}
