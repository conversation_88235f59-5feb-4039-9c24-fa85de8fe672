package com.zksr.product.api.spu.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel("商品SPU - PrdtProductPageReqVO下拉分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtProductPageReqVO extends PageParam {

    @ApiModelProperty(value = "商品SPU名称、编号 模糊搜索")
    private String product;

    @ApiModelProperty(value = "入驻商ID集合", hidden = true)
    private List<Long> supplierIds;
}
