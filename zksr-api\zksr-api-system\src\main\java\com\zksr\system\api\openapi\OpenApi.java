package com.zksr.system.api.openapi;

import com.zksr.common.core.domain.erp.ErpBean;
import com.zksr.common.core.domain.vo.openapi.TradeOutErpRequest;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "remoteOpenApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface OpenApi {
    String PREFIX = ApiConstants.PREFIX + "/openApi";



    /**
     * b2b销售单推送erp系统接口
     * @param data
     */
    @PostMapping(value = PREFIX + "/sendErpOrder")
    String synErpXhSheet(@RequestBody TradeOutErpRequest data);

    /**
     * b2b退货单推送erp系统接口
     * @param data
     * @return
     */
    @PostMapping(value = PREFIX + "/sendErpAfter")
    void synErpThSheet(@RequestBody ErpBean data);

    /**
     * 门店信息推送erp接口
     * @param data
     */
    @PostMapping(value = PREFIX + "/synErpBranch")
    void synErpBranch(@RequestBody ErpBean data);

    /**
     * B2B销售订单推送erp接口
     * @param data
     */
    @PostMapping(value = PREFIX + "/synErpSupplierOrder")
    void synErpSupplierOrder(@RequestBody ErpBean data);

    /**
     * 售后订单推送erp接口
     * @param data
     */
    @PostMapping(value = PREFIX + "/synErpSupplierAfter")
    void synErpSupplierAfter(@RequestBody ErpBean data);


    /**
     * B2B收款或退款信息推送erp接口
     * @param data
     */
    @PostMapping(value = PREFIX + "/synErpReceipt")
    void synErpReceipt(@RequestBody ErpBean data);
}
