package com.zksr.member.api.command.dto;

import cn.hutool.log.Log;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel("操作指令 - 业务员加单执行结果 CommandAddOrderExecVO")
public class CommandAddOrderExecDTO {

    @ApiModelProperty(value = "普通指令Id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long ordinaryCommandId;

    @ApiModelProperty(value = "推送加单金额")
    private BigDecimal pushAmt;

    @ApiModelProperty(value = "推送加单sku数量")
    private Long pushSkuQty;

    @ApiModelProperty(value = "订单下单金额")
    private BigDecimal orderAmt;

    @ApiModelProperty(value = "订单下单sku数量")
    private Long orderSkuQty;

    @ApiModelProperty(value = "达成率")
    private BigDecimal achievementRate;

    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date completeTime;

    @ApiModelProperty(value = "执行结果JSON", hidden = true)
    private String execRes;

    @ApiModelProperty(value = "普通指令状态; 1-进行中，2：已完成")
    private Long ordinaryStatus;


}
