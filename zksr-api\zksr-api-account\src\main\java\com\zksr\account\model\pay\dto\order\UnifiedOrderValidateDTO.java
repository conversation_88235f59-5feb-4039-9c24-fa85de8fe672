package com.zksr.account.model.pay.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单支付验证结果
 * @date 2024/10/17 9:23
 */
@Data
public class UnifiedOrderValidateDTO {

    @ApiModelProperty("true-验证成功, false-验证失败")
    private Boolean success;

    @ApiModelProperty("0-正常, 1-需要处理微信请求分账是否完成")
    private int code = 0;

    @ApiModelProperty("验证失败原因")
    private String msg;

    public UnifiedOrderValidateDTO() {
    }

    public UnifiedOrderValidateDTO(Boolean success) {
        this.success = success;
    }

    public UnifiedOrderValidateDTO(Boolean success, String msg) {
        this.success = success;
        this.msg = msg;
    }

    public static UnifiedOrderValidateDTO success() {
        return new UnifiedOrderValidateDTO(true);
    }

    public static UnifiedOrderValidateDTO fail(String msg) {
        return new UnifiedOrderValidateDTO(false, msg);
    }
}
