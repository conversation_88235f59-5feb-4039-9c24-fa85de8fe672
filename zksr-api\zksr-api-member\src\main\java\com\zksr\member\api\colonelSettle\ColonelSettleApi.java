package com.zksr.member.api.colonelSettle;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

@FeignClient(
        contextId = "remoteColonelSettleApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface ColonelSettleApi {

    String PREFIX = ApiConstants.PREFIX + "/colonelSettle";

    /**
     * 业务员 - 业务日结
     * @param sysCode 平台编码
     * @param date 日期
     * @return
     */
    @GetMapping(PREFIX + "/colonelDaySettlement")
    CommonResult<Boolean> colonelDaySettlementJob(@RequestParam("sysCode") Long sysCode, @RequestParam("date") Date date);

    /**
     * 业务员 - 业务月结
     * @param sysCode 平台编码
     * @param date 日期
     * @return
     */
    @GetMapping(PREFIX + "/colonelMonthSettlementJob")
    CommonResult<Boolean> colonelMonthSettlementJob(@RequestParam("sysCode") Long sysCode, @RequestParam("date") Date date);

    /**
     * 业务员 - 业务结算（用于调整错误数据）
     * @param sysCode 平台编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    @GetMapping(PREFIX + "/colonelSettlementJob")
    CommonResult<Boolean> colonelSettlementJob(@RequestParam("sysCode") Long sysCode, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);
}
