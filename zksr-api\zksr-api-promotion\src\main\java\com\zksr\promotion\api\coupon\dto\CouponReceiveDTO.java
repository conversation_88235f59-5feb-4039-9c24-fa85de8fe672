package com.zksr.promotion.api.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 异步给门店发送优惠券
 * @date 2024/4/2 9:19
 */
@Data
@ApiModel(description = "异步给门店发送优惠券")
public class CouponReceiveDTO {

    @ApiModelProperty("优惠券模版ID集合")
    private List<Long> couponTemplateIds;

    @ApiModelProperty("优惠券ID:状态ID")
    private Map<Long, Long> couponStatusMap = new HashMap<>();

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("用户ID")
    private Long memberId;

    @ApiModelProperty("优惠券批次ID")
    private Long couponBatchId;

    @ApiModelProperty("领取模式,0-验证库存, 1-直接发放")
    private Integer mode = 0;

    public CouponReceiveDTO() {
    }

    public CouponReceiveDTO(List<Long> couponTemplateIds, Long branchId, Long memberId, Long couponBatchId) {
        this.couponTemplateIds = couponTemplateIds;
        this.branchId = branchId;
        this.memberId = memberId;
        this.couponBatchId = couponBatchId;
    }
}
