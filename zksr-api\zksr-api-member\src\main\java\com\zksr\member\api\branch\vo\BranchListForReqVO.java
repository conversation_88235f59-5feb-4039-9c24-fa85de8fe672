package com.zksr.member.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 批量获取门店数据
 * @date 2024/11/14 8:50
 */
@Data
@ApiModel(description = "批量获取门店循环")
public class BranchListForReqVO {

    @ApiModelProperty("批次门店最小ID")
    private Long minId;

    @ApiModelProperty("指定平台商")
    private Long sysCode;

    @ApiModelProperty("一次获取数量")
    private Long size;
}
