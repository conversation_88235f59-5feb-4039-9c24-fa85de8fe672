package com.zksr.report.api.branch.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/28 16:59
 */
@Data
@NoArgsConstructor
public class CategorySaleTotalVO {

    @ApiModelProperty("管理分类")
    private String categoryName;

    @ApiModelProperty("一级管理分类ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long categoryId;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmt;

    public CategorySaleTotalVO(Long categoryId, BigDecimal orderAmt) {
        this.categoryId = categoryId;
        this.orderAmt = orderAmt;
    }

    public CategorySaleTotalVO(String categoryName, Long categoryId, BigDecimal orderAmt) {
        this.categoryName = categoryName;
        this.categoryId = categoryId;
        this.orderAmt = orderAmt;
    }
}
