package com.zksr.product.api.blockScheme;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "blockSchemeApi", value = ApiConstants.NAME)
public interface BlockSchemeApi {
    String PREFIX = ApiConstants.PREFIX + "/blockScheme";

    /**
     * 根据客户编码获取被屏蔽的商品
     * @param branchId 客户编码
     * @return 被屏蔽的skuIds
     */
    @GetMapping(PREFIX + "/getBlockSkusByBranchId")
    CommonResult<List<Long>> getBlockSkusByBranchId(@RequestParam("branchId") Long branchId);
}
