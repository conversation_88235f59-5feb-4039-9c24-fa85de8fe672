package com.zksr.system.api.partnerPolicy.enums;

import lombok.Getter;

/**
* @Description: 平台设置配置枚举 注意不要与软件配置枚举type冲突(com.zksr.system.api.partnerConfig.enums.PartnerConfigEnum)
 *              配置字典(sys_partner_policy_type)
* @Author: liuxingyu
* @Date: 2024/4/20 17:16
*/
public enum PartnerPolicyEnum {
    APPLET_AGREEMENT_POLICY(3, "小程序设置"),
    BASIC_SETTING_POLICY(4, "基础设置"),
    ORDER_SETTING_POLICY(5, "订单参数设置"),
    WITHDRAWAL_SETTING_POLICY(8, "提现设置"),
    XPYUN_SETTING_POLICY(9, "芯烨设置"),
    AFTER_SALE_SETTING(10, "售后设置"),
    FEIEYUN_SETTING_POLICY(11, "飞鹅设置"),
    COLONEL_SETTING_POLICY(12, "业务员设置"),
    OPEN_SETTING_POLICY(13, "入驻商对外系统设置"),
    SUPPLIER_OTHER_SETTING(18, "入驻商其他设置"),
    BRANCH_LIFECYCLE_SETTING(19, "门店生命周期配置"),
    DC_OTHER_SETTING(22, "运营商其他配置"),
    ;

    @Getter
    Integer type;
    @Getter
    String name;

    PartnerPolicyEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
