package com.zksr.system.api.commonMessage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订阅消息模版配置详情
 * @date 2024/6/13 9:22
 */
@Data
@ApiModel(description = "消息模版配置")
public class SubscribeMsgConfigVO {

    @ApiModelProperty("字段列表")
    @NotNull(message = "至少配置一个模版")
    @Size(min = 1, max = 6, message = "至少配置1-6个映射关系")
    List<SubscribeField> fields;

    @Data
    @ApiModel(description = "模版字段配置")
    static public class SubscribeField {

        @ApiModelProperty(value = "消息模版键值", example = "ting2,time9,amount3")
        private String name;

        @ApiModelProperty(value = "系统变量", example = "orderNo,spuName")
        private String variable;

        @ApiModelProperty(value = "自定义值", example = "xxxxx, 当系统变量等于diy{x}时可填写")
        private String custom;

    }
}
