package com.zksr.product.api.spu.dto;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品发布spusku组key
 * @date 2024/5/30 19:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuUnitGroupKeyDTO {

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("城市ID")
    private Long areaId;

    @ApiModelProperty("三级上架分类ID")
    private Long classId;

    @ApiModelProperty("商品类型")
    private ProductType productType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkuUnitGroupKeyDTO that = (SkuUnitGroupKeyDTO) o;
        return Objects.equals(spuId, that.spuId) && Objects.equals(areaId, that.areaId) && Objects.equals(classId, that.classId) && productType == that.productType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(spuId, areaId, classId, productType);
    }
}
