package com.zksr.account.model.pay.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * 微信B2B请求分账, 会自动发起请求分账完成, 微信限制, 每次只能发起一个分账方,
 * 请务必保证, 没有正在退款的, 退款失败的, 订单没有任何异常情况再进行请求分账
 * 否者分账到个人账户的资金, 无法退账
 * @date 2024/9/21 8:41
 */
@Data
@ApiModel(description = "微信B2B请求分账")
@NoArgsConstructor
public class PayWxB2bCreateDivideReqVO extends CreateDivideReqVO{

    public PayWxB2bCreateDivideReqVO(String tradeNo, Long merchantId, String merchantType) {
        super(tradeNo, merchantId, merchantType);
    }
}
