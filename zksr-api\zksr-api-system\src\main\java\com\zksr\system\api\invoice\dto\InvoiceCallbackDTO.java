package com.zksr.system.api.invoice.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 发票回调响应主体
 */
@Data
public class InvoiceCallbackDTO {
    /**
     * 上游传入唯一流水号
     */
    private String bizId;

    /**
     * 交易类型：1=蓝票，2=红票
     */
    private Integer transType;

    /**
     * 发票类型（1-电子普票 2-电子专票 3-纸质普票 4-纸质专票）
     */
    private Integer invoiceType;

    /**
     * 开票渠道
     */
    private String requestChannel;

    /**
     * 开票状态：3=开票成功，5=开票失败，8=已作废 10=二次附件回调
     */
    private Integer status;

    /**
     * 开票失败原因
     */
    private String failReason;

    /**
     * 业务系统在智汇票的标识
     */
    private Integer sysSource;

    /**
     * 主业务单号
     */
    private String businessNo;

    /**
     * 作废发票的发票号码，仅纸票作废有
     */
    private List<String> invoiceNoList;

    /**
     * 作废红票对应的蓝票发票号码，仅红票纸票作废有
     */
    private List<String> blueInvoiceNoList;

    /**
     * 发票列表，只有开票成功才会有此列表
     */
    private List<Invoice> list;

    /**
     * 附件回调附件list，只有status=10才有
     */
    private List<DownloadFile> toDownLoadList;
}







