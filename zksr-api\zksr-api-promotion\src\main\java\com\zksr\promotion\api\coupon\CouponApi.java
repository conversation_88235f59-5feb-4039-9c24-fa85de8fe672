package com.zksr.promotion.api.coupon;

import com.zksr.common.core.enums.CouponStateEnum;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.vo.*;
import com.zksr.promotion.enums.ApiConstants;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券rpc服务
 * @date 2024/3/31 15:21
 */
@FeignClient(
        contextId = "remoteCouponApi",
        value = ApiConstants.NAME
)
public interface CouponApi {

    String PREFIX = ApiConstants.PREFIX + "/coupon-api";

    /**
     * 获取优惠券模版
     */
    @GetMapping(PREFIX + "/getCouponTemplate")
    CommonResult<CouponTemplateDTO> getCouponTemplate(@RequestParam("couponTemplateId") Long couponTemplateId);

    /**
     * 获取统计已领取库存数据
     * @param minId 最小键ID
     * @return  每批次返回50条优惠券数据
     */
    @GetMapping(PREFIX + "/getTotalStockCouponTemplateList")
    CommonResult<List<CouponTemplateDTO>> getTotalStockCouponTemplateList(@RequestParam("minId") Long minId);


    /**
     * 更新优惠券统计数量
     * @param receiveCount          领取数据
     * @param couponTemplateId      优惠券模版ID
     */
    @PutMapping(PREFIX + "/updateCouponTemplateTotal")
    void updateCouponTemplateTotal(@RequestParam("receiveCount") Integer receiveCount, @RequestParam("couponTemplateId") Long couponTemplateId);

    /**
     * 用户主动领取优惠券
     */
    @PostMapping(PREFIX + "/receiveCoupon")
    CommonResult<List<CouponReceiveStatusDTO>> receiveCoupon(@RequestBody CouponReceiveDTO couponReceive);

    /**
     * 用户主动领取优惠券
     */
    @PostMapping(PREFIX + "/receiveCoupon2")
    CommonResult<List<CouponReceiveStatusDTO>> receiveCoupon2(@RequestBody CouponReceiveDTO couponReceive);


    /**
     * 用户主动领取优惠券 ( 无需验证 )
     */
    @PostMapping(PREFIX + "/receiveCouponNotCheck")
    CommonResult<List<CouponReceiveStatusDTO>> receiveCouponNotCheck(@RequestBody CouponReceiveDTO couponReceive);

    /**
     * 获取我的优惠券列表
     */
    @PostMapping(PREFIX + "/getCouponList")
    PageResult<CouponDTO> getCouponList(@RequestBody CouponPageReqVO receiveReq);

    /**
     * 获取优惠券使用范围
     */
    @PostMapping(PREFIX + "/getCouponSpuScopeList")
    CommonResult<List<CouponSpuScopeDTO>> getCouponSpuScopeList(@RequestParam("couponTemplateId") Long couponTemplateId);

    /**
     * 获取优惠券领取范围
     */
    @PostMapping(PREFIX + "/getCouponReceiveScopeList")
    CommonResult<List<CouponReceiveScopeDTO>> getCouponReceiveScopeList(@RequestParam("couponTemplateId") Long couponTemplateId);

    /**
     * 获取门店+状态优惠券状态
     * @param branchId      门店ID
     * @param couponState   优惠券状态   {@link CouponStateEnum}
     * @return
     */
    @GetMapping(PREFIX + "/getCouponListByBranchIdAndState")
    CommonResult<List<CouponDTO>> getCouponListByBranchIdAndState(@RequestParam("branchId") Long branchId, @RequestParam("couponState") Integer couponState);


    /**
     * 获取优惠券集合
     * @param couponBatchListVO    门店和指定优惠券
     * @return
     */
   @PostMapping(PREFIX + "/getCouponListByCouponIds")
    CommonResult<List<CouponDTO>> getCouponListByCouponIds(@RequestBody CouponBatchListVO couponBatchListVO);


    /**
     * 核销优惠券
     * @param couponList    核销数据
     * @return
     */
    @PostMapping(PREFIX + "/applyCoupon")
    CommonResult<Boolean> applyCoupon(@RequestBody List<CouponApplyDTO> couponList);

    /**
     * 返回优惠券 (取消, 售后等) 单号必须正确
     * @param couponReturn    返回数据
     * @return
     */
    @PostMapping(PREFIX + "/returnCoupon")
    CommonResult<Boolean> returnCoupon(@RequestBody CouponReturnDTO couponReturn);

    /**
     * 更新优惠券统计数据( 订单数据 )
     * @param extendTotalVOList
     */
    @PostMapping(PREFIX + "/updateCouponTemplateOrderExtend")
    void updateCouponTemplateOrderExtend(@RequestBody List<CouponTemplateExtendDTO> extendTotalVOList);

    /**
     * 更新优惠券统计数据( 领取数据 )
     * @param couponTemplateIdList
     */
    @PostMapping(PREFIX + "/updateCouponTemplateCouponExtend")
    void updateCouponTemplateCouponExtend(@RequestBody List<Long> couponTemplateIdList);

    /**
     * 获取指定领取类型有效优惠券
     * @param sysCode, couponReceiveType
     */
    @PostMapping(PREFIX + "/selectValidListBySysCodeAndReceiveType")
    List<CouponTemplateDTO> selectValidListBySysCodeAndReceiveType(@RequestParam("sysCode")Long sysCode,@RequestParam("couponReceiveType") Integer couponReceiveType);


    /**
     * 领取单个优惠券
     * @param reqVo
     */
    @PostMapping(PREFIX + "/saveNormalCouponReceiveSingle")
    CouponReceiveResultDTO saveNormalCouponReceiveSingle(@RequestBody NormalCouponReceiveSingleAsyncReqVo reqVo);

        /**
     * 领取单个优惠券 异步调用
     * @param reqVo
     */
    @PostMapping(PREFIX + "/saveNormalCouponReceiveSingleAsync")
    void saveNormalCouponReceiveSingleAsync(@RequestBody NormalCouponReceiveSingleAsyncReqVo reqVo);

     /**
     * 根据门店ID和批量发券ID获得优惠券记录
     *
     * @param branchId 门店ID couponBatchId，批量发券ID
     * @return 优惠券记录
     */
     @GetMapping(PREFIX + "/getCouponListByBranchIdAndCouponBatchId")
     CommonResult<List<CouponDTO>> getCouponListByBranchIdAndCouponBatchId(
             @RequestParam("branchId") Long branchId,
             @RequestParam("couponBatchId") Long couponBatchId);

    /**
     * 查看业务员发券额度
     * @param colonelId
     * @return
     */
    @GetMapping(PREFIX + "/getColonelQuota")
    CommonResult<ColonelQuotaDTO> getColonelQuota(@RequestParam("colonelId") Long colonelId);

    /**
     * 查看业务员发券管理
     * @param pageReqVO
     * @return
     */
    @PostMapping(PREFIX + "/getColonelAppPrmCouponTemplatePage")
    CommonResult<PageResult<ColonelAppPrmCouponTemplateRespVO>> getColonelAppPrmCouponTemplatePage(@RequestBody ColonelAppPrmCouponTemplatePageReqVO pageReqVO);

    /**
     * 查询发券记录
     * @param couponTemplateId
     * @return
     */
    @GetMapping(PREFIX + "/getCouponReceivedBranchs")
    CommonResult<List<Long>> getCouponReceivedBranchs(@RequestParam("couponTemplateId") Long couponTemplateId);

    /**
     * 修改业务员实例使用额度
     * @param colonelId
     * @param discountAmt
     * @return
     */
    @GetMapping(PREFIX + "/updateColonelQuota")
    Long updateColonelQuota(@RequestParam("colonelId")Long colonelId,@RequestParam("discountAmt") BigDecimal discountAmt);

    /**
     * 查询门店发放了多少张优惠券
     * @param couponTemplateId
     * @param branchId
     * @return
     */
    @GetMapping(PREFIX + "/getBranchReceiveQty")
    Integer getBranchReceiveQty(@RequestParam("couponTemplateId") Long couponTemplateId,@RequestParam("branchId") Long branchId);

    @GetMapping(PREFIX + "/getPrmCoupon")
    CommonResult<CouponDTO> getPrmCoupon(@RequestParam("couponId") Long couponId);

    @PostMapping(PREFIX + "/getPrmCouponPage")
    CommonResult<List<CouponExportRespDTO>> getPrmCouponPage(@RequestBody CouponExportVO pageReqVO);

    /**
     * 根据订单号和优惠券模板ID获得优惠券记录
     * @param relateOrderNo
     * @param couponTemplateId
     * @return
     */
    @GetMapping(PREFIX + "/getCouponByOrderNoAndCouponTemplateId")
    CommonResult<CouponDTO> getCouponByOrderNoAndCouponTemplateId(@RequestParam("relateOrderNo") String relateOrderNo,
                                                                  @RequestParam("couponTemplateId") Long couponTemplateId);
}
