package com.zksr.product.api.spu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
*
 * 需要上架未发布的商品信息 VO
* <AUTHOR>
* @date 2024/5/30 15:14
*/
@ApiModel("上架商品导出")
@Data
@Accessors(chain = true)
public class PrdtSpuNotItemPageReqExportVo{

    @ApiModelProperty("上架区域编号")
    @Excel(name = "上架区域编号")
    private Long areaId;

    @ApiModelProperty("上架三级展示分类编号")
    @Excel(name = "上架三级展示分类编号")
    private Long classId;


    @ApiModelProperty(value = "skuId")
    @Excel(name = "skuId")
    private Long skuId;


    /** 小单位-上下架状态 */
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private Integer largeShelfStatus;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位")
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private String minShelfStatusName;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位")
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private String  midShelfStatusName;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位")
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private String largeShelfStatusName;


}
