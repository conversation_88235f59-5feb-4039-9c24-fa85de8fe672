package com.zksr.account.api.platformMerchant.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件注册
 * @date 2024/7/12 11:13
 */
@ApiModel(description = "商户进件注册实体")
@Data
public class WxB2bPlatformMerchantRegisterSaveReqVO {

    @ApiModelProperty("证件类型编号")
    private Integer idDocTypeNum; // 证件类型编号

    @ApiModelProperty("商户名缩写")
    private String merchantShortname; // 商户名缩写

    @ApiModelProperty("组织类型，个人-0，企业-1")
    private Integer organizationType; // 组织类型，个人-0，企业-1

    @ApiModelProperty("身份证信息")
    private IdCardInfo idCardInfo;

    @ApiModelProperty("账户信息")
    private AccountInfo accountInfo;

    @ApiModelProperty("联系人信息")
    private ContactInfo contactInfo;

    @ApiModelProperty("营业执照信息")
    private BusinessLicense businessLicense;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IdCardInfo {
        @ApiModelProperty("身份证人像面照片id")
        private String idCardCopy; // 身份证人像面照片id

        @ApiModelProperty("身份证国徽面照片id")
        private String idCardNational; // 身份证国徽面照片id

        @ApiModelProperty("身份证姓名")
        private String idCardName; // 身份证姓名

        @ApiModelProperty("身份证号码")
        private String idCardNumber; // 身份证号码

        @ApiModelProperty("身份证有效期开始日期")
        private String idCardValidTimeBegin; // 身份证有效期开始日期

        @ApiModelProperty("身份证有效期结束日期")
        private String idCardValidTime; // 身份证有效期结束日期

        @ApiModelProperty("身份证地址")
        private String idCardAddress; // 身份证地址
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AccountInfo {
        @ApiModelProperty("账户类型")
        private String bankAccountType; // 账户类型

        @ApiModelProperty("开户银行")
        private String accountBank; // 开户银行

        @ApiModelProperty("开户名称")
        private String accountName; // 开户名称

        @ApiModelProperty("开户银行省市编码")
        private String bankAddressCode; // 开户银行省市编码

        @ApiModelProperty("开户银行联行号")
        private String bankBranchId; // 开户银行联行号

        @ApiModelProperty("开户银行全称")
        private String bankName; // 开户银行全称

        @ApiModelProperty("银行账号")
        private String accountNumber; // 银行账号
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContactInfo {
        @ApiModelProperty("联系人类型")
        private String contactType; // 联系人类型

        @ApiModelProperty("超级管理员姓名")
        private String contactName; // 超级管理员姓名

        @ApiModelProperty("超级管理员证件类型")
        private String contactIdDocType; // 超级管理员证件类型

        @ApiModelProperty("超级管理员身份证件号码")
        private String contactIdCardNumber; // 超级管理员身份证件号码

        @ApiModelProperty("超级管理员证件正面照片id")
        private String contactIdDocCopy; // 超级管理员证件正面照片id

        @ApiModelProperty("超级管理员证件反面照片id")
        private String contactIdDocCopyBack; // 超级管理员证件反面照片id

        @ApiModelProperty("超级管理员证件有效期开始时间")
        private String contactIdDocPeriodBegin; // 超级管理员证件有效期开始时间

        @ApiModelProperty("超级管理员证件有效期结束时间")
        private String contactIdDocPeriodEnd; // 超级管理员证件有效期结束时间

        @ApiModelProperty("业务办理授权函")
        private String businessAuthorizationLetter; // 业务办理授权函

        @ApiModelProperty("超级管理员手机")
        private String mobilePhone; // 超级管理员手机

        @ApiModelProperty("超级管理员邮箱")
        private String contactEmail; // 超级管理员邮箱
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessLicense {
        @ApiModelProperty("营业执照扫描件图片id")
        private String businessLicenseCopy; // 营业执照扫描件图片id

        @ApiModelProperty("营业执照注册号")
        private String businessLicenseNumber; // 营业执照注册号

        @ApiModelProperty("商户名称")
        private String merchantName; // 商户名称

        @ApiModelProperty("经营者/法定代表人姓名")
        private String legalPerson; // 经营者/法定代表人姓名

        @ApiModelProperty("注册地址")
        private String companyAddress; // 注册地址

        @ApiModelProperty("营业期限")
        private String businessTime; // 营业期限

        @ApiModelProperty("登记证书类型")
        private String certType; // 登记证书类型
    }
}
