package com.zksr.product.api.sku.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("商品SKU商品价格 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SkuPricesPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 平台商Id */
    @ApiModelProperty(value = "平台商Id")
    private Long sysCode;

    /** 入驻商Id */
    @ApiModelProperty(value = "入驻商Id")
    private Long supplierId;

    /** 管理分类Id */
    @ApiModelProperty(value = "管理分类Id")
    private Long catgoryId;

    /** 产品编号 */
    @ApiModelProperty(value = "产品编号")
    private String spuNo;

    /** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    private String spuName;

    /** 品牌Id */
    @ApiModelProperty(value = "品牌Id")
    private Long brandId;

    /** 状态(数据字典 sys_common_status) */
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    private Long status;

    @ApiModelProperty("小于有效库存数的")
    private Integer validStockQty;
}
