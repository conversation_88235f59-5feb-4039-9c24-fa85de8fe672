package com.zksr.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 *  订单下单方式枚举
 */
@Getter
@AllArgsConstructor
public enum OrderPayWayEnum {
//    ONLINE("0", "在线支付","ZXZF"),
    ONLINE("0", "微信支付","ZXZF"),
    WALLET("1", "储值支付","CZZF"),
    HDFK("2", "货到付款","HDSK"),
    ;
    private String payWay;
    private String name;
    private String erpPayWay;



    public static boolean isWallet(String payWay) {
        return Objects.equals(WALLET.getPayWay(), payWay);
    }
}
