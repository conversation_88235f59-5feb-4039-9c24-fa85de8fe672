package com.zksr.member.api.complain.excel;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class MemComplainExcel extends BaseEntity {

    @Excel(name = "投诉单号")
    private Long complainId;


    @Excel(name = "用户名称")
    private String username;

    @Excel(name = "用户账号")
    private String phone;


    @Excel(name = "投诉对象")
    private String complainType;

    @Excel(name = "关联信息")
    private String memo;

    @Excel(name = "详细说明")
    private String complainContent;

    @Excel(name = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "投诉处理回复")
    private String complainReply;

    @Excel(name = "处理人")
    private String processBy;

    @Excel(name = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTime;


    @Excel(name = "状态")
    private String status;


    @Excel(name = "投诉对象名称")
    private String targetUsername;

    @Excel(name = "投诉对象手机号(非必填)")
    private String targetPhone;



}
