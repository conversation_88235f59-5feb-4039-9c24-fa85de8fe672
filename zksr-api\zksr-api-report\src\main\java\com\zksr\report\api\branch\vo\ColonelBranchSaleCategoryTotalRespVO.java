package com.zksr.report.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月订货类别占比,
 * @date 2024/11/25 9:59
 */
@Data
@ApiModel(description = "月订货类别占比 - response")
public class ColonelBranchSaleCategoryTotalRespVO {

    @ApiModelProperty("月订货类别占比")
    private List<CategorySaleTotalVO> categoryList = new ArrayList<>();
}
