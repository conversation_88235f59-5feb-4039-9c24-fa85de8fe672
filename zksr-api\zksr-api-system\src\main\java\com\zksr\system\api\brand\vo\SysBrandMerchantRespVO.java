package com.zksr.system.api.brand.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 品牌商资料对象 sys_brand_merchant
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Data
@ApiModel("品牌商资料 - sys_brand_merchant Response VO")
public class SysBrandMerchantRespVO {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "关联品牌集合")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandMerchantId;

    /** 运营商编号 */
    @Excel(name = "运营商编号")
    @ApiModelProperty(value = "运营商编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系人手机号 */
    @Excel(name = "联系人手机号")
    @ApiModelProperty(value = "联系人手机号")
    private String contactPhone;

    /** 品牌商全称 */
    @Excel(name = "品牌商全称")
    @ApiModelProperty(value = "品牌商全称")
    private String name;

    /** 品牌商简称 */
    @Excel(name = "品牌商简称")
    @ApiModelProperty(value = "品牌商简称")
    private String simpleName;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "帐号状态")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 关联品牌集合 */
    @Excel(name = "关联品牌集合")
    @ApiModelProperty(value = "关联品牌集合")
    private String brandIds;

    @ApiModelProperty(value = "登陆账户")
    private String username;

    @ApiModelProperty(value = "头像")
    private String avatar;
}
