package com.zksr.member.constant;

/**
*
 * 业务员模块相关常量
* <AUTHOR>
* @date 2024/3/1 15:30
*/
public class MemberConstant {

    /** 业务员APP客户信息统计 刷新ES类型 1、客户登陆，更新登陆时间*/
    public static final int ES_COLONEL_APP_BRANCH_TYPE_1 = 1;
    /** 业务员APP客户信息统计 2、下单完成 更新订单信息*/
    public static final int ES_COLONEL_APP_BRANCH_TYPE_2 = 2;
    /** 业务员APP客户信息统计 3、业务员签到 更新拜访时间*/
    public static final int ES_COLONEL_APP_BRANCH_TYPE_3 = 3;
    /** 业务员APP客户信息统计 4、基础信息, GEO信息*/
    public static final int ES_COLONEL_APP_BRANCH_TYPE_4 = 4;

    /** 业务员APP客户信息统计 订单状态 0 取消*/
    public static final int ES_COLONEL_APP_ORDER_STATUS_0 = 0;
    /** 业务员APP客户信息统计 订单状态 1 支付*/
    public static final int ES_COLONEL_APP_ORDER_STATUS_1 = 1;

    /** 业务员APP首页信息 消息发送类型 1、业务员签到*/
    public static final int COLONEL_APP_Page_DATA_TYPE_1 = 1;
    /** 业务员APP客户信息统计 消息发送类型 2、业务员拓店*/
    public static final int COLONEL_APP_Page_DATA_TYPE_2= 2;
    /** 业务员APP客户信息统计  消息发送类型 3、客户下单*/
    public static final int COLONEL_APP_Page_DATA_TYPE_3 = 3;
    /** 业务员APP客户信息统计  消息发送类型 4、客户售后退货*/
    public static final int COLONEL_APP_Page_DATA_TYPE_4 = 4;

    /** 业务员APP客户信息统计  消息发送类型 5、客户下单、客户售后退货（上级业务员提成金额）*/
    public static final int COLONEL_APP_PAGE_DATA_TYPE_5 = 5;

    /** 门店生命周期操作类型 1、门店注册*/
    public static final int BRANCH_LIFECYCLE_OPERATION_TYPE_1 = 1;

    /** 门店生命周期操作类型 2、订单下单*/
    public static final int BRANCH_LIFECYCLE_OPERATION_TYPE_2 = 2;

    /** 门店生命周期操作类型 3、订单取消*/
    public static final int BRANCH_LIFECYCLE_OPERATION_TYPE_3 = 3;


}
