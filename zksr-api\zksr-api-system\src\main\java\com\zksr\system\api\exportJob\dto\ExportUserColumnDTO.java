package com.zksr.system.api.exportJob.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/1/6 10:45
 */
@Data
public class ExportUserColumnDTO {

    /** 列key */
    @ApiModelProperty("列key,字段名称")
    private String filedKey;

    /** 名称 */
    @ApiModelProperty("列key,显示名称")
    private String filedName;

    /** 是否导出显示列 */
    @ApiModelProperty("是不是导出列表")
    private Integer exportVisibelFlag;

    /** 固定 */
    private Integer fixedFlag;

    /** 排序 */
    private Integer sort;

    /**
     * 对齐方式 （left，center，right）
     */
    private String align;

    /**
     * 列宽度
     */
    private Integer width;

    /**
     * 列最小宽度
     */
    private Integer minWidth;
}
