package com.zksr.member.api.b2bAuth.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: openid 与 appid 与 门店 b2b门店助手认证授权状态
 * @date 2024/8/15 18:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemMemberOpenAuthDTO {

    /** $column.columnComment */
    @ApiModelProperty(value = "小程序appid")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long b2bAuthOpenId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 微信小程序openid */
    @Excel(name = "微信小程序openid")
    @ApiModelProperty(value = "微信小程序openid")
    private String openid;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 0-未授权, 1-已认证授权 */
    @Excel(name = "0-未授权, 1-已认证授权")
    @ApiModelProperty(value = "0-未授权, 1-已认证授权")
    private Integer authState;

    /** 小程序appid */
    @Excel(name = "小程序appid")
    @ApiModelProperty(value = "小程序appid")
    private String appid;

}
