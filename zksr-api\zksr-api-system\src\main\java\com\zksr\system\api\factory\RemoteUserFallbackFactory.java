package com.zksr.system.api.factory;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.domain.SysUserUpdatePwdVO;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.zksr.common.core.domain.R;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;

import java.util.ArrayList;
import java.util.List;

import static com.zksr.system.enums.ErrorCodeConstants.USERNAME_FIAL;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> exportTest() {
                return R.fail("用户导出测试失败:" + throwable.getMessage());
            }

            @Override
            public CommonResult<DcAreaGroupDTO> getDcAreaGroup(Long dcId) {
                return CommonResult.success(new DcAreaGroupDTO(-1L, new ArrayList<>()));
            }

            @Override
            public CommonResult<String> getBySysUserId(Long userId) {
                return CommonResult.error(USERNAME_FIAL);
            }

            @Override
            public CommonResult<SysUser> getSysUser(Long userId) {
                return CommonResult.error(USERNAME_FIAL);
            }

            @Override
            public CommonResult<SysUser> getSysUserByPhone(String phone) {
                return CommonResult.error(USERNAME_FIAL);
            }

            @Override
            public CommonResult<Integer> updateUserPwd(SysUserUpdatePwdVO updatePwdVO) {
                return CommonResult.error(500, "更改用户密码失败:" + throwable.getMessage());
            }

            @Override
            public CommonResult<Long> deleteByUserId(Long userId) {
                return CommonResult.error(500, "删除用户失败:" + throwable.getMessage());
            }
        };
    }
}
