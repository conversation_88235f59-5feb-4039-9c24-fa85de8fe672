package com.zksr.system.api.partner;

import com.zksr.common.core.web.domain.AjaxResultBase;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;
import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(
        contextId = "remotePartnerApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface PartnerApi {

    String PREFIX = ApiConstants.PREFIX + "/partner";

    @GetMapping(PREFIX + "/getBySysCode")
    public CommonResult<PartnerDto> getBySysCode(@RequestParam("sysCode") Long sysCode);

    @GetMapping(PREFIX + "/getBySource")
    public CommonResult<PartnerDto> getPartnerBySource(@RequestParam("source") String source);

    /**
     * 获取所有平台商信息
     * @return
     */
    @GetMapping(PREFIX + "/getPartnerInfo")
    public CommonResult<List<PartnerDto>> getPartnerInfo();

    /**
     * 获取平台商分页对象
     * @param pageReqVO
     * @return
     */
    @PostMapping(PREFIX + "/getPage")
    CommonResult<PageResult<SysPartnerRespVO>> getPage(@RequestBody SysPartnerPageReqVO pageReqVO);

    /**
     * 根据saas租户查询平台商
     * @param saasTenantCode
     * @return
     */
    @GetMapping(PREFIX + "/getBySaasTenantCode")
    CommonResult<PartnerDto> getBySaasTenantCode(@RequestParam("saasTenantCode") String saasTenantCode);
}
