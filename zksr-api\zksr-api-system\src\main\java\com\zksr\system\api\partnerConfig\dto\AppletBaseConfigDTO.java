package com.zksr.system.api.partnerConfig.dto;

import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 小程序配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppletBaseConfigDTO {
    /**
     * 小程序appId
     */
    @ApiModelProperty("小程序appId")
    private String appId;

    /**
     * 小程序appSecret
     */
    @ApiModelProperty("小程序appSecret")
    private String appSecret;

    @ApiModelProperty("小程序公众号appId")
    private String publishAppId;

    @ApiModelProperty("小程序公众号appSecret")
    private String publishSecret;

    @ApiModelProperty("公众号名称")
    private String publishAccountName;

    /**
     * 小程序appId
     */
    @ApiModelProperty("平台商小程序接口地址")
    private String miniJumpAddress;

    @ApiModelProperty("appVersion使用这个来判断版本是否需要更新, 使用时间戳填充(Android)")
    private String appVersion;

    @ApiModelProperty("这个只是app显示更新版本(Android)")
    private String appVersionName;

    @ApiModelProperty("appVersion使用这个来判断版本是否需要更新, 使用时间戳填充(IOS)")
    private String iosVersion;

    @ApiModelProperty("这个只是app显示更新版本(IOS)")
    private String iosVersionName;

    @ApiModelProperty("app版本更新内容 (安卓)")
    private String appVersionContent;

    @ApiModelProperty("app版本更新内容 (IOS)")
    private String iosVersionContent;

    @ApiModelProperty("app更新包位置, 仅支持安卓")
    private String appUpdateUrl;

    @ApiModelProperty("是否强制更新,0-不强制, 1-强制更新 (安卓)")
    private String forceUpdate;

    @ApiModelProperty("是否强制更新,0-不强制, 1-强制更新 (IOS)")
    private String iosForceUpdate;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    /**
     * 当前平台是否允许销售全国本地,0-默认全部允许, 1-只允许本地, 2-只允许全国
     */
    @ApiModelProperty("当前平台是否允许销售全国本地, 0-默认全部允许, 1-只允许本地, 2-只允许全国")
    private String saleClassSwitch = "0";

    @ApiModelProperty("是否展示中科标签 0 展示 1 不展示")
    private String zksrShowLabel;

    public String getZksrShowLabel(){
        if (StringUtils.isEmpty(zksrShowLabel)){
            return "0";
        }
        return zksrShowLabel;
    }
}
