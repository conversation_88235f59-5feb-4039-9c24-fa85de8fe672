package com.zksr.product.api.spu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
* @Description: 商品信息对象
* @Author: liu<PERSON>yu
* @Date: 2024/4/11 16:46
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductDTO implements Serializable {
    /**
     * spuId集合
     */
    private List<Long> spuIdList;

    /**
     * skuId集合
     */
    private List<Long> skuIdList;
}
