package com.zksr.promotion.api.coupon.handler;

import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券使用范围策略
 * @date 2024/4/3 15:54
 */
public interface CouponSpuScopeStrategy {

    /**
     * 处理优惠券具体使用范围
     * @param items     下单商品
     * @param coupons   优惠券列表
     * @param couponTemplateMap 优惠券模版
     * @param availableSet  有效列表
     */
    void productFilter(List<OrderValidItemDTO> items, Set<CouponDTO> coupons, Map<Long, CouponTemplateDTO> couponTemplateMap, Set<CouponDTO> availableSet);

}
