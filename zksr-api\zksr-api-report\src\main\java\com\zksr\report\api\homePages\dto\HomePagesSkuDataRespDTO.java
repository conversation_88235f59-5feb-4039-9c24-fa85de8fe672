package com.zksr.report.api.homePages.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@ApiModel("PC首页获取Sku上架数据返回 - HomePagesSkuShelfDataRespDTO Response VO")
@Accessors(chain = true)
public class HomePagesSkuDataRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID */
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 入驻商ID */
    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;


    /** 上架SKU数量 */
    @ApiModelProperty(value = "上架SKU数量")
    private Long skuShelfQty = NumberPool.LONG_ZERO;

    /** 上次上架SKU数量 */
    @ApiModelProperty(value = "上次上架SKU数量")
    private Long beforeSkuShelfQty = NumberPool.LONG_ZERO;

    /** 上架SKU同比上升/下降率  */
    @ApiModelProperty(value = "上架SKU同比上升/下降率")
    private BigDecimal skuShelfRate = BigDecimal.ZERO;

    /** 动销SKU数量 */
    @ApiModelProperty(value = "动销SKU数量")
    private Long skuSalesQty = NumberPool.LONG_ZERO;

    /** 上次动销SKU数量 */
    @ApiModelProperty(value = "上次动销SKU数量")
    private Long beforeSkuSalesQty = NumberPool.LONG_ZERO;

    /** 动销SKU数量同比上升/下降率  */
    @ApiModelProperty(value = "动销SKU数量同比上升/下降率")
    private BigDecimal skuSalesRate = BigDecimal.ZERO;

    /** SKU动销率  */
    @ApiModelProperty(value = "SKU动销率")
    private BigDecimal skuRate = BigDecimal.ZERO;

    /** 一级品类个数 */
    @ApiModelProperty(value = "一级品类个数")
    private Long category1Qty = NumberPool.LONG_ZERO;

    /** 上次一级品类个数 */
    @ApiModelProperty(value = "上次一级品类个数")
    private Long beforeCategory1Qty = NumberPool.LONG_ZERO;

    /** 一级品类个数同比上升/下降率  */
    @ApiModelProperty(value = "一级品类个数同比上升/下降率")
    private BigDecimal category1Rate = BigDecimal.ZERO;

    /** 一级品类动销率  */
    @ApiModelProperty(value = "一级品类动销率")
    private BigDecimal category1SalesRate = BigDecimal.ZERO;

    /** 新上架SKU数量  */
    @ApiModelProperty(value = "新上架SKU数量")
    private Long skuNewShelfQty = NumberPool.LONG_ZERO;

    /** 上次新上架SKU数量 */
    @ApiModelProperty(value = "上次新上架SKU数量")
    private Long beforeSkuNewShelfQty = NumberPool.LONG_ZERO;

    /** 新上架SKU数量同比上升/下降率 */
    @ApiModelProperty(value = "新上架SKU数量同比上升/下降率")
    private BigDecimal skuNewShelfRate = BigDecimal.ZERO;

    /** sku总数 */
    @ApiModelProperty(value = "sku总数")
    private Long skuTotalQty = NumberPool.LONG_ZERO;

    /** 一级品类动销数量 */
    @ApiModelProperty(value = "一级品类动销数量")
    private Long category1SalesQty = NumberPool.LONG_ZERO;


}
