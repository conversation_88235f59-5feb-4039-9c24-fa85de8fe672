package com.zksr.account.model.pay.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信订单发货管理请求
 * @date 2024/10/9 14:18
 */
@Data
@ApiModel(description = "微信订单发货管理请求")
public class WxPayDeliveryReqVO {

    @JSONField(serialize = false)
    @ApiModelProperty("内部交易订单号")
    private String tradeNo;

    @JSONField(name = "order_key")
    @ApiModelProperty("订单，需要上传物流信息的订单")
    private OrderKey orderKey;

    @JSONField(name = "logistics_type")
    @ApiModelProperty("物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提")
    private Integer logisticsType;

    @JSONField(name = "delivery_mode")
    @ApiModelProperty("发货模式，发货模式枚举值：1、UNIFIED_DELIVERY（统一发货）2、SPLIT_DELIVERY（分拆发货） 示例值: UNIFIED_DELIVERY")
    private String deliveryMode;

    @JSONField(name = "is_all_delivered")
    @ApiModelProperty("分拆发货模式时必填，用于标识分拆发货模式下是否已全部发货完成，只有全部发货完成的情况下才会向用户推送发货完成通知。示例值: true/false")
    private boolean isAllDelivered;

    @Data
    @ApiModel(description = "订单信息")
    public static class OrderKey {

        @JSONField(name = "order_number_type")
        @ApiModelProperty(value = "订单单号类型，用于确认需要上传详情的订单。枚举值1，使用下单商户号和商户侧单号；枚举值2，使用微信支付单号", notes = "我们默认需要使用微信订单号")
        private Integer orderNumberType = 2;

        @JSONField(name = "transaction_id")
        @ApiModelProperty(value = "微信订单号", example = "4200002352202410095881990448")
        private String transactionId;
    }

    @Data
    @ApiModel(description = "物流信息")
    public static class Shipping {

        @JSONField(name = "tracking_no")
        @ApiModelProperty(value = "物流单号，物流快递发货时必填，示例值: 323244567777 字符字节限制: [1, 128]")
        private String tracking_no;

        @JSONField(name = "express_company")
        @ApiModelProperty(value = "物流公司编码，快递公司ID，参见「查询物流公司编码列表」，物流快递发货时必填， 示例值: DHL 字符字节限制: [1, 128]")
        private String express_company;

        @JSONField(name = "item_desc")
        @ApiModelProperty(value = "商品信息，例如：微信红包抱枕*1个，限120个字以内")
        private String item_desc;

        @JSONField(name = "contact")
        @ApiModelProperty(value = "联系方式，当发货的物流公司为顺丰时，联系方式为必填，收件人或寄件人联系方式二选一")
        private Contact contact;
    }

    @Data
    @ApiModel(description = "物流信息")
    public static class Contact {

        @JSONField(name = "consignor_contact")
        @ApiModelProperty(value = "寄件人联系方式，寄件人联系方式，采用掩码传输，最后4位数字不能打掩码 示例值: `189****1234, 021-****1234, ****1234, 0**2-***1234, 0**2-******23-10, ****123-8008` 值限制: 0 ≤ value ≤ 1024")
        private String consignorContact;

        @JSONField(name = "receiver_contact")
        @ApiModelProperty(value = "收件人联系方式，收件人联系方式为，采用掩码传输，最后4位数字不能打掩码 示例值: `189****1234, 021-****1234, ****1234, 0**2-***1234, 0**2-******23-10, ****123-8008` 值限制: 0 ≤ value ≤ 1024")
        private String expressCompany;
    }

    // 设置统一发货
    public void setDeliveryModelUnified() {
        this.deliveryMode = "UNIFIED_DELIVERY";
    }

    // 设置拆分发货
    public void setDeliveryModelSplit() {
        this.deliveryMode = "SPLIT_DELIVERY";
    }

    // 设置为快递物流发货
    public void setLogisticsTypeByExpress() {
        this.logisticsType = 1;
    }

    // 设置为同城配送
    public void setLogisticsTypeByLocal() {
        this.logisticsType = 2;
    }

    // 虚拟发货
    public void setLogisticsTypeByVirtual() {
        this.logisticsType = 3;
    }

    // 自提
    public void setLogisticsTypeByOffLine() {
        this.logisticsType = 4;
    }
}
