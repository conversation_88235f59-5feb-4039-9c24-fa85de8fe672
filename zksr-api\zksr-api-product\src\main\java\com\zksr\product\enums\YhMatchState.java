package com.zksr.product.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货匹配结果
 * @date 2024/12/10 10:10
 */
@Getter
public enum YhMatchState {
    UNDEFINED(0, "未匹配"),
    SUCCESS(1, "匹配成功"),
    FAIL(2, "匹配失败"),
    ;
    private Integer state;
    private String name;

    YhMatchState(Integer state, String name) {
        this.state = state;
        this.name = name;
    }
}
