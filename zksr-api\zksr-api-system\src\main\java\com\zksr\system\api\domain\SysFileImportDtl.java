package com.zksr.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysFileImportDtl extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 导入记录id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long fileImportDtlId;

    /**
     * 平台编号
     */
    private Long sysCode;

    /**
     * 状态0成功 1失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 快递导入记录id
     */
    private Long fileImportId;

    /**
     * 详情json
     */
    private String dtlJson;

    /**
     * 批次
     */
    private Integer batchNum;

}
