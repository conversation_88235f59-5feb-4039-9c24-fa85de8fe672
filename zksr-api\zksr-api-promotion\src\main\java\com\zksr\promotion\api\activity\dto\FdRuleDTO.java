package com.zksr.promotion.api.activity.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 满减活动数据
 * @date 2024/5/17 16:08
 */
@Data
@ToString
@ApiModel(description = "满减规则")
public class FdRuleDTO {

    /** 满减活动规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fdRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 满减触发金额 */
    @Excel(name = "满减触发金额")
    private BigDecimal fullAmt;

    /** 满减触发数量 */
    @Excel(name = "满减触发数量")
    private Integer fullQty;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discountAmt;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;
}
