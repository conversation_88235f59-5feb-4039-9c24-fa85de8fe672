Read file: zksr-portal/src/main/java/com/zksr/portal/service/impl/mall/TradePriceServiceImpl.java
商品利润的计算方式如下：

---

 /**
    *   分润计算说明
    *平台商设置的一级分润比例是30%；
    * 运营商商设置的二级分润比例分别是：
    * 运营商20%，业务员管理员30%，业务员50%。
    *
    * 假设商品利润为100元，则：
    * 平台商分润为：100 * 30% = 30元；
    * 运营商分润为：100 * （100%-30%）* 20% = 14元；
    * 业务员管理员分润为： 100 * （100%-30%）* 30% = 21元；
    * 业务员管分润为： 100 * （100%-30%）* 50% = 35元；
    *
    * 改：
    软件商分润规则说明：
    利润是100元，
    软件商10%，100*10%=10元；
    平台商30%，100*30%=30元
    运营商60%,100*（100%-40%）*60%=36元
    业务经理30%,100*（100%-40%）*30%=18元
    业务员10%，100*（100%-40%）*10%=6元
    *
    *
    * 特别说明：
    * 1.如果运营商没有设置二级分润比例，则剩余分润金额100 * （100%-30%）= 70元  全部归到运营商；
    * 2.如果订单没有业务员，则本该分给业务员的分润全部归到运营商下面；
    * 3.如果业务员有设置提成系数，假如是 提成系数为80%，按照上面的例子，实际业务员管理员分润为：100 * （100%-30%）* 30%  * 80% = 16.8元，剩余20%（21 * 20%=4.2元）业务员分润金额归到运营商下面， 如果业务员没有设置提成系数，按100% 处理；
    * 4.如果是业务员管理员产生的订单，按照上面的例子，该业务员管理员所得的分润金额为 21 + 35 = 56元
*/

### 1. 普通利润模式下

在 `TradePriceServiceImpl#calculateRateAmt` 方法中，商品利润的计算公式为：

```java
// 利润 = 商品金额 - 入驻商金额 - 运费
.setProfit(sOrderSettleVo.getItemAmt().subtract(sOrderSettleVo.getSupplierAmt()).subtract(sOrderSettleVo.getTransAmt()))
```

- **商品金额（itemAmt）**：通常是商品的销售总价（单价 × 数量）。
- **入驻商金额（supplierAmt）**：入驻商的成本价 × 数量。
- **运费（transAmt）**：目前代码中默认是0。

**即：**
> 商品利润 = 商品销售总价 - 入驻商成本总价 - 运费

---

### 2. 利润模式为0（特殊利润模式）

如果 `profitModel.equals("0")`，则利润会按配置的分润比例（relate）重新计算：

```java
sOrderSettleVo.setProfit(
    (sOrderSettleVo.getItemAmt().subtract(sOrderSettleVo.getTransAmt())).multiply(relate)
)
```
此时利润为：
> 商品利润 = (商品销售总价 - 运费) × 分润比例

---

### 3. 负毛利保护

如果利润为负，则利润直接置为0，不再分润。

---

### 总结

**商品利润的核心计算公式：**
- 默认：**商品利润 = 商品销售总价 - 入驻商成本总价 - 运费**
- 特殊模式：**商品利润 = (商品销售总价 - 运费) × 分润比例**

这些利润金额会作为后续各类分润（平台、运营商、业务员、软件商等）的分配基础。



       

如需进一步追踪销售总价、成本价等字段的来源，也可以继续深入！