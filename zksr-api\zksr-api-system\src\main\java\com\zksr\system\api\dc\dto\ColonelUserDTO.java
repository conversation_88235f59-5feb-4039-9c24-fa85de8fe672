package com.zksr.system.api.dc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年03月28日 09:31
 * @description: ColonelDTO
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ColonelUserDTO {
    /** 业务员id */
    private Long colonelId;

    /** 平台商id */
    private Long sysCode;

    /** 城市id */
    private Long areaId;

    /** 业务员手机号 */
    private String colonelPhone;

    /** 业务员名 */
    private String colonelName;

    /** 业务员级别（职务） */
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    private Long pcolonelId;

    /** 性别（数据字典） */
    private Integer sex;

    /** 状态 1正常 0停用 */
    private Integer status;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date birthday;

    /** 籍贯 */
    @Excel(name = "籍贯")
    private String birthplace;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryDate;

    /** 学历(数据字典) */
    private String edu;

    /** 身份证号 */
    private String idcard;

    /** 提成系数 */
    private BigDecimal percentageRate;

    /** 联系地址 */
    private String contactAddr;

    /** 备注 */
    private String memo;

    /** 是否是业务管理员（Y:是，N:否） */
    private String isColonelAdmin;

    /** 部门 */
    private Long deptId;

    /** APP下单改价（Y:是，N:否） */
    private String appOrderPriceAdjust;

    /** APP退货改价（Y:是，N:否） */
    private String appAfterPriceAdjust;

    /** 下单自动审核（Y:是，N:否） */
    private String orderAutoApprove;

    /** 用户ID */
    private Long userId;

    /** 用户账号 */
    private String userName;

    /** 密码 */
    private String password;
}
