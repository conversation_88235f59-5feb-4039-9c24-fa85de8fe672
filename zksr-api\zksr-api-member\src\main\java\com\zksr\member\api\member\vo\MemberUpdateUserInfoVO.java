package com.zksr.member.api.member.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户信息更新 接口请求")
public class MemberUpdateUserInfoVO {
    @ApiModelProperty(value = "用户Id 必填", hidden = true)
    private Long memberId;

    @ApiModelProperty(value = "用户名称")
    private String memberName;

    @ApiModelProperty(value = "用户头像地址")
    private String avatar;

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

    @ApiModelProperty(value = "用户小程序openid")
    private String xcxOpenid;
}
