package com.zksr.product.api.materialApply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 素材应用对象 prdt_material_apply
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialApplyDTO {
    private static final long serialVersionUID=1L;

    /** 素材应用id */
    private Long materialApplyId;

    /** 平台商id */
    private Long sysCode;

    /** 素材id */
    private Long materialId;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    private Integer applyType;

    /** 素材应用类型id */
    private Long applyId;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 操作人 */
    private Long applyUserId;

    /** 素材图片地址 */
    private String img;

    /** 原生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date newStartTime;

    /** 原失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date newEndTime;

}
