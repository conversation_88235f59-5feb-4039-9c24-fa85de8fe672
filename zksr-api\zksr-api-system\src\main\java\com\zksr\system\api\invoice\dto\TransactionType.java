package com.zksr.system.api.invoice.dto;


/**
 * 交易类型枚举
 */
public enum TransactionType {
    BLUE_INVOICE(1, "蓝票"),
    RED_INVOICE(2, "红票");

    private final Integer code;
    private final String desc;

    TransactionType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
