package com.zksr.promotion.api.coupon.vo;

import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请求有效优惠券
 * @date 2024/4/1 15:25
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
@Builder
@ApiModel(description = "请求参数")
public class CouponSpuScopeValidReqVO {

    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    @ApiModelProperty("管理分类ID")
    private Long categoryId;

    @ApiModelProperty("管理分类ID")
    private Long brandId;

    @ApiModelProperty("SKUId")
    private Long skuId;

    public CouponSpuScopeValidReqVO(SpuDTO spuDTO, Long skuId, Long supplierId) {
        if (Objects.nonNull(spuDTO)) {
            this.categoryId = spuDTO.getCatgoryId();
            this.brandId = spuDTO.getBrandId();
        }
        this.skuId = skuId;
        this.supplierId = supplierId;
    }

    public CouponSpuScopeValidReqVO(OrderValidItemDTO itemDTO) {
        if (Objects.isNull(itemDTO)) {
            return;
        }
        this.supplierId = itemDTO.getSupplierId();
        this.categoryId = itemDTO.getCategoryId();
        this.brandId = itemDTO.getBrandId();
        this.skuId = itemDTO.getSkuId();
    }
}
