package com.zksr.report.api.branch.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户类占比定义
 * @date 2024/11/12 8:43
 */
@Data
@ApiModel(description = "客户类占比定义")
public class BranchSaleClassConfigDTO {

    @ApiModelProperty(value = "是否有指定管理分类, (传三级分类ID, OK???)")
    private List<String> catgoryList;

    @ApiModelProperty("类占比规则")
    private List<BranchLevelConfigDTO> ruleList;
}
