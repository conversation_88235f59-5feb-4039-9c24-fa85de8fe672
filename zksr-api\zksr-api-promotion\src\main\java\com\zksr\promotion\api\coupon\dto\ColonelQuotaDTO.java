package com.zksr.promotion.api.coupon.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("业务员的额度信息")
public class ColonelQuotaDTO {
    /** 业务员发券额度ID */
    @TableId
    private Integer couponColonelQuotaId;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 本月发券额度 */
    @Excel(name = "本月发券额度")
    @ApiModelProperty(value = "本月发券额度")
    private BigDecimal quota;

    /** 已发额度 */
    @Excel(name = "已发额度")
    @ApiModelProperty(value = "已发额度")
    private BigDecimal finishQuota;

    /** 剩余额度 */
    @Excel(name = "剩余额度")
    @ApiModelProperty(value = "剩余额度")
    private BigDecimal remainingQuota;
}
