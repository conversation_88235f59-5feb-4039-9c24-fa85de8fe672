### =======================1、acc_transfer（AccTransfer）：转账表（账户间转账的主表）==============================

# `acc_transfer` 表结构说明

## 业务关联字段
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `settle_id` | bigint | YES | NULL | 订单结算id |
| `transfer_no` | varchar(32) | YES | NULL | 转账单号 |
| `platform` | varchar(16) | YES | NULL | 支付平台(数据字典);从订单 |

## 账户信息字段
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `source_account_id` | bigint | YES | NULL | 转出方账户id |
| `target_account_id` | bigint | YES | NULL | 转入方账户id |
| `source_merchant_id` | bigint | YES | NULL | 转出方商户ID |
| `source_merchant_type` | varchar(12) | YES | NULL | 转出方商户类型 |
| `target_merchant_id` | bigint | YES | NULL | 转入方商户ID |
| `target_merchant_type` | varchar(12) | YES | NULL | 转入方商户类型 |

## 金额相关字段
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `transfer_amt` | decimal(12,2) | YES | NULL | 转账金额 |
| `settle_amt` | decimal(12,2) | YES | 0.00 | 转账发起方解除金额 |

## 状态与时间字段
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `state` | int | YES | 0 | 转账状态;0-已提交 1-处理中 2-已完成 3-转账失败 |
| `processing_time` | datetime | YES | NULL | 处理时间 |
| `finish_time` | datetime | YES | NULL | 完成时间 |

### =======================2、acc_transfer_bill（AccTransferBill）：转账单表（转账的单据记录）=======================

# `acc_transfer_bill` 表结构说明

## 表基本信息

## 账单基础信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `bill_date` | varchar(10) | YES | NULL | 账单日期 |
| `transfer_num` | int | YES | NULL | 交易笔数 |
| `refund_count` | int | YES | NULL | 退款笔数 |
| `platform` | varchar(16) | YES | NULL | 支付平台(hlb-合利宝,wxb2b-微信b2b) |

## 金额统计字段（商户）
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `merchant_total_pay_amt` | decimal(16,2) | YES | NULL | 商户总支付金额 |
| `merchant_total_refund_amt` | decimal(16,2) | YES | NULL | 商户总退款金额 |
| `merchant_total_pay_free` | decimal(10,2) | YES | NULL | 商户总支付手续费 |
| `merchant_total_refund_free` | decimal(10,2) | YES | NULL | 商户总退款手续费 |

## 金额统计字段（平台）
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `platform_total_pay_amt` | decimal(16,2) | YES | NULL | 平台总支付金额 |
| `platform_total_refund_amt` | decimal(16,2) | YES | NULL | 平台总退款金额 |
| `platform_total_pay_free` | decimal(10,2) | YES | NULL | 平台总支付手续费 |
| `platform_total_refund_free` | decimal(10,2) | YES | NULL | 平台总退款手续费 |



### =======================3、acc_transfer_bill_order（AccTransferBillOrder）：转账单订单表（转账单与订单的关联）=======================
# `acc_transfer_bill_order` 表结构说明

## 账单关联信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `transfer_bill_id` | bigint | YES | NULL | 交易账单id |
| `transfer_time` | datetime | YES | NULL | 交易时间 |
| `platform` | varchar(16) | YES | NULL | 支付平台(hlb-合利宝,wxb2b-微信b2b) |
| `order_type` | tinyint | NOT NULL | 0 | 0-支付, 1-退款 |

## 交易金额信息（平台）
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `platform_pay_amt` | decimal(10,2) | YES | NULL | 平台支付金额 |
| `platform_refund_amt` | decimal(10,2) | YES | NULL | 平台退款金额 |
| `platform_pay_free` | decimal(10,2) | YES | NULL | 平台支付手续费 |
| `platform_refund_free` | decimal(10,2) | YES | NULL | 平台退款手续费 |

## 交易金额信息（商户）
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `merchant_pay_amt` | decimal(10,2) | YES | NULL | 商户支付金额 |
| `merchant_refund_amt` | decimal(10,2) | YES | NULL | 商户退款金额 |
| `merchant_pay_free` | decimal(10,2) | YES | NULL | 商户支付手续费 |
| `merchant_refund_free` | decimal(10,2) | YES | NULL | 商户退款手续费 |

## 交易单号信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `platform_trade_no` | varchar(64) | YES | NULL | 平台交易单号(外部) |
| `merchant_trade_no` | varchar(64) | YES | NULL | 商户交易单号(内部) |
| `alt_no` | varchar(32) | YES | NULL | 商户号 |
| `busi_trade_no` | varchar(64) | YES | NULL | 业务单号(订单号, 退款单号...) |

## 状态与备注
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `state` | tinyint | NOT NULL | 0 | 0-正常,1-异常 |
| `remark` | varchar(32) | YES | NULL | 备注(金额不对, 支付/退款状态不对) |

### =======================4、acc_transfer_flow（AccTransferFlow）：转账流水表（转账资金流转明细）=======================================
# `acc_transfer_flow` 表结构说明

## 主键字段
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `transfer_flow_id` | bigint | NOT NULL | AUTO_INCREMENT | 转账流水id |

## 业务关联信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `busi_type` | varchar(32) | YES | NULL | 业务类型(数据字典) |
| `busi_id` | bigint | YES | NULL | 业务单据id |
| `platform` | varchar(16) | YES | NULL | 支付平台(数据字典) |
| `transfer_no` | varchar(32) | YES | NULL | 转账单号 |
| `out_flow_no` | varchar(32) | YES | NULL | 支付平台流水号(回调返回) |

## 转出方信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `source_merchant_type` | varchar(16) | YES | NULL | 转出方商户类型 |
| `source_merchant_id` | bigint | YES | NULL | 转出方商户id |
| `source_alt_mch_no` | varchar(32) | YES | NULL | 转出方分账方商户编号 |
| `source_alt_mch_name` | varchar(64) | YES | NULL | 转出方分账方商户名 |

## 转入方信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `target_merchant_type` | varchar(16) | YES | NULL | 转入方商户类型 |
| `target_merchant_id` | int | YES | NULL | 转入方商户id |
| `target_alt_mch_no` | varchar(32) | YES | NULL | 转入方分账方商户编号 |
| `target_alt_mch_name` | varchar(64) | YES | NULL | 转入方分账方商户名 |

## 转账金额信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `transfer_amt` | decimal(10,2) | YES | NULL | 转账金额 |
| `transfer_fee` | decimal(10,2) | YES | NULL | 转账手续费 |
| `transfer_debit_amount` | decimal(10,2) | YES | NULL | 实际扣款金额(转账金额+手续费) |

## 状态与时间信息
| 字段名 | 类型 | 可空 | 默认值 | 注释 |
|--------|------|------|--------|------|
| `state` | int | YES | 0 | 状态(0-已提交 1-处理中 2-已完成 3-转账失败) |
| `transfer_type` | int | YES | 1 | 转账类型(1-平台转分帐方 2-分帐方转分帐方 3-分帐方转平台) |
| `transfer_remark` | varchar(128) | YES | NULL | 转账说明 |
| `init_time` | datetime(3) | YES | NULL | 申请时间(毫秒级) |
| `processing_time` | datetime(3) | YES | NULL | 处理时间(毫秒级) |
| `finish_time` | datetime(3) | YES | NULL | 完成时间(毫秒级) |
| `error_reason` | varchar(64) | YES | NULL | 失败原因 |