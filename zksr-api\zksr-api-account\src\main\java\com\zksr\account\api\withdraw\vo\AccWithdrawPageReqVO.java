package com.zksr.account.api.withdraw.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.enums.WithdrawStateEnum;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 账户提现单对象 acc_withdraw
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@ApiModel("账户提现单 - acc_withdraw分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccWithdrawPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 账户提现单 */
    @ApiModelProperty(value = "转账失败信息;转账失败时写入")
    private Long withdrawId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 商户类型
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    /** 商户id */
    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    @ApiModelProperty(value = "审核状态,0-未审核,1-已审核,2-已拒绝")
    private Integer auditState;

    @ApiModelProperty(value = "转账状态,10-转账中,11-转账成功,12-转账失败")
    private Integer transferState;

    @ApiModelProperty(value = "打款状态,13-打款中,14-打款成功,15-打款失败")
    private Integer settleState;

    /**
     * 提现状态
     * {@link WithdrawStateEnum}
     */
    @ApiModelProperty(value = "实际提状态", hidden = true)
    private List<Integer> state;

    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTimeEnd;

    /**
     * 审核开始时间
     */
    @ApiModelProperty(value = "审核开始时间")
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date approveTime;

    /**
     * 审核结束时间
     */
    @ApiModelProperty(value = "审核结束时间")
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date approveTimeEnd;

    /**
     * 商户类型
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型集合", hidden = true)
    private List<String> merchantTypeList;

    @ApiModelProperty(value = "运营商ID")
    private Long dcId;

    /**
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "账户支付平台")
    private String accountPlatform;

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
        if (Objects.nonNull(auditState)) {
            state = new ArrayList<>();
            switch (auditState) {
                case 0:
                    state.add(WithdrawStateEnum.INIT.getState());
                    break;
                case 1:
                    state.add(WithdrawStateEnum.PROCESSING.getState());
                    state.add(WithdrawStateEnum.FINISH.getState());
                    state.add(WithdrawStateEnum.FAIL.getState());
                    break;
                case 2:
                    state.add(WithdrawStateEnum.REJECT.getState());
                    break;
            }


        }
    }
}
