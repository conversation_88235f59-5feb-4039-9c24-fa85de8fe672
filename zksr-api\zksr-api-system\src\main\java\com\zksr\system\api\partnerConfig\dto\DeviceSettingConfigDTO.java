package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 平台商设备配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DeviceSettingConfigDTO {
    /**
     * 打印机设置
     */
    @ApiModelProperty("打印机设置  字典 sys_print_setting")
    private String printSetting;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;
}
