package com.zksr.member.api.loginHis;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.loginHis.vo.MemLoginHisVO;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2024/12/19 10:05
 * @注释
 */
@FeignClient(
        contextId = "remoteLoginHisApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface LoginHisApi {

    String PREFIX = ApiConstants.PREFIX + "/loginHis";

    /**
     * 新增登录日志
     */
    @PostMapping(PREFIX + "/addLoginHis")
    CommonResult<Long> addLoginHis(@RequestBody MemLoginHisVO createReqVO);
}
