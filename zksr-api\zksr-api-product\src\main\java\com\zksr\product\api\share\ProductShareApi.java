package com.zksr.product.api.share;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.share.dto.BatchProductShareDTO;
import com.zksr.product.api.share.dto.PrdtProductShareRespDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/6 17:32
 * 商品分享转发
 */
@FeignClient(
        contextId = "remoteProductShareApi",
        value = ApiConstants.NAME
)
public interface ProductShareApi {
    String PREFIX = ApiConstants.PREFIX + "/share";

    /**
     * 批量新增商品分享记录
     * @param items
     */
    @PostMapping(PREFIX + "/batchInsertProductShare")
    CommonResult<String> batchInsertProductShare(@RequestBody BatchProductShareDTO items);


    /**
     * 根据分享Key获取分享商品信息
     * @param shareKey
     */
    @PostMapping(PREFIX + "/getShareProductInfo")
    CommonResult<List<PrdtProductShareRespDTO>> getShareProductInfo(@RequestParam("shareKey") String shareKey);
}
