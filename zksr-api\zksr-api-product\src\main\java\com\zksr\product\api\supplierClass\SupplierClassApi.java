package com.zksr.product.api.supplierClass;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.api.supplierClass.dto.SupplierClassRespDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@FeignClient( contextId = "supplierClassApi" , value = ApiConstants.NAME)
public interface SupplierClassApi {

    String PREFIX = ApiConstants.PREFIX + "/supplierClass";

    /**
    * @Description: 通过入驻商ID获取绑定的所有管理分类ID
    * @Param: Long supplierId
    * @return: List<Long>
    * @Author: liuxingyu
    * @Date: 2024/3/6 15:07
    */
    @GetMapping(PREFIX)
    CommonResult<List<Long>> getCatgoryIdListBySupplierId(@RequestParam("supplierId") Long supplierId);

    /**
    * @Description: 新增入驻商绑定管理类别
    * @Param: PrdtSupplierClassRespDto prdtSupplierClassRespDto
    * @return: CommonResult<Integer>
    * @Author: liuxingyu
    * @Date: 2024/3/6 16:28
    */
    @PostMapping(PREFIX)
    CommonResult<Boolean> insetBath(@RequestBody PrdtSupplierClassRespDTO prdtSupplierClassRespDto);

    /**
    * @Description: 根据入驻商ID修改管理类别绑定关系
    * @Param: PrdtSupplierClassRespDto respDto
    * @return: CommonResult<Boolean>
    * @Author: liuxingyu
    * @Date: 2024/3/6 17:18
    */
    @PutMapping(PREFIX)
    CommonResult<Boolean> updateBySupplierId(@RequestBody PrdtSupplierClassRespDTO respDto);

    /**
     * 获取入驻商一级管理分类销售分润占比
     * @param supplierId
     * @return
     */
    @GetMapping(PREFIX + "/getCatgoryRateListBySupplierId")
    CommonResult<List<PrdtSupplierClassRateDTO>> getCatgoryRateListBySupplierId(@RequestParam("supplierId") Long supplierId);

    @GetMapping(PREFIX + "/getSupplierClassRate")
    CommonResult<BigDecimal> getSupplierClassRate(@RequestParam("supplierId") Long supplierId, @RequestParam("catgoryId") Long catgoryId);

    /**
     * 根据入驻商ID和分类ID获取三级管理分类售后配置
     * @param supplierIds
     * @param catgoryIds
     * @return
     */
    @GetMapping(PREFIX + "/getSupplierClassAfterConfig")
    CommonResult<List<SupplierClassRespDTO>> getSupplierClassAfterConfig(@RequestParam("supplierIds") List<Long> supplierIds, @RequestParam("catgoryIds") List<Long> catgoryIds);
}
