package com.zksr.promotion.api.coupon.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 异步给门店发送优惠券
 * @date 2024/4/2 9:19
 */
@Data
@ApiModel(description = "异步给门店发送优惠券")
public class CouponReceiveStatusDTO {

    @ApiModelProperty("优惠券ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty("状态查询ID, 领取优惠券后不一定马上会发放成功, 60秒有效")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long statusId;

    @ApiModelProperty("0-领取成功,other=失败")
    private Integer code = 0;

    @ApiModelProperty("失败原因, code!=0 时返回")
    private String msg;

    public boolean isSuccess() {
        return code == 0;
    }

    public CouponReceiveStatusDTO() {
    }

    public void setErr(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.msg = errorCode.getMsg();
    }

    public CouponReceiveStatusDTO(Long couponTemplateId, Long statusId) {
        this.couponTemplateId = couponTemplateId;
        this.statusId = statusId;
    }
}
