package com.zksr.system.api.area.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 省市区对象 sys_area_city
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
@ApiModel("省市区 - sys_area_city Response VO")
public class SysAreaCityRespVO {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "城市区域名称")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaCityId;

    /** 父城市区域 */
    @Excel(name = "父城市区域")
    @ApiModelProperty(value = "父城市区域")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 级别: 0 省份, 1 城市, 2 区域 */
    @Excel(name = "级别: 0 省份, 1 城市, 2 区域")
    @ApiModelProperty(value = "级别: 0 省份, 1 城市, 2 区域")
    private Integer deep;

    /** 编码 */
    @Excel(name = "编码")
    @ApiModelProperty(value = "编码")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long extId;

    /** 城市区域名称 */
    @Excel(name = "城市区域名称")
    @ApiModelProperty(value = "城市区域名称")
    private String name;

}
