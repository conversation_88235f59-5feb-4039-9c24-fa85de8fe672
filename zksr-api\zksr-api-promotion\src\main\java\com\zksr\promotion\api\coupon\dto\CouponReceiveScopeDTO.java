package com.zksr.promotion.api.coupon.dto;

import com.zksr.common.core.enums.CouponReceiveScope;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 具体领取范围
 * @date 2024/4/3 8:39
 */
@Data
@NoArgsConstructor
public class CouponReceiveScopeDTO {

    @ApiModelProperty("领取范围;0-全部可领 1-指定渠道可领（func_scope=2可选）2-指定渠道可领（func_scope=2可选） 2-指定平台商城市分组可领（func_scope=2可选） 3-指定城市可领   4-指定业务员下的门店可领")
    private Integer receiveScope;

    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer whiteOrBlack = NumberPool.INT_ONE;

    @ApiModelProperty("具体使用范围ID, 例如入驻商ID")
    private Long applyId;

    @ApiModelProperty("具体使用范围名称, 例如入驻商名称")
    private String applyName;

    public CouponReceiveScopeDTO(Long applyId, String applyName, Integer receiveScope) {
        this.applyId = applyId;
        this.applyName = applyName;
        this.receiveScope = receiveScope;
    }

    public CouponReceiveScopeDTO(Long applyId) {
        this.applyId = applyId;
    }

    public static CouponReceiveScopeDTO build(ChannelDTO channelDTO) {
        if (Objects.isNull(channelDTO)) {
            return new CouponReceiveScopeDTO(Long.MAX_VALUE);
        }
        return new CouponReceiveScopeDTO(channelDTO.getChannelId(), channelDTO.getChannelName(), CouponReceiveScope.CHANNEL.getScope());
    }

    public static CouponReceiveScopeDTO build(AreaDTO areaDTO) {
        if (Objects.isNull(areaDTO)) {
            return new CouponReceiveScopeDTO(Long.MAX_VALUE);
        }
        return new CouponReceiveScopeDTO(areaDTO.getAreaId(), areaDTO.getAreaName(), CouponReceiveScope.AREA.getScope());
    }
}
