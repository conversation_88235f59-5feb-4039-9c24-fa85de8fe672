package com.zksr.system.api.partnerConfig.enums;

import lombok.Getter;

/**
* @Description: 软件商配置枚举 注意不要与平台配置枚举type冲突(com.zksr.system.api.partnerPolicy.enums.PartnerPolicyEnum)
 *  *              配置字典(sys_partner_policy_type)
* @Author: liuxingyu
* @Date: 2024/4/23 9:12
*/
public enum PartnerConfigEnum {
    APPLET_BASE_CONFIG(0, "小程序设置"),
    HELIBAO_PAY_CONFIG(1, "合利宝配置"),
    PAY_CONFIG(2, "支付配置"),
    PAY_ACCOUNT_CONFIG(6, "支付账号配置"),
    COURIER_CONFIG(7, "快递配置"),
    DEVICE_SETTING_POLICY(13, "设备设置"),
    SMS_CONFIG(14, "短信设置"),
    MIDEA_PAY_CONFIG(15, "美的付配置"),
    WXB2B_PAY_CONFIG(16, "微信b2b支付配置"),
    WXB2B_PAY_STATE(17, "微信b2b支付申请状态", "wxB2bPayAppletStatus."),
    ;

    @Getter
    Integer type;
    @Getter
    String name;
    @Getter
    String key;

    PartnerConfigEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
    PartnerConfigEnum(Integer type, String name, String key) {
        this.type = type;
        this.name = name;
        this.key = key;
    }
}
