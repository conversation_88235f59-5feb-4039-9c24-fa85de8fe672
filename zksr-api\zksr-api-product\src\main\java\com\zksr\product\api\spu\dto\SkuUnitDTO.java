package com.zksr.product.api.spu.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/31 8:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuUnitDTO {

    @ApiModelProperty("单位标准价")
    private BigDecimal markPrice;

    @ApiModelProperty("建议零售价")
    private BigDecimal suggestPrice;

    @ApiModelProperty("条码")
    private String barcode;

    @ApiModelProperty("单位-字典")
    private String unit;

    @ApiModelProperty("单位名称")
    private String unitName;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty("单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize;

    @ApiModelProperty("单位起订")
    private Long minOq;

    @ApiModelProperty("单位订货组")
    private Long jumpOq;

    @ApiModelProperty("单位最大限购")
    private Long maxOq;
}
