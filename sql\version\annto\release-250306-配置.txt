 -- ----------------------------------- zksr-gateway-dev.yml 增加配置 start ---------------------------------
-- nacos 配置
-- zksr-gateway-dev.yml 增加白名单接口
- /portal/mall/index/getChannelInfo

 -- -----------------------------------  zksr-gateway-dev.yml 增加配置 end ---------------------------------


 -- -----------------------------------  xxl-job 增加配置 start ---------------------------------
 任务描述     :  	定时执行业务员日结、月结数据
 JobHandler  :   colonel<PERSON>ay<PERSON>ettleJobHandler
 CRON        :   0 30 1 * * ?
 状态        ： 	RUNNING
 ------------------------------------------------


 任务描述     :  	手动执行业务员日结、月结数据（后台人员处理，切勿操作）
 JobHandler  :   colonelSettleJobHandler
 CRON        :   0 0 0 1 1 ? *
 状态        ： 		STOP
 任务参数     ：平台编码;开始时间;结束时间（例：4;2024-01-01;2024-02-01）
  ------------------------------------------------

 任务描述     :  	同步行政区域
 JobHandler  :   syncAreaJobHandler
 CRON        :   0 0 0 * * ?
 状态        ： 	STOP
  ------------------------------------------------

 -- -----------------------------------  xxl-job 增加配置 end ---------------------------------




 -- ----------------------------------- zksr-system-dev.yml 增加配置 start ---------------------------------
 -- 【开发接口优化】针对推送失败的单据 通过发邮件的形式，告知给客户和实施人员 新增Nacos配置
 -- zksr-system-dev.yml
 -- 配置在 spring: 下面
 # 设置邮箱通知
   mail:
     # 设置邮箱主机 网易: smtp.163.com  QQ邮箱:smtp.qq.com
     host: smtp.qq.com
     # 设置用户名
     username: <EMAIL>
     # 设置密码 注这个不是真实的密码，是在邮箱设置里面生成的授权码
     password: kokeoojocahybfja
     properties:
       mail:
         smtp:
           # 设置是否需要认证，如果为true,那么用户名和密码就必须的
           #如果设置false，可以不设置用户名和密码，当然也得看你的对接的平台是否支持无密码进行访问的。
           auth: true
           starttls:
             # STARTTLS[1]  是对纯文本通信协议的扩展。它提供一种方式将纯文本连接升级为加密连接（TLS或SSL），而不是另外使用一个端口作加密通信。
             enable: true
             required: true

-- -----------------------------------  zksr-system-dev.yml 增加配置 end ---------------------------------

---------------------------------------application-rocketmq-system-dev.yml 增加配置---------------------------------
spring:
  cloud:
    #spring-cloud-stream 配置
    stream:
      function:
        # spring-cloud-stream规范 函数式编程定义的BEAN name（这里只定义了消费者入口）
        definition:  ----配置下多加个 fileImportTaskEvent

        bindings: ----最下面新增配置
            # 文件导入消息发送
                    fileImportTaskEvent-out-0:
                      destination: fileImportTaskEvent
                    fileImportTaskEvent-in-0:
                      destination: fileImportTaskEvent
                      group: fileImportTaskEventGroup


---------------------------------------zksr-job-executor-sit.yml 增加配置---------------------------------
#saas-dc配置
saasDcApi:
  host: https://saasopen.annto.com
  urlPrefix: /bop/annto/saasOp
  api:
    selectByNameList: /ebPlaceInnerSelectByNameList
    pageSearch: /ebPlaceInnerPage