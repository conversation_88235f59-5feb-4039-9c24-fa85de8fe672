package com.zksr.account.api.withdraw.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现对账单对象 acc_withdraw_bill
 * 
 * @date 2024-03-22
 */
@TableName(value = "acc_withdraw_bill")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccWithdrawBillDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 提现账单ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long withdrawBillId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 平台提现金额 */
    @Excel(name = "平台提现金额")
    private BigDecimal platformWithdrawAmt;

    /** 商户提现金额 */
    @Excel(name = "商户提现金额")
    private BigDecimal merchantWithdrawAmt;

    /** 平台提现手续费 */
    @Excel(name = "平台提现手续费")
    private BigDecimal platformFree;

    /** 商户提现手续费 */
    @Excel(name = "商户提现手续费")
    private BigDecimal merchantFree;

    /** 平台交易单号 */
    @Excel(name = "平台交易单号")
    private String platformTradeNo;

    /** 商户交易单号 */
    @Excel(name = "商户交易单号")
    private String merchantTradeNo;

    /** 提现到账银行卡号 */
    @Excel(name = "提现到账银行卡号")
    private String bankAccountNo;

    /** 提现到账银行账户名称 */
    @Excel(name = "提现到账银行账户名称")
    private String bankAccountName;

    /** 提现发起时间 */
    @Excel(name = "提现发起时间")
    private Date requestTime;

    /** 提现完成时间 */
    @Excel(name = "提现完成时间")
    private Date finishTime;

    /** 商户号 */
    @Excel(name = "商户号")
    private String altNo;

    /** 状态 */
    @Excel(name = "状态")
    private Integer state;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 支付平台 */
    @Excel(name = "支付平台")
    private String platform;
}