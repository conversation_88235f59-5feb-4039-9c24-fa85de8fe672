package com.zksr.promotion.api.activity.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动验证商品
 * @date 2024/5/18 9:58
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ApiModel(description = "促销活动验证商品")
public class ActivityVerifyItemDTO {

    @ApiModelProperty(value = "商品类型", name = "默认本地")
    private String type = ProductType.LOCAL.getType();

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    @ApiModelProperty("品牌ID")
    private Long brandId;

    @ApiModelProperty("管理分类ID")
    private Long categoryId;

    @ApiModelProperty("商品总数")
    private Integer num = NumberPool.INT_ZERO;

    @ApiModelProperty("商品单价")
    private BigDecimal itemPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "应付金额", notes = "会根据促销计算实时调整")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "参与活动价格", notes = "特价/秒杀价格, 返回填充")
    private BigDecimal activityPrice;

    @ApiModelProperty(value = "参与活动数量", notes = "特价/秒杀数量, 返回填充")
    private Integer activityNum;

    @ApiModelProperty(value = "参与活动应付金额", notes = "会根据促销计算实时调整")
    private BigDecimal activityPayAmt;

    @ApiModelProperty(value = "已满足的促销活动", notes = "特价/秒杀价格, 返回填充")
    @Builder.Default
    private List<ActivityLabelInfoVO> adequateList = new ArrayList<>();

    @ApiModelProperty("是否选中")
    @Builder.Default
    private Boolean selected = Boolean.TRUE;

    @ApiModelProperty(value = "unit, 单位")
    private String unit = StringPool.ZERO;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize = 1;

    @ApiModelProperty("一个中单位等于多少个小单位")
    private BigDecimal midSize;

    @ApiModelProperty("一个大单位等于多少个小单位")
    private BigDecimal largeSize;

    @ApiModelProperty(value = "和最小单位库存转换比例")
    private BigDecimal stockConvertRate = BigDecimal.ONE;

    @ApiModelProperty(value = "上架商品ID")
    private Long itemId;

    @ApiModelProperty(value = "加入购物车时间,毫秒值", notes = "某些特殊场景下需要用到, 比如秒杀/特价, 限定了sku需要根据加入购物车时间优先命中")
    private Long carTime = NumberPool.LONG_ZERO;

    @ApiModelProperty(value = "购物车满减活动多入驻商使用")
    private BigDecimal discountAmt = BigDecimal.ZERO;

    @ApiModelProperty(value = "参与计算的满减总金额")
    private BigDecimal calcTotalAmt;

    @ApiModelProperty(value = "参与计算的满减总数量")
    private BigDecimal calcTotalNum;

    @ApiModelProperty(value = "已参与的满减活动")
    private List<Long> fdActivityIdList;

    public BigDecimal getAmt() {
        BigDecimal totalAmt = BigDecimal.ZERO;
        if (Objects.nonNull(this.getActivityNum())) {
            totalAmt = totalAmt.add(this.getItemPrice().multiply(new BigDecimal(this.getNum() - this.getActivityNum())));
            totalAmt = totalAmt.add(this.getActivityPrice().multiply(new BigDecimal(this.getActivityNum())));
        } else {
            totalAmt = totalAmt.add(this.getItemPrice().multiply(new BigDecimal(this.getNum())));
        }
        return totalAmt;
    }

    @JsonIgnore
    public String getUniqueKey() {
        return StringUtils.format("{}_{}", itemId, unitSize);
    }


    @JsonIgnore
    public BigDecimal getStockConvertRate() {
        if (Objects.isNull(stockConvertRate)) {
            return BigDecimal.ONE;
        }
        return stockConvertRate;
    }

    @JsonIgnore
    public BigDecimal getUnitNum() {
        if (UnitTypeEnum.M(this.getUnitSize()) && Objects.nonNull(midSize)) {
            return StockUtil.stockMultiply(this.num, midSize);
        }
        if (UnitTypeEnum.L(this.getUnitSize()) && Objects.nonNull(largeSize)) {
            return StockUtil.stockMultiply(this.num, largeSize);
        }
        return StockUtil.bigDecimal(this.num);
    }

    @JsonIgnore
    public BigDecimal getNumByUnit(Integer targetUnitSize) {
        if (Objects.isNull(targetUnitSize) || targetUnitSize < UnitTypeEnum.UNIT_SMALL.getType() || targetUnitSize > UnitTypeEnum.UNIT_LARGE.getType()) {
            return BigDecimal.ZERO;
        }
        BigDecimal[] sizeArray = new BigDecimal[] {null, BigDecimal.ONE, midSize, largeSize};
        if (sizeArray[this.unitSize] == null || sizeArray[targetUnitSize] == null || sizeArray[targetUnitSize].compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return sizeArray[this.unitSize].multiply(new BigDecimal(this.num)).divide(sizeArray[targetUnitSize], 8, RoundingMode.HALF_UP);
    }

    @JsonIgnore
    public BigDecimal getActivityUnitNum() {
        if (UnitTypeEnum.M(this.getUnitSize()) && Objects.nonNull(midSize)) {
            return StockUtil.stockMultiply(this.activityNum, midSize);
        }
        if (UnitTypeEnum.L(this.getUnitSize()) && Objects.nonNull(largeSize)) {
            return StockUtil.stockMultiply(this.activityNum, largeSize);
        }
        return StockUtil.bigDecimal(this.activityNum);
    }

    @JsonIgnore
    public BigDecimal getActivityUnitNum(BigDecimal activityNum) {
        if (UnitTypeEnum.M(this.getUnitSize()) && Objects.nonNull(midSize)) {
            return StockUtil.stockMultiply(activityNum, this.midSize);
        }
        if (UnitTypeEnum.L(this.getUnitSize()) && Objects.nonNull(largeSize)) {
            return StockUtil.stockMultiply(activityNum, this.largeSize);
        }
        return activityNum;
    }
}
