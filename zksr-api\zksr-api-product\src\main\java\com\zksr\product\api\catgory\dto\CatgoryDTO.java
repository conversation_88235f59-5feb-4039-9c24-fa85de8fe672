package com.zksr.product.api.catgory.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 平台商管理分类对象 prdt_catgory
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CatgoryDTO {

    /**
     * 平台商管理分类id
     */
    @ApiModelProperty(value = "平台商管理分类id")
    private Long catgoryId;


    /**
     * 父id
     */
    @ApiModelProperty(value = "父id", required = true)
    private Long pid;

    /**
     * 分类名
     */
    @ApiModelProperty(value = "分类名", required = true)
    private String catgoryName;

   /* *//**
     * 分类编号
     *//*
    @ApiModelProperty(value = "分类编号", required = true)
    private String catgoryNo;*/  //TODO 暂时没有用上



    /**
     * 平台商id
     */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 分类图标
     */
    @ApiModelProperty(value = "分类图标")
    private String icon;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 状态 0-停用 1-启用
     */
    @ApiModelProperty(value = "状态 0-停用 1-启用")
    private String status;

/*    *//** 软件商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01 *//*
    @Excel(name = "软件商分润比例", readConverterExp = "只一级分类设定")
    @ApiModelProperty(value = "软件商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01")
    private BigDecimal softwareRate;*/


    /**
     * 平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01
     */
    @ApiModelProperty(value = "平台商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01")
    private BigDecimal partnerRate;

    /**
     * 运营商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01
     */
    @ApiModelProperty(value = "运营商分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01")
    private BigDecimal dcRate;

    /**
     * 业务员负责人分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01
     */
    @ApiModelProperty(value = "业务员负责人分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01")
    private BigDecimal colonel1Rate;

    /**
     * 业务员分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01
     */
    @ApiModelProperty(value = "业务员分润比例（只一级分类设定）百分比的小数表现形式，1%表示为0.01")
    private BigDecimal colonel2Rate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Integer level;

    /**
     * 销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置
     */
    @ApiModelProperty(value = "销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置")
    private BigDecimal saleTotalRate;
}
