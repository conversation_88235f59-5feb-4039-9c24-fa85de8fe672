package com.zksr.product.api.yhdata.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 9:04
 */
@Data
@Builder
@ApiModel(description = "要货批次入驻商展示分类数据")
@NoArgsConstructor
@AllArgsConstructor
public class YhBatchSupplierSaleClassReqVO {

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @JsonFormat(pattern = YYYY_MM_DD, timezone = "Asia/Shanghai")
    @NotNull(message = "要货批次时间不能为空")
    @ApiModelProperty("要货批次时间")
    private Date batchDate;

    @ApiModelProperty(value = "门店ID", hidden = true)
    private Long branchId;
}
