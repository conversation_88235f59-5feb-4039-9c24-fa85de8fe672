package com.zksr.member.api.branch.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchDTO {

    /** 门店id */
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 平台商id */
    private Long sysCode;

    /** 门店名称 */
    private String branchName;

    /** 城市id */
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 业务员id */
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 门店地址 */
    private String branchAddr;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 渠道id */
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 平台商城市分组id */
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** 联系人 */
    private String contactName;

    /** 联系电话 */
    private String contactPhone;

    /** 备注 */
    private String memo;

    /** 状态 */
    private Integer status;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 审核人 */
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核状态 */
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState = 0;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate; 		 // 过期时间

    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    @ApiModelProperty(value = "上级业务员ID")
    private Long pColonelId;

    @ApiModelProperty(value = "上级业务员级别")
    private Long pColonelLevel;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    @ApiModelProperty(value = "门店Id集合")
    private List<Long> branchIds;

    /** 是否支持货到付款(0,否 1,是) */
    private Integer hdfkSupport;

    /** 是否默认 */
    @Excel(name = "是否默认数据字典")
    private String isDefault = "N";

    /** 门头照 */
    @Excel(name = "门头照")
    private String branchImages;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @ApiModelProperty(value = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @Excel(name = "门店编号")
    @ApiModelProperty("门店编号")
    private String branchNo;

    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;

    @ApiModelProperty("微信商户认证openid")
    private String wechatMerchantAuthOpenid;

    @ApiModelProperty("是否首单, 新客标识, 首单标识 1-是 0-否")
    private Integer firstOrderFlag;

    @ApiModelProperty("一级区域城市ID, 省市区关联(省份)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long firstAreaCityId;

    @ApiModelProperty("二级区域城市ID, 省市区关联(城市)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long secondAreaCityId;

    @Excel(name = "进入公海时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date seasTime;

    @ApiModelProperty("门店生成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private Long dcId;

    @ApiModelProperty("首单单号")
    private String firstOrderNo;

    /** 生命周期阶段 */
    @Excel(name = "生命周期阶段")
    @ApiModelProperty("生命周期阶段")
    private Integer lifecycleStage;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

    /** 省份 */
    private String provinceName;

    /** 城市 */
    private String cityName;

    /** 区县 */
    private String districtName;
    
    /** 纳税人识别码 */
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

    public boolean fistOrderFlagTrue() {
        return Objects.nonNull(firstOrderFlag) && firstOrderFlag == 1;
    }

    /**
     * 该单号是否是首单单号
     * @return
     */
    public boolean fistOrderNoFlagTrue(String orderNo) {
        return Objects.nonNull(firstOrderFlag) && firstOrderFlag == 1 && ToolUtil.isNotEmpty(firstOrderNo) && Objects.equals(orderNo,firstOrderNo );
    }

    public BranchDTO(Long areaId, Long channelId, Long groupId) {
        this.areaId = areaId;
        this.channelId = channelId;
        this.groupId = groupId;
    }

    public BranchDTO(Long sysCode, Integer status, List<Long> branchIds) {
        this.sysCode = sysCode;
        this.status = status;
        this.branchIds = branchIds;
    }
}
