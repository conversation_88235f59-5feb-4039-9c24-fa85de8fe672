package com.zksr.product.api.yhdata.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货单数据保存
 * @date 2024/12/13 15:42
 */
@Data
public class YhBatchSaveReqVO {

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @JsonFormat(pattern = YYYY_MM_DD, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "批次日期")
    private Date batchDate;

    @ApiModelProperty(value = "门店ID", hidden = true)
    private Long branchId;

    @ApiModelProperty("操作数据, 最多200条数据, 需要通过交互优化一次提交数据")
    @Size(min = 1, max = 100, message = "单次最多操作200条数据")
    private List<YhDtl> yhDtls;

    @Data
    public static class YhDtl {

        @ApiModelProperty("要货ID")
        private Long yhId;

        @ApiModelProperty("上架商品ID")
        private Long itemId;

        @ApiModelProperty("操作数量")
        private Integer productNum;

        @ApiModelProperty("是否覆写商品数量,默认覆写")
        private Boolean overProductNum = true;

        @ApiModelProperty("是否选中")
        private Boolean selected;

        @ApiModelProperty("是否删除")
        private Boolean isDelete = false;

        @ApiModelProperty("1-小单位,2-中单位,3-大单位")
        private Integer unitSize;

        @ApiModelProperty("skuId")
        private Long skuId;

        @JsonIgnore
        public String getUnitSkuKey() {
            return StringUtils.format("{}:{}", this.getSkuId(), this.getUnitSize());
        }
    }

}
