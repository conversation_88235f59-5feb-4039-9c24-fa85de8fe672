package com.zksr.system.api.domain;

import com.zksr.common.core.annotation.Excel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/4/2 17:21
 */
@Data
public class SysUserImportVO {

    /** 用户账号 */
    @Excel(name = "登录名称", headerColor = IndexedColors.RED)
    private String userName;

    /** 密码 */
    @Excel(name = "登陆密码", headerColor = IndexedColors.RED)
    private String password;

    /** 用户昵称 */
    @Excel(name = "用户名称", headerColor = IndexedColors.RED)
    private String nickName;

    /** 用户邮箱 */
    @Excel(name = "用户邮箱")
    private String email;

    /** 手机号码 */
    @Excel(name = "手机号码", headerColor = IndexedColors.RED)
    private String phonenumber;

}
