package com.zksr.report.api.homePages.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@ApiModel("PC首页获取销售类型（区域、门店、一级品类、运营商、商品、入驻商、业务员）Top10数据返回 - HomePagesSalesTop10DataRespDTO Response VO")
@Accessors(chain = true)
public class HomePagesSalesTop10DataRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID */
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 入驻商ID */
    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;


    /** 销售TOP10类型（区域：area，门店：branch，一级品类：category，运营商：dc，商品：itme，入驻商：supplier，业务员：colonel） */
    @ApiModelProperty(value = "销售TOP10类型")
    private String salesType;

    /** 销售TOP10类型 */
    @ApiModelProperty(value = "销售TOP10类型id")
    private Long salesTypeId;

    /** 销售TOP10类型名称 */
    @ApiModelProperty(value = "销售TOP10类型名称")
    private String salesTypeName;

    /** 销售金额  */
    @ApiModelProperty(value = "销售金额")
    private BigDecimal orderSalesAmt = BigDecimal.ZERO;

    /** 上次销售金额 */
    @ApiModelProperty(value = "上次销售金额")
    private BigDecimal beforeOrderSalesAmt = BigDecimal.ZERO;

}
