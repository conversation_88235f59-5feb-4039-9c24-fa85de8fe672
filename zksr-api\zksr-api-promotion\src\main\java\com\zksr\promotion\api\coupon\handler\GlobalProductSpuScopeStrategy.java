package com.zksr.promotion.api.coupon.handler;

import com.zksr.common.core.enums.ProductType;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.promotion.api.coupon.handler.applySpuScope.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 处理全国商品
 * @date 2024/4/3 15:59
 */
public class GlobalProductSpuScopeStrategy implements CouponSpuScopeStrategy{

    static List<CouponSpuScopeStrategy> applySpuScopeList = new ArrayList<>();
    static {
        // 多范围
        applySpuScopeList.add(new CouponCouponMultiSpuScopeStrategy());
        // 全场券
        // applySpuScopeList.add(new QcCouponSpuScopeStrategy());
        // 类别券
        // applySpuScopeList.add(new ClassCouponCouponSpuScopeStrategy());
        // 品牌券
        // applySpuScopeList.add(new BrandCouponCouponSpuScopeStrategy());
        // 商品券
        // applySpuScopeList.add(new SpuCouponCouponSpuScopeStrategy());
        // 入驻商券
        //applySpuScopeList.add(new SupplierCouponCouponSpuScopeStrategy());
    }

    public static GlobalProductSpuScopeStrategy getInstance() {
        return new GlobalProductSpuScopeStrategy();
    }

    @Override
    public void productFilter(List<OrderValidItemDTO> items, Set<CouponDTO> coupons, Map<Long, CouponTemplateDTO> couponTemplateMap, Set<CouponDTO> availableSet) {
        // 全国商品
        List<OrderValidItemDTO> list = items.stream().filter(item -> ProductType.GLOBAL.getType().equals(item.getProductType())).collect(Collectors.toList());
        for (CouponSpuScopeStrategy spuScopeStrategy : applySpuScopeList) {
            spuScopeStrategy.productFilter(list, coupons, couponTemplateMap, availableSet);
        }
    }
}
