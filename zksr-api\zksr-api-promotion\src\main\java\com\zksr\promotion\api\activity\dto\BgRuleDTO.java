package com.zksr.promotion.api.activity.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 买赠条件规则对象 prm_bg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BgRuleDTO extends GiveRuleDTO implements Serializable {

    private static final long serialVersionUID=1L;

    /** 买赠条件规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long bgRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 买赠活动id */
    @Excel(name = "买赠活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 触发赠送的数量 */
    @Excel(name = "触发赠送的数量")
    private Integer ruleQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 触发条件的单位 */
    @ApiModelProperty(value = "触发条件的单位", example = "1：小单位, 2：中单位, 3：大单位")
    @Excel(name = "触发条件的单位 1：小单位, 2：中单位, 3：大单位")
    private Integer ruleUnitType;
}
