package com.zksr.trade.api.order.vo;

import java.util.List;

import lombok.Data;

/**
 * O2O任务参数
 */
@Data
public class O2OGenerateSettleParamVO {
    /**
     * 是否测试环境
     */
    boolean isTest = false;
    /**
     * 系统编码
     */
    private Long sysCode;
    
    /**
     * 订单号列表
     */
    private List<String> orderNos;
    
    /**
     * 入驻商订单号列表
     */
    private List<String> supplierOrderNos;
    
    /**
     * 签收后多少分钟可以结算（默认7天 = 10080分钟）
     */
    private Integer signAfterMinutes = 10080;
}