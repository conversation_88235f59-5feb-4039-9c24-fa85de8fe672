package com.zksr.trade.api.order.vo;

import java.util.List;

import lombok.Data;

/**
 * O2O任务参数
 */
@Data
public class O2OGenerateSettleParamVO {
    /**
     * 系统编码
     */
    private Long sysCode;
    
    /**
     * 订单号列表
     */
    private List<String> orderNos;
    
    /**
     * 入驻商订单号列表
     */
    private List<String> supplierOrderNos;
    
    /**
     * 签收后多少分钟可以结算（默认7天 = 10080分钟）
     */
    private Integer signAfterMinutes = 10080;
    
    /**
     * 查询签收后多少天的结算记录（默认14天 = 20160分钟） - 有可能可能放假7天，所以查询范围要扩大成 7天 + 放假7天 = 14天
     */
    private Integer signSearchMinutes = 10080;
}