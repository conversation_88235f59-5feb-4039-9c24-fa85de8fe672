package com.zksr.account.api.platformMerchant.vo;

import com.zksr.account.api.platformMerchant.vo.PlatformMerchantRegisterSaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("支付平台商户 - acc_platform_merchant 进件保存")
public class AccPlatformMerchantRegisterReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户类型
     * 参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty("商户类型: partner-平台商, dc-运营商, supplier-入驻商, colonel-业务员")
    private String merchantType;

    /**
     * 商户ID
     */
    @ApiModelProperty("商户ID: sysCode, dcId, supplierId, colonelId")
    private Long merchantId;

    /**
     * 支付平台信息
     */
    @ApiModelProperty("支付平台信息: hlb-合利宝, mideaPay-美的支付, mock-模拟支付, wxb2b-微信B2B")
    private String platform;

    /**
     * 平台商编号
     */
    @ApiModelProperty("平台商编号")
    private Long sysCode;

    /**
     * 商户信息(合利宝), 默认信息
     */
    @ApiModelProperty("商户信息(合利宝), 默认信息")
    private PlatformMerchantRegisterSaveReqVO platformMerchantRegisterSaveReqVO;

    /**
     * 微信B2B进件数据
     */
    @ApiModelProperty("微信B2B进件数据")
    private WxB2bPlatformMerchantRegisterSaveReqVO wxB2bPlatformMerchantRegisterSaveReqVO;
}
