package com.zksr.product.api.supplierItem;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.api.supplierItem.vo.ApiSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteSupplierItemApi",
        value = ApiConstants.NAME
)
/**
*
 *  入驻商服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface SupplierItemApi {

    String PREFIX = ApiConstants.PREFIX + "/supplierItem";

    @GetMapping(PREFIX + "/getBySupplierItemId")
    public CommonResult<SupplierItemDTO> getBySupplierItemId(@RequestParam("supplierItemId") Long supplierItemId);

    @PostMapping(PREFIX + "/getSupplierItemListByApi")
    public CommonResult<List<SupplierItemDTO>> getSupplierItemListByApi(@RequestBody SupplierItemDTO itemDTO);

    @PostMapping(PREFIX + "/getSupplierItemPageByApi")
    public CommonResult<PageResult<PrdtSupplierItemPageRespVO>> getSupplierItemPageByApi(@RequestBody ApiSupplierItemPageReqVO supplierItemPageReqVO);


    /**
     * 统计已售数据
     * @param minSupplierItemId 最小上架ID
     * @return  返回处理批次的最小上架ID
     */
    @GetMapping(PREFIX + "/updateSaleQtyTotal")
    CommonResult<Long> updateSaleQtyTotal(@RequestParam("minSupplierItemId") Long minSupplierItemId);


    /**
     * 根据组合商品ID 查询对应入驻商商品信息
     * @param spuCombineId 组合商品ID
     * @return  SupplierItemDTO
     */
    @PostMapping(PREFIX + "/getSupplierItemBySpuCombineId")
    public CommonResult<SupplierItemDTO> getSupplierItemBySpuCombineId(@RequestParam("spuCombineId") Long spuCombineId);

    /**
     * 修改全国上架商品
     * @param itemDTO
     * @return
     */
    @PostMapping(PREFIX + "/updateSupplierItem")
    CommonResult<Boolean> updateSupplierItem(@RequestBody SupplierItemDTO itemDTO);

    /**
     * 根据活动ID 查询对应入驻商商品信息
     * @param activityId
     * @return
     */
    @PostMapping(PREFIX + "/getSupplierItemByActivityId")
    public CommonResult<SupplierItemDTO> getSupplierItemByActivityId(@RequestParam("activityId") Long activityId);
}
