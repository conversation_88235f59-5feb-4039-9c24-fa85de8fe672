package com.zksr.trade.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.trade.api.order.dto.O2OGenerateSettleOrderDTO;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.BranchTransitQtyReqVO;
import com.zksr.trade.api.order.vo.O2OGenerateSettleParamVO;
import com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import com.zksr.trade.controller.order.vo.HomePageRespVO;
import com.zksr.trade.controller.orderSupplier.vo.TrdSupplierOrderPageReqVO;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 入驻商订单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Mapper
public interface TrdSupplierOrderMapper extends BaseMapperX<TrdSupplierOrder> {
    default PageResult<TrdSupplierOrder> selectPage(TrdSupplierOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TrdSupplierOrder>()
                .eqIfPresent(TrdSupplierOrder::getSupplierOrderId, reqVO.getSupplierOrderId())
                .eqIfPresent(TrdSupplierOrder::getSysCode, reqVO.getSysCode())
                .eqIfPresent(TrdSupplierOrder::getSupplierOrderNo, reqVO.getSupplierOrderNo())
                .eqIfPresent(TrdSupplierOrder::getOrderId, reqVO.getOrderId())
                .eqIfPresent(TrdSupplierOrder::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(TrdSupplierOrder::getTransAmt, reqVO.getTransAmt())
                .eqIfPresent(TrdSupplierOrder::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(TrdSupplierOrder::getMemo, reqVO.getMemo())
                .eqIfPresent(TrdSupplierOrder::getDelFlag, reqVO.getDelFlag())
                .orderByDesc(TrdSupplierOrder::getSupplierOrderId));
    }

    /**
     * 根据主单号获取入驻商订单信息
     *
     * @param reqVO
     * @return
     */
    default List<TrdSupplierOrder> selectList(TrdSupplierOrderPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrder>()
                .eqIfPresent(TrdSupplierOrder::getOrderNo, reqVO.getOrderNo()));
    }

    /**
     * @Description: 获取入驻商小程序首页订单统计
     * @Author: liuxingyu
     * @Date: 2024/4/3 9:14
     */
    HomePageRespVO selectHomePageMonth(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("supplierId") Long supplierId);

    /**
     * 获取入驻商小程序首页统计 (按天)
     * @param supplierId
     * @return
     */
    HomePageRespVO selectHomePageDay(@Param("supplierId") Long supplierId);

    /**
     * 根据主单ID 获取入驻商订单信息
     * @param orderId 主单ID
     * @return
     */
    default List<TrdSupplierOrder> selectListByOrderId(Long orderId) {

        if(null == orderId){
            return new ArrayList<>();
        }

        return selectList(new LambdaQueryWrapperX<TrdSupplierOrder>()
                .eqIfPresent(TrdSupplierOrder::getOrderId, orderId));
    }

    /**
     * 根据主单单号 获取入驻商订单信息
     * @param orderNo
     * @return
     */
    default List<TrdSupplierOrder> selectListByOrderNo(String orderNo) {
        return selectList(new LambdaQueryWrapperX<TrdSupplierOrder>()
                .eqIfPresent(TrdSupplierOrder::getOrderNo, orderNo));
    }

    /**
     * 根据入驻商订单号 获取入驻商订单信息
     * @param supplierOrderNo
     * @return
     */
    default TrdSupplierOrder selectOrderBySupplierOrderNo(String supplierOrderNo){
        if(StringUtils.isEmpty(supplierOrderNo)){
            return null;
        }
        return selectOne(new LambdaQueryWrapper<TrdSupplierOrder>().eq(TrdSupplierOrder::getSupplierOrderNo,supplierOrderNo));
    }


    /**
     * 更新订单同步状态
     * @param supplierOrderNo
     * @param pushStatus
     */
    default void updateByPushStatus(String supplierOrderNo,Integer pushStatus){
         update(null,new LambdaUpdateWrapper<TrdSupplierOrder>().eq(TrdSupplierOrder::getSupplierOrderNo,supplierOrderNo)
                .set(TrdSupplierOrder::getPushStatus,pushStatus));//1代表已经推送订单到其他系统
    }

    /**
     * 根据订单查询入驻商订单支付金额信息
     * @param pageVo
     * @return
     */
    List<TrdSupplierResDto> getSupplierOrderPayInfo(TrdSupplierPageVO pageVo);

    /**
     * 根据入驻商订单号列表 获取入驻商订单信息
     * @param supplierOrderNos
     * @return
     */
    default List<TrdSupplierOrder> selectListBySupplierOrderNoList(List<String> supplierOrderNos){
        return selectList(new LambdaQueryWrapper<TrdSupplierOrder>().in(TrdSupplierOrder::getSupplierOrderNo,supplierOrderNos));
    }

    /**
     * 根据入驻商id 和 订单id查询 订单信息
     * @param supplierId
     * @param orderIds
     * @return
     */
    default List<TrdSupplierOrder> selectListBySupplierIdAndOrderIds(Long supplierId, List<Long> orderIds){
        return selectList(
                new LambdaQueryWrapper<TrdSupplierOrder>()
                        .eq(TrdSupplierOrder::getSupplierId,supplierId)
                        .in(TrdSupplierOrder::getOrderId,orderIds)
                        .in(TrdSupplierOrder::getPushStatus, OpenApiConstants.ORDER_SYNC_FLAG_1, OpenApiConstants.ORDER_SYNC_FLAG_2)

        );
    }


    /**
     * 根据供应商订单号查询供应商订单收款信息
     * @param reqVo
     * @return
     */
    List<OrderReceiptRespDTO> getOrderReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo);

    /**
     * 根据供应商订单号查询供应商订单收款信息(销售订单、差异出库、拒收)
     * @param reqVo
     * @return
     */
    List<OrderReceiptRespDTO> getOrderAfterReceiptInfoBySupplierOrderNo(SyncReceiptSendDTO reqVo);

    /**
     * 查询需要生成分账的O2O订单
     * @param paramVO 查询参数
     * @return 需要生成分账的订单列表
     */
    List<O2OGenerateSettleOrderDTO> selectO2OOrdersForDivide(O2OGenerateSettleParamVO paramVO);

    /**
     * 获取在途数量, 已下单没完成的
     * @param branchTransitQtyReqVO
     * @return
     */
    Long selectBranchTransitQty(BranchTransitQtyReqVO branchTransitQtyReqVO);

    List<SupplierOrderDtlInfoExportVO> selectLastOrderTime(@Param("branchIds") List<Long> branchIds);

    /**
     * 更新开票标记
     * @param supplierOrderNo
     * @param invoiceFlag
     */
    default void updateInvoiceFlagByNo(String supplierOrderNo,Integer invoiceFlag){
        update(null,new LambdaUpdateWrapper<TrdSupplierOrder>().eq(TrdSupplierOrder::getSupplierOrderNo,supplierOrderNo)
                .set(TrdSupplierOrder::getInvoiceFlag, invoiceFlag));//1代表已经推送订单到其他系统
    }

}
