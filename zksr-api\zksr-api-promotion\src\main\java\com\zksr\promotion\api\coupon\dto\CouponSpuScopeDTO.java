package com.zksr.promotion.api.coupon.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 具体使用范围
 * @date 2024/4/3 8:39
 */
@Data
public class CouponSpuScopeDTO {

    @ApiModelProperty("商品适用范围(数据字典);0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券）")
    private Integer spuScope;

    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer whiteOrBlack = NumberPool.INT_ONE;

    @ApiModelProperty("具体使用范围ID, 例如入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long applyId;

    @ApiModelProperty("具体使用范围名称, 例如入驻商名称")
    private String applyName;

    public CouponSpuScopeDTO() {
    }

    public CouponSpuScopeDTO(Long applyId, String applyName, Integer spuScope) {
        this.applyId = applyId;
        this.applyName = applyName;
        this.spuScope = spuScope;
    }

    public static CouponSpuScopeDTO build(SupplierDTO apply) {
        return new CouponSpuScopeDTO(apply.getSupplierId(), apply.getSupplierName(), CouponSpuScopeEnum.SUPPLIER.getScope());
    }

    public static CouponSpuScopeDTO build(BrandDTO apply) {
        return new CouponSpuScopeDTO(apply.getBrandId(), apply.getBrandName(), CouponSpuScopeEnum.BRAND.getScope());
    }

    public static CouponSpuScopeDTO build(SpuDTO apply) {
        return new CouponSpuScopeDTO(apply.getSpuId(), apply.getSpuName(), CouponSpuScopeEnum.SKU.getScope());
    }

    public static CouponSpuScopeDTO build(SkuDTO skuDTO) {
        return new CouponSpuScopeDTO(skuDTO.getSkuId(), skuDTO.getProperties(), CouponSpuScopeEnum.SKU.getScope());
    }

    public static CouponSpuScopeDTO build(CatgoryDTO catgoryDTO) {
        return new CouponSpuScopeDTO(catgoryDTO.getCatgoryId(), catgoryDTO.getCatgoryName(), CouponSpuScopeEnum.CLASS.getScope());
    }
}
