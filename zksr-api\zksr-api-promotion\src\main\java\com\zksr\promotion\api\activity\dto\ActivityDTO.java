package com.zksr.promotion.api.activity.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动
 * @date 2024/5/13 15:07
 */
@Data
@ApiModel(description = "促销活动")
@AllArgsConstructor
@NoArgsConstructor
public class ActivityDTO {

    /** 活动id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("促销活动ID")
    private Long activityId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("促销活动平台ID")
    private Long sysCode;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    @ApiModelProperty("1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）")
    private Integer funcScope;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("入驻商id")
    private Long supplierId;

    /** 促销类型（数据字典）;SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数=据字典")
    @ApiModelProperty("SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价")
    private String prmNo;

    /** 促销单号 */
    @Excel(name = "促销单号")
    @ApiModelProperty("促销单号")
    private String prmSheetNo;

    /** 活动名称 */
    @Excel(name = "活动名称")
    @ApiModelProperty("活动名称")
    private String activityName;

    /** 启用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "启用时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("启用时间")
    private Date effectTime;

    /** 启用人 */
    @Excel(name = "启用人")
    @ApiModelProperty("启用人")
    private String effectMan;

    /** 活动开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("活动结束时间")
    private Date endTime;

    /** 商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场")
    @ApiModelProperty("商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品）")
    private Integer spuScope;

    /** 是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  （指定渠道和指定门店二选一） */
    @Excel(name = "是否指定渠道参与;0-所有渠道参与 1-指定渠道参与  ", readConverterExp = "指=定渠道和指定门店二选一")
    @ApiModelProperty("是否指定渠道参与;0-所有渠道参与 1-指定渠道参与")
    private Integer chanelScopeAllFlag;

    /** 是否指定门店参与;0-所有门店参与 1-指定门店参与   （指定渠道和指定门店二选一） */
    @Excel(name = "是否指定门店参与;0-所有门店参与 1-指定门店参与   ", readConverterExp = "指=定渠道和指定门店二选一")
    @ApiModelProperty("是否指定门店参与;0-所有门店参与 1-指定门店参与")
    private Integer branchScopeAllFlag;

    /** 是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) 满赠:(1-满赠 0-每赠)*/
    @Excel(name = "是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) 满赠:(1-满赠 0-每赠)")
    @ApiModelProperty("是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) 满赠:(1-满赠 0-每赠)")
    private Integer ladderFlag;

    /** 参与活动次数限制规则（数据字典）;仅特价限购和满赠，0-每日一次 1-这个活动只能参加一次  2-仅活动期间内首单(作废) 3-系统首单*/
    @Excel(name = "参与活动次数限制规则", readConverterExp = "数=据字典")
    @ApiModelProperty("参与活动次数限制规则（数据字典）;仅特价限购和满赠，0-每日一次 1-这个活动只能参加一次  2-仅活动期间内首单(作废), 3-系统首单")
    private Integer timesRule;

    /** 活动优惠类型;仅满减，0-金额  1-数量 */
    @Excel(name = "活动优惠类型;仅满减，0-金额  1-数量")
    @ApiModelProperty("活动优惠类型;仅满减，0-金额  1-数量")
    private Integer amtOrQty;

    /** 促销状态;0-未启用 1-启用 2-停用 3-已失效（待定，过期或者无商品可参与促销） */
    @Excel(name = "促销状态;0-未启用 1-启用 2-停用 3-已失效", readConverterExp = "待=定，过期或者无商品可参与促销")
    @ApiModelProperty("促销状态;0-未启用 1-启用 2-停用 3-已失效")
    private Integer prmStatus;

    /**
     * {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty("参与促销活动商品单位,1-小单位,2-中单位,3-大单位")
    private Integer activityUnitType;

    /** 活动id集合 */
    @ApiModelProperty(value = "活动id集合")
    private List<Long> activityIds;

    /** 素材应用id */
    @ApiModelProperty(value = "素材应用ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialApplyId;

    /** 素材id */
    @Excel(name = "素材id")
    @ApiModelProperty(value = "素材id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialId;

    /** 素材图片地址 */
    @Excel(name = "素材图片地址")
    @ApiModelProperty(value = "素材图片地址")
    private String img;

    /** 活动说明 */
    @Excel(name = "活动说明")
    @ApiModelProperty(value = "活动说明")
    private String memo;

    /** 秒杀/特价, 限定参与SKU数 */
    @Excel(name = "秒杀/特价, 限定参与SKU数")
    @ApiModelProperty(value = "秒杀/特价, 限定参与SKU数")
    private Integer limitSkus;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActivityDTO that = (ActivityDTO) o;
        return Objects.equals(activityId, that.activityId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(activityId);
    }

    /**
     * 活动是否有效
     * @return false-无效, true-有效
     */
    public boolean validate() {
        // 活动还没开始
        if (this.startTime.getTime() > System.currentTimeMillis()) {
            return false;
        }
        // 活动已结束
        if (this.endTime.getTime() < System.currentTimeMillis()) {
            return false;
        }
        // 活动不是未启用
        if (this.prmStatus != NumberPool.INT_ONE) {
            return false;
        }
        return true;
    }

    public ActivityDTO(List<Long> activityIds) {
        this.activityIds = activityIds;
    }
}
