package com.zksr.account.model.transfer.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.business.TransferBusiType;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提交转账VO
 * @date 2024/3/11 14:07
 */
@Data
@Accessors(chain = true)
@ToString
public class TransferSubmitReqVO {

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 转账单号 */
    @ApiModelProperty(value = "转账单号")
    private String transferNo;

    /** 转账金额 */
    @ApiModelProperty(value = "转账金额")
    private BigDecimal transferAmt;

    /**
     * 业务类型
     * {@link TransferBusiType}
     */
    @ApiModelProperty(value = "业务类型(数据字典)")
    private String busiType;

    /** 业务单据id */
    @ApiModelProperty(value = "业务单据id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long busiId;

    /** 支付平台(数据字典) */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /** 转出方商户类型 */
    @ApiModelProperty(value = "转出方商户类型")
    private String sourceMerchantType;

    /** 转出方商户id */
    @ApiModelProperty(value = "转出方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sourceMerchantId;

    /** 转出方分账方商户编号 */
    @ApiModelProperty(value = "转出方分账方商户编号")
    private String sourceAltMchNo;

    /** 转出方分账方商户名 */
    @ApiModelProperty(value = "转出方分账方商户名")
    private String sourceAltMchName;

    /** 转入方商户类型 */
    @ApiModelProperty(value = "转入方商户类型")
    private String targetMerchantType;

    /** 转入方商户id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long targetMerchantId;

    /** 转入方分账方商户编号 */
    @ApiModelProperty(value = "转入方分账方商户编号")
    private String targetAltMchNo;

    /** 转入方分账方商户名 */
    @ApiModelProperty(value = "转入方分账方商户名")
    private String targetAltMchName;

    /** 转账说明 */
    @ApiModelProperty(value = "转账说明")
    private String transferRemark;

    /** 支付体系 0-储值 (入驻商充值), 1-收款 (订单支付平台) */
    @ApiModelProperty(value = "支付体系 0-储值 (入驻商充值), 1-收款 (订单支付平台)")
    private Integer usage;
}
