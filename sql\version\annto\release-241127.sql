-- spu 索引调整
ALTER TABLE `zksr_product`.`prdt_spu` ADD INDEX `idx_supplier_id`(`supplier_id`);

-- 菜单code编码优化, 使用自定义code作为子父级依据 新增字段
ALTER TABLE `zksr_cloud`.`sys_menu`
    ADD COLUMN `menu_code` varchar(32) NOT NULL COMMENT '菜单编号' AFTER `order_num`,
 ADD COLUMN `menu_pcode` varchar(32) NOT NULL COMMENT '菜单父级编号' AFTER `menu_code`;

-- 软件商信息表
CREATE TABLE `zksr_cloud`.`sys_software`(
`software_id` BIGINT(20) NOT NULL  COMMENT '软件商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` VARCHAR(64)   COMMENT '更新人' ,
`update_time` DATETIME(3)   COMMENT '更新时间' ,
`software_name` VARCHAR(32)   COMMENT '软件商名' ,
`contact_name` VARCHAR(32)   COMMENT '联系人' ,
`contact_phone` VARCHAR(16)   COMMENT '联系电话' ,
`contact_address` VARCHAR(255)   COMMENT '联系地址' ,
`software_user_id` BIGINT(20)   COMMENT '关联软件商管理员的账号id' ,
`software_rate` DECIMAL(7,6)   COMMENT '软件商分润比例' ,
PRIMARY KEY (software_id)
)  COMMENT = '软件商信息';

-- 平台商新增软件商ID和软件商分润比例
ALTER TABLE `zksr_cloud`.`sys_partner`
    ADD COLUMN `software_id` bigint(20) NOT NULL COMMENT '软件商id' AFTER `sid`;

ALTER TABLE `zksr_cloud`.`sys_partner`
    ADD COLUMN `software_rate` decimal(7, 6) NOT NULL default 0 COMMENT '软件商分润比例' AFTER `sid`;

-- 管理类别新增软件商分润比例
ALTER TABLE `zksr_product`.`prdt_catgory`
    ADD COLUMN `software_rate` decimal(7,6) DEFAULT NULL default 0 COMMENT '软件商分润比例 百分比的小数表现形式，1%表示为0.01';


ALTER TABLE `zksr_member`.`mem_branch`
    ADD COLUMN `first_order_flag` tinyint(1) NULL COMMENT '首单标识 1-是 0-否' ;
-- 增加拉链表  zhengsenbing 2024-11-22
CREATE TABLE `zksr_member`.`mem_colonel_branch_zip`(
`colonel_branch_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '门店业务员关系拉链表ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`branch_id` BIGINT(20) NOT NULL  COMMENT '门店id' ,
`colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`colonel_branch_zip_id`)
)  COMMENT = '门店业务员关系拉链表';

CREATE TABLE `zksr_member`.`mem_colonel_hierarchy_zip`(
`colonel_hierarchy_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '业务员上下级关系拉链表ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`pcolonel_id` BIGINT(20) NOT NULL  COMMENT '上级业务员id' ,
`colonel_id` BIGINT(20) NOT NULL  COMMENT '业务员id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`colonel_hierarchy_zip_id`)
)  COMMENT = '业务员上下级关系拉链表';

CREATE TABLE `zksr_cloud`.`sys_area_supplier_zip`(
`area_supplier_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '区域城市入驻商关系拉链表ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`area_id` VARCHAR(255) NOT NULL  COMMENT '区域城市id' ,
`supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`area_supplier_zip_id`)
)  COMMENT = '区域城市入驻商关系拉链表';


CREATE TABLE `zksr_cloud`.`sys_dc_area_zip`(
`dc_area_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '运营商区域城市拉链表ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`dc_id` BIGINT(20) NOT NULL  COMMENT '运营商id' ,
`area_id` BIGINT(20) NOT NULL  COMMENT '区域城市id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`dc_area_zip_id`)
)  COMMENT = '运营商区域城市拉链表';

CREATE TABLE `zksr_product`.`prdt_area_item_zip`(
`area_item_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '城市上架商品拉链表ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
`spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
`sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
`area_id` BIGINT(20) NOT NULL  COMMENT '城市id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`area_item_zip_id`)
)  COMMENT = '城市上架商品拉链表';


CREATE TABLE `zksr_product`.`prdt_supplier_item_zip`(
`supplier_item_zip_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '全国上架商品拉链表ID',
`sys_code` BIGINT(20) NOT NULL  COMMENT '平台商id' ,
`create_by` VARCHAR(64)   COMMENT '创建人' ,
`create_time` DATETIME(3)   COMMENT '创建时间' ,
`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
`supplier_id` BIGINT(20) NOT NULL  COMMENT '入驻商id' ,
`spu_id` BIGINT(20) NOT NULL  COMMENT '商品SPU id' ,
`sku_id` BIGINT(20) NOT NULL  COMMENT '商品sku id' ,
`start_date` DATETIME NOT NULL  COMMENT '开始日期' ,
`end_date` DATETIME NOT NULL  COMMENT '结束日期',
PRIMARY KEY (`supplier_item_zip_id`)
)  COMMENT = '全国上架商品拉链表';


INSERT INTO `zksr_cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('满赠限购类型', 'fg_activity_times_rule', '0', 'zksr', '2024-11-25 09:49:14', 'zksr', '2024-11-25 09:50:09', '满赠限购类型');
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '系统首单', '3', 'fg_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:40', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '仅一次', '1', 'fg_activity_times_rule', NULL, 'default', 'N', '0', 'zksr', '2024-11-25 09:49:33', '', NULL, NULL);



-- trd_supplier_order_settle 订单结算表新增字段
ALTER TABLE `zksr_trade`.`trd_supplier_order_settle`
    ADD COLUMN `software_rate` decimal(7,6) NULL COMMENT '软件商分润比例' AFTER `software_amt`;

-- trd_supplier_after_settle 售后订单结算表新增字段
ALTER TABLE `zksr_trade`.`trd_supplier_after_settle`
    ADD COLUMN `software_rate` decimal(7,6) NULL COMMENT '软件商分润比例 从订单 平台商分润比例，百分比的小数表现形式，1%表示为0.01' AFTER `software_amt`;

-- 提现流水调整
ALTER TABLE `zksr_account`.`acc_transfer_flow` MODIFY COLUMN `source_merchant_id` bigint(20) NULL DEFAULT NULL COMMENT '转出方商户id' AFTER `source_merchant_type`;


INSERT INTO `zksr_cloud`.`sys_software` (`software_id`, `create_by`, `create_time`, `update_by`, `update_time`, `software_name`, `contact_name`, `contact_phone`, `contact_address`, `software_user_id`, `software_rate`) VALUES (553204835870867456, 'zksr', '2024-11-26 18:37:18.271', 'zksr', '2024-11-26 18:37:18.273', '中科商软-软件商管理员', '中科商软', '***********', '暂无地址', 11231, 0.000000);


INSERT INTO `zksr_cloud`.`sys_user` (`user_id`, `sys_code`, `dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `dc_id`, `supplier_id`, `colonel_id`, `area_id`, `brand_id`) VALUES (11231, NULL, NULL, '***********', '【中科商软-软件商管理员】中科商软', '00', '', '***********', '0', '', '$2a$10$M9Wxs2dxLTkINXG17BJoceYrxC9MtMNNBh4dLfdFf2eFZP6SCHcZq', '0', '0', '', NULL, 'zksr', '2024-11-26 18:37:18', '', NULL, '中科商软-软件商管理员默认软件商管理员用户', NULL, NULL, NULL, NULL, NULL);


INSERT INTO `zksr_cloud`.`sys_user_role` (`user_id`, `role_id`, `sys_code`) VALUES (11231, 2, NULL);


update `zksr_cloud`.`sys_partner` set software_id = 553204835870867456;
