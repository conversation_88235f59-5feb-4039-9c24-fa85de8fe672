package com.zksr.product.api.brand.excel;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于平台品牌信息导入
 * @date 2024/10/28 09:18
 */
@Data
@ApiModel(description = "用于平台品牌信息导入")
public class BrandImportExcel {
    private static final long serialVersionUID = 1L;

    /** 平台商品牌id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandId;

    /**  商品品牌名称 */
    @Excel(name = " 商品品牌名称")
    private String brandName;

    /** 0 正常, 1 停用 */
    @Excel(name = "0 正常, 1 停用")
    private String status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;
}
