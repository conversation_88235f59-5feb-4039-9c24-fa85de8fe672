package com.zksr.product.api.sku;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.vo.openapi.IncreaseUpdateStockDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.dto.SkuPricesRespDTO;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.sku.vo.SkuPricesPageReqVO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;


@FeignClient(
        contextId = "remoteSkuApi",
        value = ApiConstants.NAME
)
/**
 *
 *  入驻商服务
 * <AUTHOR>
 * @date 2024/3/16 11:29
 */
public interface SkuApi {

    String PREFIX = ApiConstants.PREFIX + "/sku";

    @GetMapping(PREFIX + "/getBySkuId")
    public CommonResult<SkuDTO> getBySkuId(@RequestParam("skuId") Long skuId);


    /**
     * 修改商品SKU
     */
    @PostMapping(PREFIX + "/updateBySku")
    public void updateBySku(@RequestBody SkuDTO skuDTO);


    /**
     * 根据skuID 修改库存并更新缓存
     */
    @PostMapping(PREFIX + "/editInventory")
    CommonResult<Boolean> editInventory(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                        @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                        @RequestBody PrdInventoryVO prdInventoryVO);

    /**
     * 触发更新SKU已售数据
     */
    @PostMapping(PREFIX + "/updateSaleQtyEvent")
    CommonResult<Boolean> updateSaleQtyEvent(@RequestBody List<Long> skuIds);

    /**
     * 根据外部编码和入驻商Id查询最后库存更新时间
     */
    @GetMapping(PREFIX + "/getLastUpdateTime")
    CommonResult<Date> getLastUpdateTime(@RequestParam("sourceNo") String sourceNo, @RequestParam("supplierId") Long supplierId);

    /**
     * 获取sku销售分润占比
     */
    @PostMapping(PREFIX + "/getSkuSaleTotalRate")
    CommonResult<PrdtSkuSaleTotalRateVO> getSkuSaleTotalRate(@RequestBody PrdtSkuSaleTotalRateReqVO reqVO);

    /**
     * 获取sku商品价格信息列表
     */
    @PostMapping(PREFIX + "/getSkuPricesList")
    CommonResult<List<SkuPricesRespDTO>> getSkuPricesList(@RequestBody SkuPricesPageReqVO reqVO);

    @PostMapping(PREFIX + "/listBySkuIds")
    CommonResult<Map<Long, SkuDTO>> listBySkuIds(@RequestBody List<Long> skuIds);

    /**
     * 查询SKU关联的有效的, 组合商品ID
     */
    @PostMapping(PREFIX + "/getRelationSpuCombineList")
    CommonResult<List<Long>> getRelationSpuCombineList(@RequestBody List<Long> skuIds);

    @PostMapping(PREFIX + "/getAreaIdExistSku")
    CommonResult<Boolean> getAreaIdExistSku(@RequestParam("areaId") Long areaId, @RequestParam("sysCode") Long sysCode);

    @PostMapping(PREFIX + "/getExistShelfSku")
    CommonResult<Boolean> getExistShelfSku(@RequestParam("spuId") Long spuId);


    @PostMapping(PREFIX + "/increaseUpdateStock")
    CommonResult<Boolean> increaseUpdateStock(@RequestBody IncreaseUpdateStockDTO dto);
}
