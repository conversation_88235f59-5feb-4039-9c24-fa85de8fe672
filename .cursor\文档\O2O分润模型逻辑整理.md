平台上 = O2O分润模型逻辑整理


基础公式

消费者支付金额 - 入驻商分成 = 门店分成 + 运营商分成 + 平台商分成 + 软件商分成

5.2.1 商品零售分润模式（按金额）

分润金额 = SKU表的（销售模式[零售]）的分润金额

5.2.1.1 入驻商分成计算

入驻商分成 = ∑(商品零售价 - 分润金额) * 数量

5.2.1.2 门店分润模式 = 全部

门店分成 = ∑分润金额 * 数量

5.2.1.3 门店分润模式（按比例）→ 暂不实现

分配逻辑：门店先分，剩余部分由运营商/平台商/软件商按比例分配

5.2.1.3.1 门店分成

=∑分润金额  门店分润比例  数量

5.2.1.3.2 运营商分成

=∑(分润金额 - 分润金额门店分润比例)  运营商O2O分润比例 * 数量

5.2.1.3.3 平台商分成

=∑分润金额  (1-门店分润比例)  平台商O2O分润比例 * 数量

5.2.1.3.4 软件商分成

=∑分润金额  (1-门店分润比例)  软件商O2O分润比例 * 数量

5.2.1.4 门店分润模式（不分润）

入驻商分成 = 订单金额