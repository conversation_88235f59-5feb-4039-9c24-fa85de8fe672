### 业务层级理解：
    🍦 冰淇淋店分钱游戏
    角色扮演
    想象我们开了一个冰淇淋店，有这些小伙伴一起合作：
    小明 👦：买冰淇淋的小朋友（消费者）
    冰淇淋工厂 🏭：做冰淇淋的人（入驻商）
    小卖部 🏪：卖冰淇淋的商店（门店）
    发传单的姐姐 📢：告诉小朋友哪里有冰淇淋卖（运营商）
    游乐场 🎡：让所有小朋友来玩的地方（平台商）
    做收银机的叔叔 💻：发明收钱机器的人（软件商）


### 关系网
    平台
        运营
            门店A 
                入驻商A
                    商品A  
                        订单A
            门店B
                入驻商B
                    商品B  
                        订单B
                
        

### 平台（类似大超市）
    基础管理 > 平台商 sys_partner（SysPartner）
        基本信息 ： 名称 +联系人 + 电话 + 城市数量 + 运营商数量 + 入驻商数量 + 租户 + 联系地址
        O2O配置 ： 开启零售、开启O2O
        支付配置 ： 
            小程序：小程序appid 、AppSecret  、 公众号appid 、 公众号AppSecret、小程序接口地址、商品展示类型
            支付配置: 利润算法 、初始扣点 、软件商分润比例 、入驻商分润充值平台（美的付 、 合利宝 、 微信） 、 在线支付 、 支付平台 、 储值支付 、 储值平台（合利宝）、统一分账
            合利宝支付配置: 公钥 / 私钥 证书+ 密码 + 支付回调地址 、 合利宝支付商户号 、 合利宝平台商户号 、MD5签名秘钥 、 Des3加密key
        
        分账配置
            平台商户信息 ： 商户号 、进件单号 商户全称
            软件商户信息 :  商户号 、进件单号 商户全称
            平台业务员统一分润商户 :  商户号 、进件单号 商户全称

        打印配置
            （芯烨打印、 飞鹅打印） + （设备编码 + 打印联数）

        app配置 : 平台 、版本号 、APK文件 、 更新内容

        其他配置 ：
            短信配置： 短信平台（腾讯 、 阿里） 、 accessKeyId 、accessKeySecret
            快递查询配置 ：快递查询平台（快递鸟）、商户ID 、 appKey

    财务管理 > 平台商账户 acc_account（AccAccount）
        基础：平台编号、平台名称、 提现金额
        提现：提现日期  提现金额  手续费  预计到账金额  提现单号  到账时间  到账卡号  到账开户名称  到账商户号  驳回原因  失败原因  提现结果
        结算：订单日期  分润金额  订单号  售后单号  商品编号   商品名称   商品金额  实际结算金额  分润比例%  入驻商名称  结算状态
        
    财务管理 > 平台商申请审核 acc_withdraw（AccWithdraw）
    
    基础管理 > 平台基础设置 sys_config（SysConfig）
        小程序配置：小程序名称、 平台商logo 、 商城二级类目展示 、 无库存商品是否隐藏、用户协议 、 提现设置：（提现费率、最小提现金额、门店提现费率）
        平台基础配置：微信商家助手认证、强制微信b端商家助手认证、是否开启设备验证、平台本地商品售后类型、本地商品售后审核类型、是否展示佣金、游客模式归属区域城市、平台初始密码:

    基础管理 > 平台管理类别 sys_dict_type（SysDictType）+ sys_dict_data（SysDictData）
        基础：左边是树型结构 ，右边是对应类别：类别编号、类别名称（可口可乐）、 上级类别（啤酒）、 状态 、 类别扣点
        一级类别特有： 商品类别扣点 、 软件商分润比例 、 平台分润比例
    
    基础管理 > 平台商城市分组 sys_group（SysGroup）

    商城装修 > 平台模版 visual_setting_template（VisualSettingTemplate）+ visual_setting_master（VisualSettingMaster）
        基础：模版名称 + 自定义设置拖拽组件

    商城管理 > 平台商展示分类 sys_dict_type（SysDictType）+ sys_dict_data（SysDictData）
         基础：左边是树型结构 ，右边是对应类别：类别编号、类别名称（可口可乐）、 上级类别（啤酒）、 状态 、 平台城市分组 + GIF图片

    平台促销管理 > 优惠劵领取记录 prm_coupon_log（PrmCouponLog）
    平台促销管理 > 优惠劵列表 prm_coupon（PrmCoupon）
    平台促销管理 > 买赠 prm_bg_rule（PrmBgRule）
    平台促销管理 > 满赠 prm_fg_rule（PrmFgRule）
    平台促销管理 > 满减 prm_fd_rule（PrmFdRule）
    平台促销管理 > 秒杀 prm_sk_rule（PrmSkRule）
    平台促销管理 > 特价限购 prm_sp_rule（PrmSpRule）
    平台促销管理 > 发券额度管理 prm_coupon_colonel_quota（PrmCouponColonelQuota）
    平台促销管理 > 批次发券管理 prm_coupon_batch（PrmCouponBatch）
    平台促销管理 > 组合促销 prm_cb_rule（PrmCbRule）
    系统管理 > 平台数据字典 sys_dict_type（SysDictType）+ sys_dict_data（SysDictData）

### 入驻商（供货商）

    财务管理 > 入驻商账户 acc_account（AccAccount）
        基础：（ 名称 + 余额 + 可用余额 + 冻结余额 + 授信额度[平台先让商家"借钱"做生意，以后再还]）

    基础管理 > 入驻商信息 sys_supplier（SysSupplier）
        基础：（名称 + 联系人 + 联系电话 + 地址 + 状态+ 电子围栏[防止商家"越界经营"（比如本地商家偷偷做全国生意 +只服务能覆盖的区域 + 起送费 + 经营信息）
        分账： (美的付 、 合利宝 、 微信) + (分账商户号 + 秘钥 + 支付进件号 + 保证金 )
        打印：（芯烨打印、 飞鹅打印） + （设备编码 + 打印联数）
        售后：（地址 + 电话）
        开放：（key + openSecret + 告警邮箱 + 邮箱订阅 +ERP接口设置[ publicKey + privateKey + 接口配置 + 对接系统唯一编码 + 订单自动推送 + 是否同步标准/供销价格 +是否开启货到付款清账] ）
              入驻商和ERP关系 - 小卖部老板和智能记账本的关系！ERP是商家的"超级管家帮手 
                1、订单自动处理:打印+扣库存+通知仓库发货
                2、库存自动同步:库存自动同步到商家ERP系统
                3、财务对账
                4、数据报告
        其他：（赠品取价算法 、商品配送标签 、 打烊时间  、 商城公告 、 补单机制  、 允许储值支付）
        入驻商首页数据

    基础管理 > 入驻商管理分类 （暂无）
        
    报表管理 > 入驻商销售汇总 dws_trd_supplier_sales_month（DwsTrdSupplierSalesMonth）

### 运营商(与城市相关)

    财务管理 > 运营商账户 acc_account（AccAccount）
        基础：运营商编号、运营商名称、 提现金额 当日冻结资金 操作：提现

    财务管理 > 运营商申请审核 acc_withdraw（AccWithdraw）
        基础设置：运营商名称   申请订单号  提现金额   手续费金额   税率(%)    状态   转账状态  打款状态  商户号   失败原因

    基础管理 > 运营商基础设置 sys_config（SysConfig）
        基础设置： 门店自动审核开关 、 用户自动审核开关 、 门店货到付款最大默认可欠款金额 、 一级分类展示、
        订单参数设置： 未支付订单失效时间、收货后订单完成时间、全国发货后自动确认收货时间、未发货异常超时时间、是否显示可售后时间
        业务员APP配置：业务员拓店是否自动审核

    基础管理 > 运营商管理 sys_partner（SysPartner）
        基础设置：运营商编号  公司名称   联系人   运营商名称   状态   联系电话   【运营区域   本地起送费   全国起送费】  联系地址   备注
        分账设置：提现商户号  进件单号   商户全称   商户KEY   门店储值软件商分佣比例   门店储值平台商分佣比例

    商城管理 > 运营商展示分类 (areaClass) sys_dict_type（SysDictType）+ sys_dict_data（SysDictData）
        基础设置：左边是树【先选择 城市】，树：相应的类别
        详细设置：城市 类别名称  上级类别  电子围栏分类 渠道类型 展示生产日期、生产日期格式 状态、排序 、 gif图


    运营促销管理 > 优惠券领取记录 prm_coupon_log（PrmCouponLog）
    运营促销管理 > 优惠券列表 prm_coupon（PrmCoupon）
    运营促销管理 > 买赠 prm_bg_rule（PrmBgRule）
    运营促销管理 > 满赠 prm_fg_rule（PrmFgRule）
    运营促销管理 > 满减 prm_fd_rule（PrmFdRule）
    运营促销管理 > 秒杀 prm_sk_rule（PrmSkRule）
    运营促销管理 > 特价限购 prm_sp_rule（PrmSpRule）
    运营促销管理 > 优惠劵报表 prm_coupon_log（PrmCouponLog）
    运营促销管理 > 促销报表 prm_activity（PrmActivity）
    运营促销管理 > 批次发券管理 prm_coupon_batch（PrmCouponBatch）
    运营促销管理 > 组合促销 prm_cb_rule（PrmCbRule）


### 软件商（哪个平台下的那个软件）
    财务管理 > 软件商账户 acc_account（AccAccount）
        基础设置：软件商编号  软件商名称  平台编码  平台名称  可提现金额
        
    基础管理 > 软件商管理 sys_software（SysSoftware）
        基础设置：ss软件商编号  软件商名称 默认分润比例(%)  联系人  联系电话  联系地址 软件商账号



### 门店

    门店管理 > 门店信息 mem_branch（MemBranch）
        基础设置：  门店名称（客户名称） 门店编码（客户编码） 渠道类型  全国分组 状态 城市区域   价格码（配送价1、2等） 业务员  省市区 联系人 联系电话 联系地址 
        O2O模式(平台开启O2O才展示)：  分销渠道【美的零售店】、分销模式：【O2O】、分润模式：【全部（先做全部）/按比例/按平台规则】、分润比例 、 商户号、商户号进件单号、商户号全称
        订单明细： 订单编号  下单时间 入驻商订单编号 外部订单号  单据类型  状态  异常说明  产品名称 单位  单价 要货数量 单价  要货数量  金额  合计金额  优惠金额  实际金额  订单状态  打印状态  付款方式  支付状态  业务员经理  门店名称 门店地址 同步标识
        状态变更记录：注册日志 、 下单日志

    门店管理 > 门店注册信息 mem_branch_register（MemBranchRegister）
        基础设置：门店名称、联系人、业务员、渠道、城市、地址、备注
        操作： 审核、作废

    门店管理 > 门店用户管理 mem_branch_user（MemBranchUser）
        基础设置：用户账号、用户名称、【管理门店 - 可多个】、备注
        操作：停用 、修改密码

    门店管理 > 用户注册信息 mem_member_register（MemMemberRegister）
        基础设置： 用户名    门店名称    渠道类型  城市  联系人  手机号  地址  经度  纬度  烟  审核状态  审核人  审核时间
        操作：审核、作废

    财务管理 > 门店充值 acc_recharge（AccRecharge）
    财务管理 > 门店提现申请 acc_withdraw（AccWithdraw）
    基础管理 > 门店储值套餐 acc_recharge_scheme（AccRechargeScheme）
   
    门店管理 > 经营屏蔽 mem_branch_lifecycle_zip（MemBranchLifecycleZip）
    系统设置 > 门店生命周期配置 mem_branch_lifecycle_zip（MemBranchLifecycleZip）
    报表管理 > 门店销售汇总 dws_trd_branch_sales_month（DwsTrdBranchSalesMonth）
    财务管理 > PC门店储值 acc_account（AccAccount）


### 用户

    系统管理 > 用户管理 sys_user（SysUser）
        基础设置：用户昵称 手机号码 用户性别 角色 备注
        操作：分配角色

    系统监控 > 在线用户 sys_logininfor（SysLogininfor）
