package com.zksr.account.api.account.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户
 * @date 2024/3/23 11:15
 */
@Data
@ApiModel(description = "进件账户数据传递")
public class AccPlatformAccountDTO {

    @ApiModelProperty("第三方支付平台进件编号")
    private String altMchNo;

    @ApiModelProperty("第三方支付平台进件名称")
    private String altMchName;

    public AccPlatformAccountDTO() {
    }

    public AccPlatformAccountDTO(String altMchNo, String altMchName) {
        this.altMchNo = altMchNo;
        this.altMchName = altMchName;
    }
}
