package com.zksr.member.api.command.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("操作指令 - 指令数据查询返回 CommandRespDTO")
public class CommandAddOrderRespDTO {
    @ApiModelProperty(value = "加单指令锚点ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long commandId;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店联系人名称")
    private String branchContactName;

    @ApiModelProperty(value = "门店联系电话")
    private String branchContactPhone;

    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    @ApiModelProperty(value = "门店图片")
    private String branchImg;

    @ApiModelProperty(value = "锚点指令状态；0：待加单；1：加单中；2：加单完成")
    private Integer state;

    @ApiModelProperty(value = "普通指令返回数据集合")
    private List<CommandAddOrderExecDTO> commandAddOrderExecDTOList;
}
