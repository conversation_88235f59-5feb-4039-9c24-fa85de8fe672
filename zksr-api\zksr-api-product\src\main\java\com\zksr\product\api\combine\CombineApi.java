package com.zksr.product.api.combine;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.combine.dto.SpuCombineDTO;
import com.zksr.product.api.combine.vo.SpuCombineSaveReqVO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/2 18:12
 * @组合促销商品rpc服务
 */
@FeignClient(
        contextId = "remoteCombineApi",
        value = ApiConstants.NAME
)
public interface CombineApi {

    String PREFIX = ApiConstants.PREFIX + "/combine";

    /**
     * 新增组合商品
     *
     * @param spuCombineSaveReqVO
     */
    @PostMapping(PREFIX + "/addSpuCombine")
    CommonResult<Long> addSpuCombine(@RequestBody SpuCombineSaveReqVO spuCombineSaveReqVO);

    /**
     * 新增组合商品
     *
     * @param spuCombineSaveReqVO
     */
    @PostMapping(PREFIX + "/editSpuCombine")
    CommonResult<Long> editSpuCombine(@RequestBody SpuCombineSaveReqVO spuCombineSaveReqVO);


    /**
     * 获取组合商品
     *
     * @param spuCombineId
     */
    @GetMapping(PREFIX + "/getSpuCombine")
    CommonResult<SpuCombineDTO> getSpuCombine(@RequestParam("spuCombineId")Long spuCombineId);


    /**
     * 停用启用组合商品
     *
     * @param spuCombineId
     */
    @GetMapping(PREFIX + "/editSpuCombineStatus")
    CommonResult<Boolean> editSpuCombineStatus(@RequestParam("spuCombineId") Long spuCombineId,@RequestParam("status") Integer status);
}

