package com.zksr.member.api.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户信息对象 mem_member
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Data
@ApiModel("用户信息 - mem_member分页 Request VO")
public class MemMemberUpdatePasswordReqVO {
    private static final long serialVersionUID = 1L;

    /** 用户id */
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    @ApiModelProperty(value = "sysCode",  example = "11")
    private Long sysCode;

    /** 用户手机号 */
    @ApiModelProperty(value = "用户手机号",  example = "13412344321")
    @Length(min=1, max=16, message = "长度最大16")
    private String memberPhone;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 用户密码 */
    @ApiModelProperty(value = "用户密码")
    private String password;

}
