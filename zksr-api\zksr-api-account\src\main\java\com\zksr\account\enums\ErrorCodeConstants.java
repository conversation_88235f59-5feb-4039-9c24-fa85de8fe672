package com.zksr.account.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc
 */
public interface ErrorCodeConstants {
    // ========== acoount 模块 1-011-001-000 ==========

    // ========== pay 模块 1-011-001-000 ==========
    ErrorCode ORDER_TYPE_NOT_EXIST = new ErrorCode(1_011_001_001, "登录账号已存在");
    ErrorCode ORDER_TYPE_PROCESS_NOT_EXIST = new ErrorCode(1_011_001_002, "订单类型不支持");
    ErrorCode ORDER_NOT_EXIST = new ErrorCode(1_011_001_003, "订单不存在");
    ErrorCode ORDER_REPAT_PAY = new ErrorCode(1_011_001_004, "订单已支付");
    ErrorCode PAY_CLIENT_NOT_EXIST = new ErrorCode(1_011_001_005, "支付渠道不存在");
    ErrorCode PAY_FLOW_NOT_EXIST = new ErrorCode(1_011_001_006, "支付流水不存在");
    ErrorCode REFUND_FLOW_EXIST = new ErrorCode(1_011_001_007, "退款已经发起");
    ErrorCode WALLET_NONSUPPORT = new ErrorCode(1_011_001_008, "余额支付不支持");
    ErrorCode TRANSFER_NOT_EXIST = new ErrorCode(1_011_001_009, "交易方式不存在");
    ErrorCode MIN_AMT = new ErrorCode(1_011_001_010, "最低结算5元");
    ErrorCode NONSUPPORT_ORDER_TYPE = new ErrorCode(1_011_001_011, "不支持订单类型");
    ErrorCode PARAM_CANNOT_NULL = new ErrorCode(1_011_001_012, "参数不能为空");
    ErrorCode NET_WORK_ERR01 = new ErrorCode(1_011_001_013, "网络异常");
    ErrorCode REFUND_AMT_INSUFFICIENT = new ErrorCode(1_011_001_014, "退款金额不得大于支付金额");
    ErrorCode USER_APPID_NOT_EXIST = new ErrorCode(1_011_001_015, "平台商用户小程序appid配置不存在");
    ErrorCode PAY_FLOW_HAS_PROCESS = new ErrorCode(1_011_001_016, "支付流水已经处理");
    ErrorCode GLOBAL_PAY_CONFIG_NOT_EXIST = new ErrorCode(1_011_001_017, "全局支付配置global_pay_config不存在");
    ErrorCode HDFK_PAYMENT_ORDER_NOT_EXIST = new ErrorCode(1_011_001_018, "货到付款支付入驻商订单列表必传");
    ErrorCode FREE_GT_PAY_AMT = new ErrorCode(1_011_001_019, "支付金额必须大于手续费");
    ErrorCode PAY_PLATFORM_NOT_EXIST = new ErrorCode(1_011_001_020, "支付平台没有配置，平台编码[{}]");
    ErrorCode PAY_MIDEA_PAY_FAIL = new ErrorCode(1_011_001_021, "美的付微信支付失败，返回[{}]");
    ErrorCode PAY_MIDEA_PAY_REFUND_FAIL = new ErrorCode(1_011_001_022, "美的付退款失败，返回[{}]");
    ErrorCode PAY_MIDEA_PAY_WITHDRAW_FAIL = new ErrorCode(1_011_001_023, "美的付提现失败，返回[{}]");
    ErrorCode PAY_MIDEA_PAY_TRANSFER_FAIL = new ErrorCode(1_011_001_024, "美的付转账失败，返回[{}]");
    ErrorCode PAY_MIDEA_PAY_QUERY_FAIL = new ErrorCode(1_011_001_025, "美的付余额查询失败，返回[{}]");
    ErrorCode PAY_MIDEA_PAY_SIGN_FAIL = new ErrorCode(1_011_001_026, "美的付签名失败，返回[{}]");
    ErrorCode SUPPLIER_APPID_NOT_EXIST = new ErrorCode(1_011_001_027, "软件商入驻商小程序appid配置不存在");
    ErrorCode PAY_ORDER_PRE_PAY_SUCCESS = new ErrorCode(1_011_001_028, "订单已支付, 请稍后查询最新状态");
    ErrorCode REFUND_FLOW_PROCESSING = new ErrorCode(1_011_001_029, "退款处理中, 请勿重复发起");
    ErrorCode REFUND_FLOW_FINISH = new ErrorCode(1_011_001_030, "退款已完成, 请勿重复发起");
    ErrorCode ORDER_PAY_BEFORE_VALIDATE_ERR = new ErrorCode(1_011_001_031, "订单验证异常 {}");
    ErrorCode ORDER_REFUND_BEFORE_VALIDATE_ERR = new ErrorCode(1_011_001_032, "退款验证异常 {}");
    ErrorCode ORDER_REFUND_SPLIT_AMT_ERR = new ErrorCode(1_011_001_033, "订单商户退款金额异常 {}");
    ErrorCode ORDER_REFUND_SETTLE_NONE = new ErrorCode(1_011_001_034, "退款结算不能为空 {}");
    ErrorCode MIDEA_PAY_ATTACH_NULL = new ErrorCode(1_011_001_035, "美的付回调Attach信息为空");
    ErrorCode PAY_MIDEA_PAY_SIGN_SUCCESS = new ErrorCode(1_011_001_36, "美的付签名成功，返回[{}]");
    ErrorCode PAY_MIDEA_PAY_SUCCESS = new ErrorCode(1_011_001_037, "美的付微信支付成功，返回[{}]");


    // ========== recharge 充值 模块 1-011-002-000 ==========
    ErrorCode NOT_EXIST_RECHARGE = new ErrorCode(1_011_002_001, "充值单不存在");
    ErrorCode NON_TENANT_SUPPLIER = new ErrorCode(1_011_002_002, "非入驻商租户");
    ErrorCode NON_RECHARGE_TYPE = new ErrorCode(1_011_002_003, "未知充值类型");
    ErrorCode NON_RECHARGE_SUPPLIER = new ErrorCode(1_011_002_004, "未知充值入驻商");
    ErrorCode RECHARGE_REPEAT = new ErrorCode(1_011_002_005, "充值重复支付");
    ErrorCode ACCOUNT_FLOW_CAS_ERR = new ErrorCode(1_011_002_006, "账户流水CAS变更失败");
    ErrorCode ACCOUNT_CAS_ERR = new ErrorCode(1_011_002_007, "账户CAS变更失败");
    ErrorCode NOT_EXIST_MERCHANT_CONFIG = new ErrorCode(1_011_002_008, "平台商户配置不存在");
    ErrorCode NOT_EXIST_MERCHANT = new ErrorCode(1_011_002_009, "商户不存在");
    ErrorCode NOT_EXIST_SUPPLIER_MERCHANT = new ErrorCode(1_011_002_010, "入驻商进件商户不存在");
    ErrorCode NOT_EXIST_MERCHANT_ACCOUNT_CONFIG = new ErrorCode(1_011_002_011, "平台商户账户不存在");
    ErrorCode NOT_EXIST_PAY_CONFIG = new ErrorCode(1_011_002_012, "平台支付配置不存在");
    ErrorCode STORE_AREA_NOT_DC = new ErrorCode(1_011_002_013, "门店所属区域没有配置主运营商");
    ErrorCode RECHARGE_INVALID = new ErrorCode(1_011_002_014, "充值活动已过期无法操作");

    // ========== account 模块 1-011-003-000 ==========
    ErrorCode NOT_EXIST_ACCOUNT = new ErrorCode(1_011_003_001, "账户不存在");
    ErrorCode BALANCE_ERR = new ErrorCode(1_011_003_002, "余额不足");
    ErrorCode SAVE_PAY_PLATFORM_ERR = new ErrorCode(1_011_003_003, "保存平台商户数据异常");
    ErrorCode GET_PAY_PLATFORM_ERR = new ErrorCode(1_011_003_005, "获取平台商户数据异常");
    ErrorCode MERCHANT_TYPE_ERR = new ErrorCode(1_011_003_006, "商户类型不一致");
    ErrorCode THE_SALESMAN_NAME_DOES_NOT_EXIST = new ErrorCode(1_011_003_007, "业务员名称不存在");
    ErrorCode BRANCH_ACCOUNT_NOT_EXIST = new ErrorCode(1_011_003_008, "门店余额不足");

    // ========== transfer 模块 1-011-004-000 ==========
    ErrorCode SETTLE_ALREADY_CREATE = new ErrorCode(1_011_004_001, "结算交易转账单重复创建");
    ErrorCode SOURCE_ACCOUNT_NONE_EXISTS = new ErrorCode(1_011_004_002, "转出方账户不存在");
    ErrorCode TRANSFER_UPDATE_SETTLE = new ErrorCode(1_011_004_003, "更新订单结算失败");
    ErrorCode TRANSFER_REPEAT = new ErrorCode(1_011_005_004, "同一个交易请勿重复转账");

    // ========== withdraw 模块 1-011-005-000 ==========
    ErrorCode WITHDRAW_MERCHANT_ERR = new ErrorCode(1_011_005_001, "提现商户账户平台与平台储值支付平台");
    ErrorCode WITHDRAW_BSI_MERCHANT_ERR = new ErrorCode(1_011_005_002, "提现出账商编不能为空(收款商编)");
    ErrorCode WITHDRAW_MERCHANT_NONE_EXIST = new ErrorCode(1_011_005_003, "提现账户支付平台进件信息不存在");
    ErrorCode WITHDRAW_IS_EMPTY = new ErrorCode(1_011_005_004, "提现单信息不存在");
    ErrorCode WITHDRAW_TRANSFER_NOT_FINISH = new ErrorCode(1_011_005_005, "转账未完成");
    ErrorCode WITHDRAW_SETTLE_FINISH = new ErrorCode(1_011_005_006, "结算已完成");
    ErrorCode WITHDRAW_MIN_AMT = new ErrorCode(1_011_005_007, "最小提现金额");
    ErrorCode WITHDRAW_NOT_SUPPLIER = new ErrorCode(1_011_005_008, "没有入驻商提现权限");
    ErrorCode WITHDRAW_SETTLE_PROCESS = new ErrorCode(1_011_005_009,"结算处理中");
    ErrorCode WITHDRAW_BALANCE_ERR = new ErrorCode(1_011_005_010,"可提现余额不足");
    ErrorCode WITHDRAW_TSF_ERR = new ErrorCode(1_011_005_011,"请稍后重试");
    ErrorCode WITHDRAW_DC_NOT_REGISTER = new ErrorCode(1_011_005_012,"所属区域主运营商未进件完成");

    // ========== merchant 模块 1-011-006-000 ==========
    ErrorCode PLATFORM_MERCHANT_EXIST = new ErrorCode(1_011_006_001, "商户已经进件, 请勿重复进件");
    ErrorCode PLATFORM_MERCHANT_NOT_EXIST_MERCHANT = new ErrorCode(1_011_006_002, "进件商户不存在");
    ErrorCode PLATFORM_MERCHANT_NOT_AUDITED = new ErrorCode(1_011_006_003, "商户进件未完成审核");
    ErrorCode PLATFORM_MERCHANT_AUDITED_FINISH = new ErrorCode(1_011_006_004, "商户已经完成进件");
    ErrorCode PLATFORM_MERCHANT_AUDITED_WAIT = new ErrorCode(1_011_006_005, "商户进件审核中");
    ErrorCode PLATFORM_MERCHANT_BANK_CHANNEL_NOT_EXIST = new ErrorCode(1_011_006_006, "未查询到联行号关联银行信息");
    ErrorCode PLATFORM_MERCHANT_ACCESS_TOKEN_NOT_EXIST = new ErrorCode(1_011_006_007, "ACCESS_TOKEN 未查询到");
    ErrorCode PLATFORM_MERCHANT_SERVICE_UNDEFINE = new ErrorCode(1_011_006_008, "商户服务未定义, 不支持处理");
    ErrorCode PLATFORM_MERCHANT_WX_MERCHANT_NONE_EXIST = new ErrorCode(1_011_006_009, "未查询到微信B2B支付商户进件信息");
    ErrorCode PLATFORM_MERCHANT_COLONEL_BIND_KEY_NONE_EXIST = new ErrorCode(1_011_006_010, "绑定凭证失效, 请重新在APP端发起");
    ErrorCode PLATFORM_MERCHANT_COLONEL_BIND_KEY_REPEAT = new ErrorCode(1_011_006_010, "业务员已经绑定openid");
    ErrorCode PLATFORM_MERCHANT_THIRD_NO_NONE_EXIST = new ErrorCode(1_011_006_011, "微信第三方进件单号不存在, 无需同步信息");
    ErrorCode PLATFORM_WX_MERCHANT_SHARING_ERR = new ErrorCode(1_011_006_012, "绑定微信商户信息失败, 请检查商户信息是否正确");
    ErrorCode PLATFORM_WX_MERCHANT_SHARING_ERR1 = new ErrorCode(1_011_006_013, "微信商户绑定, 商户号, 商户名称不能为空");
    ErrorCode PLATFORM_WX_MERCHANT_MCH_PROFIT_RATE_URL_NONE = new ErrorCode(1_011_006_014, "未配置PROVIDER_MCH_PROFIT_RATE_URL请求地址");
    ErrorCode PLATFORM_WX_MERCHANT_MCH_PROFIT_RATE_TIMEOUT = new ErrorCode(1_011_006_015, "请检查当前商户是否在近一个月已经设置了费率, 一个月只能修改一次费率");
    ErrorCode PLATFORM_WX_MERCHANT_MCH_PROFIT_RATE_ERR = new ErrorCode(1_011_006_016, "手续费设置失败,请联系管理员");
    ErrorCode PLATFORM_WX_MERCHANT_CAN_NOT_PRIVATE = new ErrorCode(1_011_006_017, "微信支付不支持个人进件");


    // ========== offline divide 离线分账 模块 1-011-007-000 ==========
    ErrorCode ACCOUNT_OFFLINE_DIVIDE_REPEAT = new ErrorCode(1_011_007_001, "{} 分账已处理");
    ErrorCode ACCOUNT_OFFLINE_DIVIDE_BATCH_MERCHANT_REPEAT = new ErrorCode(1_011_007_002, "合并单据处理只支持同一个商户");


    // ========== online divide 在线分账 模块 1-011-008-000 ==========
    ErrorCode ACCOUNT_ONLINE_DIVIDE_NONE_EXIST = new ErrorCode(1_011_008_001, "{} 无可分账信息");
    ErrorCode ACCOUNT_ONLINE_DIVIDE_MCHID_NONE_EXIST = new ErrorCode(1_011_008_002, "{} 无商户ID");

    // ========== recharge scheme 充值套餐 1-011-009-000 ==========
    ErrorCode RECHARGE_AREA_TIME_CONFLICT = new ErrorCode(1_011_009_001, "当前配置与以下配置时间冲突[{}]");
    ErrorCode NOT_OPEN_WALLET = new ErrorCode(1_011_009_002, "未开启钱包支付相关功能");
    ErrorCode RECHARGE_MAX_RATE = new ErrorCode(1_011_009_003, "充值商户配置异常MAX-3");
    ErrorCode RECHARGE_MERCHANT_ERR_1 = new ErrorCode(1_011_009_004, "软件商商户商户配置异常NOT-1");
    ErrorCode RECHARGE_MERCHANT_ERR_2 = new ErrorCode(1_011_009_005, "平台商商户商户配置异常NOT-2");
    ErrorCode RECHARGE_MERCHANT_ERR_3 = new ErrorCode(1_011_009_006, "运营商商户商户配置异常NOT-3");


    // ========== recharge scheme 充值套餐 1-011-010-000 ==========
    ErrorCode RECHARGE_IMPORT_BRANCH_ERR = new ErrorCode(1_011_010_001, "充值导入门店信息不存在,门店ID:{}");
    ErrorCode RECHARGE_IMPORT_STATE_ERR = new ErrorCode(1_011_010_002, "当前单据状态不允许操作");
    ErrorCode RECHARGE_IMPORT_BRANCH_AREA_ERR = new ErrorCode(1_011_010_003, "{} 门店未关联销售区域");
    ErrorCode RECHARGE_IMPORT_AREA_DC_ERR = new ErrorCode(1_011_010_004, "{} 门店关联区域不存在, 或者区域不存在主运营商");
    ErrorCode RECHARGE_IMPORT_UPDATE_ERR = new ErrorCode(1_011_010_005, "网络异常请稍后再试");
}
