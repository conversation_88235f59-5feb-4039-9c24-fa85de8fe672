package com.zksr.account.api.transfer;

import com.zksr.account.api.transfer.dto.*;
import com.zksr.account.api.transfer.vo.AccTransferBillPageVO;
import com.zksr.account.api.transfer.vo.AccTransferBillOrderExportPageVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/11
 * @desc 内部账户转账接口
 */

@FeignClient(
        contextId = "remoteTransferApi",
        value = ApiConstants.NAME
)
public interface TransferApi {

    String PREFIX = ApiConstants.PREFIX + "/transfer";

    /**
     * 创建内部转账单
     * @param transferSaveDTO   转账单内容
     * @return  创建结果
     */
    @PostMapping(PREFIX + "/createTransfer")
    CommonResult<Boolean> createTransfer(@RequestBody TransferSaveDTO transferSaveDTO);

    /**
     * 创建内部转账单
     * @param transferSaveDTO   转账单内容
     * @return  创建结果
     */
    @PostMapping(PREFIX + "/createTransferBatch")
    CommonResult<Boolean> createTransferBatch(@RequestBody List<TransferSaveDTO> transferSaveDTO);

    /**
     * 获取转账单重试列表
     * @param minId 最小ID
     * @return  每批次返回1000个等待处理转账单
     */
    @GetMapping(PREFIX + "/getRetryAccTransfer")
    CommonResult<List<TransferRetryDTO>> getRetryAccTransfer(@RequestParam("minId") Long minId);

    /**
     * 重试转账单
     * @return  每批次返回1000个等待处理转账单
     */
    @PostMapping(PREFIX + "/retryAccTransfer")
    CommonResult<Boolean> retryAccTransfer(@RequestBody List<TransferRetryDTO> retryList);

    /**
     * 保存商户账单文件备份
     * @return
     */
    @PostMapping(PREFIX + "/insertBillFileRecord")
    CommonResult<Boolean> insertBillFileRecord(@RequestBody AccBillFileDTO accBillFileDTO);


    /**
     * 批量保存交易对账单明细单
     * @return
     */
    @PostMapping(PREFIX + "/insertTransferBillOrder")
    CommonResult<Boolean> insertTransferBillOrder(@RequestBody List<AccTransferBillOrderDTO> accBillOrderDTO);

    /**
     * 批量保存交易对账单
     * @param accTransferBillDTO
     * @return
     */
    @PostMapping(PREFIX + "/insertTransferBill")
    CommonResult<Boolean> insertTransferBill(@RequestBody AccTransferBillDTO accTransferBillDTO);

    /**
     * 判断文件记录是否已经存在
     * @param altNo
     * @param data
     * @return
     */
    @GetMapping(PREFIX + "/checkBillFileRecordExists")
    List<String> checkBillFileRecordExists(@RequestParam("altNo") String altNo,@RequestParam("data") String data);

    /**
     * 删除已经存在的记录
     */
    @GetMapping(PREFIX + "/deleteBillFileRecord")
    void deleteBillFileRecord(@RequestParam("billFileId") String billFileId);

    /**
     * 检查数据库中是否已经存在对应日期和商户号的数据
     * @param dataFormatted
     * @return
     */
    @GetMapping(PREFIX + "/countTransferBillsByDateAndAltNo")
    List<String> countTransferBillsByDateAndAltNo(@RequestParam("dataFormatted")  String dataFormatted,@RequestParam("altNo")  String altNo);

    @GetMapping(PREFIX + "/deleteTransferBillsByDateAndAltNo")
    void deleteTransferBillsByDateAndAltNo(@RequestParam("dataFormatted")  String dataFormatted,@RequestParam("altNo")  String altNo);
    @GetMapping(PREFIX + "/deleteTransferBill")
    void deleteTransferBill(@RequestParam("transferBillId") String transferBillId);

    /**
     * 获取交易对账单数据
     * @param pageVO
     * @return
     */
    @PostMapping(PREFIX + "/getTransferBill")
    CommonResult<List<AccTransferBillRespDTO>> getTransferBill(@RequestBody AccTransferBillPageVO pageVO);

    /**
     * 获取交易对账单明细单导出数据
     * @param reqVo
     * @return
     */
    @PostMapping(PREFIX + "/getAccTransferBillOrderDataExport")
    CommonResult<List<AccTransferBillOrderRespDTO>> getAccTransferBillOrderDataExport(@RequestBody AccTransferBillOrderExportPageVO reqVo);
}
