package com.zksr.system.api.invoice.dto;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 蓝票开具相关DTO
 */
@Data
@ApiModel("蓝票开具相关DTO")
@JSONType(alphabetic = false,           // 坑：一定要设置，不然还是按ascii码顺序排列
        serializeFeatures = {
                JSONWriter.Feature.WriteNulls,  // 保留 null 字段
                JSONWriter.Feature.FieldBased   // 按字段声明顺序
        })
public class TransBlueInvoiceRequest {
    /**
     * 上游传入唯一流水号（必填）
     */
    private String bizId;

    /**
     * 是否直接开票（必填）
     * 0: 非直接开票，保存到待开列表，由操作人员勾选需要开的发票，进行开票
     * 1: 直接开票，马上开（若editable传可修改需确认后直接开票）。
     */
    private Integer immediateInvoice;

    /**
     * 是否可修改（可选）
     * 0: 不可修改
     * 1: 可修改
     */
    private Integer editable;

    /**
     * OU编码（可选）
     */
    private String ouCode;

    /**
     * OU名称（可选）
     */
    private String ouName;

    /**
     * 省份中心（可选）
     */
    private String provinceCentre;

    /**
     * ERP客户编码（可选）
     */
    private String erpCustCode;

    /**
     * 发票类型（必填）
     * 1: 电子普票
     * 2: 电子专票
     * 3: 纸质普票
     * 4: 纸质专票
     */
    private Integer invoiceType;

    /**
     * 销方税号（必填）
     */
    private String taxpayerCode;

    /**
     * 发票含税总金额（必填）
     * 发票金额必须等于明细所有数据含税金额总和
     */
    private BigDecimal invoiceAmt;

    /**
     * 发票抬头（可选）
     * 1: 个人
     * 2: 企业
     * 开专票时，必须为企业
     */
    private Integer invoiceHead;

    /**
     * 商户ID（可选）
     */
    private String merchantId;

    /**
     * 购方税号（可选）
     * 购方为企业时必填
     */
    private String payTaxpayerCode;

    /**
     * 购方名称（必填）
     */
    private String payTaxpayerName;

    /**
     * 购方地址（可选）
     */
    private String payUnitAddress;

    /**
     * 购方电话号码（可选）
     */
    private String payFixedPhoneNumber;

    /**
     * 购方银行名称（可选）
     */
    private String payBankName;

    /**
     * 购方银行账号（可选）
     */
    private String payBankAccount;

    /**
     * 购方邮箱（可选）
     */
    private String mail;

    /**
     * 票面备注（可选）
     */
    private String remarks;

    /**
     * 拆合规则（可选）
     * 1: 不启用发票系统规则 - 不拆不合
     * 2: 使用发票系统规则
     * 没有该字段走不使用开票系统规则
     */
    private String invoiceRule;

    /**
     * 开票成功接收短信的手机号（可选）
     */
    private String messagePhone;

    /**
     * 主业务单号（可选）
     */
    private String businessNo;

    /**
     * 客户备注（可选）
     */
    private String customerRemarks;

    /**
     * 特定业务类型（可选）
     * 03: 建筑服务
     * 04: 货物运输
     * 05: 不动产销售
     * 06: 不动产租赁
     */
    private String specialElementType;

    /**
     * 特定业务类型JSON字符串 开具特定业务类型数电票需要额外的特定业务信息，特定业务类型可以根据税收分类编码映射（智汇票会基于发票第一行明细的税收分类编码进行映射） 当其为货物运输特定业务类型时，其JSON格式为： [{ "toolType": "公路运输",//运输工具种类：铁路运输、公路运输、水路运输、航空运输、管道运输、其他运输工具 "toolNum": "粤A23401",//运输工具牌号 "departure": "湖南长沙",//启运地 "arrive": "广东佛山",//到达地 "goodsName": "14"//货物名称 }] 2. 当其为建筑服务特定业务类型时，其JSON格式为： { "buildingLocalAddress":"建筑服务特定要素-建筑服务发生地，按照省、市、区/县三级传值，以&符间隔，举例“北京市&东城区、河北省&石家庄市&正定县”(建筑服务发生地和详细地址之和为 120)", "buildingDetailAddress":"建筑服务特定要素-建筑服务详细地址，举例“北京市海淀区清华东路 17 号", "buildingName":"建筑服务特定要素-建筑项目名称", "buildingLandTaxNo":"建筑服务特定要素-土地增值税项目编号", "buildingCrossSign":"建筑服务特定要素-跨地（市）标志；标志：Y：是；N：否", } 3. 当其为不动产租赁特定业务类型时，其JSON格式为： { "leasePropertyNo":"不动产租赁-房屋产权证书/不动产权证号码", "leaseAddress":"不不动产租赁-不动产地址，按照省、市、区/县三级传值，以&符间隔，举例“北京市&东城区、河北省&石家庄市&正定县", "leaseDetailAddress":"不动产租赁-详细地址，举例“北京市海淀区清华东路17 号", "leaseCrossSign":"不动产租赁-跨地（市）标志", "leaseAreaUnit":"不动产租赁-面积单位", "leaseHoldDateStart":"不动产租赁-租赁期起；yyyy-MM-dd", "leaseHoldDateEnd":"不动产租赁-租赁期止；yyyy-MM-dd" } 4. 当其为不动产销售服务特定业务类型时，其JSON格式为： { "propertyPropertyNo":"不动产销售服务-房屋产权证书/不动产权证号码", "propertyAddress":"不动产销售服务-不动产地址，按照省、市、区/县三级传值，以&符间隔，举例“北京市&东城区、河北省&石家庄市&正定县", "propertyDetailAddress":"不动产销售服务-详细地址，举例“北京市海淀区清华东路17 号", "propertyContractNo":"不动产销售服务-不动产单元代码/网签合同备案编码", "propertyLandTaxNo":"不动产销售服务-土地增值税项目编号", "propertyCrossSign":"不动产销售服务-跨地（市）标志", "propertyAreaUnit":"不动产销售服务-面积单位", "propertyApprovedPrice":不动产销售服务-核定计税价格//number类型，小数 "propertyDealPrice":不动产销售服务-实际成交含税金额//number类型，小数 }
     */
    private String specialElementJsonstr;

    /**
     * 备注银行信息标记（可选）
     * 1: 备注销售方银行账号信息
     * 2: 备注购买方银行账号信息
     * 3: 备注销售方和购买方银行账号信息
     */
    private Integer remarkAddBankAccountInfo;

    /**
     * 发票明细列表（必填）
     */
    private List<InvoiceBlueDetailDTO> detailList;

    /**
     * 发票明细DTO
     */
    @Data
    @JSONType(alphabetic = false,           // 坑：一定要设置，不然还是按ascii码顺序排列
            serializeFeatures = {
                    JSONWriter.Feature.WriteNulls,  // 保留 null 字段
                    JSONWriter.Feature.FieldBased   // 按字段声明顺序
            })
    public static class InvoiceBlueDetailDTO {
        /**
         * 开票项唯一id（必填）
         */
        private String bizDetailId;

        /**
         * 业务单号（必填）
         */
        private String businessNo;

        /**
         * 商品编码（可选）
         * 如果此项传入数据，那么以下字段可以为空，开票项税码税率等信息以发票系统为准
         * 如果此项不传数据，那么以下字段部分必填
         */
        private String code;

        /**
         * 开票项目/商品名称（可选）
         */
        private String goodsName;

        /**
         * 单位（可选）
         */
        private String goodsUnit;

        /**
         * 税收分类编码（可选）
         * 支持长、短税码，长税码固定19位，确保在主数据平台有维护
         */
        private String taxCode;

        /**
         * 税率（可选）
         */
        private BigDecimal taxRate;

        /**
         * 规格编码/规格型号（可选）
         */
        private String standards;

        /**
         * 软著名称（可选）
         */
        private String softwareTitle;

        /**
         * 数量（可选）
         * 折扣开票项时为负数，否则为正数
         */
        private BigDecimal goodCount;

        /**
         * 含税单价（可选）
         * 必须为正数
         */
        private BigDecimal taxUnitPrice;

        /**
         * 含税金额（必填）
         * 含税金额必须等于 含税单价*数量
         * 折扣开票项时为负数
         */
        private BigDecimal amtContainTax;

        /**
         * 税额（可选）
         * 如果上游传了就用上游的税额，没传系统会进行反算
         * 反算可能会产生一定误差，所以建议上游都传
         */
        private BigDecimal amtTax;

        /**
         * 明细备注（可选）
         */
        private String detailRemark;

        /**
         * 是否享受优惠政策（可选）
         * 0: 不享受
         * 1: 享受
         */
        private Integer discountStatus;

        /**
         * 优惠政策类型（可选）
         * 0: 普通零税
         * 1: 不征税
         * 2: 免税
         * 5: 简易征收
         * 6: 按5%简易征收
         */
        private String discountType;
    }
}
