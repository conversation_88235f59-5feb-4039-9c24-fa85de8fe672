package com.zksr.portal.api.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p> 业务员小程序错误码
 * bpm 系统，使用 1-102-000-000 段
 */
public interface AppErrorCodeConstants {

    // ==========  通用流程处理 模块 1-102-001-000 ==========
    ErrorCode APP_COLONEL_CHECK_MEMBER_USERNAME = new ErrorCode(1_102_000_001, "该手机号已存在用户信息，请重新确认或联系相关人员");
    ErrorCode APP_COLONEL_CHECK_MEMBER_REGISTER_USERNAME = new ErrorCode(1_102_000_002, "该手机号已申请用户注册，请重新确认或联系相关人员");
    ErrorCode APP_COLONEL_CHECK_COLONEL_ID = new ErrorCode(1_102_000_003, "当前用户异常，请联系相关人员");
    ErrorCode APP_COLONEL_CHECK_BRANCH_ID = new ErrorCode(1_102_000_004, "当前门店异常，请联系相关人员");
    ErrorCode APP_COLONEL_CHECK_VISIT_LOG = new ErrorCode(1_102_000_005, "上一家门店未签退,请先签退");
    ErrorCode APP_COLONEL_CHECK_PHONE_LOG = new ErrorCode(1_102_000_006, "手机号码必填不能为空");
    ErrorCode APP_COLONEL_CHECK_NAME_LOG = new ErrorCode(1_102_000_007, "姓名必填不能为空");
    ErrorCode APP_COLONEL_CHECK_PASSWORD_LOG = new ErrorCode(1_102_000_008, "密码必填不能为空");
    ErrorCode APP_COLONEL_CHECK_SEX_LOG = new ErrorCode(1_102_000_009, "性别必填不能为空");
    ErrorCode APP_COLONEL_CHECK_IDCARD_LOG = new ErrorCode(1_102_000_010, "身份证必填不能为空");
    ErrorCode APP_COLONEL_INVALID_PHONE_FORMAT = new ErrorCode(1_102_000_011, "手机号码格式不正确，请重试！");
    ErrorCode APP_COLONEL_INVALID_IDCARD_FORMAT = new ErrorCode(1_102_000_012, "身份证填写格式不正确，请重试！");
    ErrorCode APP_COLONEL_ANCHOR_COMMAND_NOT_EXISTS = new ErrorCode(1_102_000_013, "锚点指令不存在！");

    ErrorCode APP_COLONEL_ORDINARY_COMMAND_PERFORM_EXISTS = new ErrorCode(1_102_000_014, "已存在执行中普通指令！");


    // ==========  通用流程处理 模块 1-102-001-000 ==========
    ErrorCode SHOP_RECODE_CONFIG_ERR = new ErrorCode(1_102_001_001, "未配置商城小程序appid");
    ErrorCode SHOP_RECODE_ACCESS_TOKEN_ERR = new ErrorCode(1_102_001_002, "未获取到有效token");

}
