package com.zksr.promotion.api.coupon.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 使用优惠券
 * @date 2024/4/2 16:17
 */
@ToString
@Data
@ApiModel(description = "核销使用优惠券")
public class CouponApplyDTO {

    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty(value = "优惠券具体ID", required = true)
    private Long couponId;

    @ApiModelProperty(value = "对应订单号", required = true)
    private String relateOrderNo;
}
