package com.zksr.member.api.colonel.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 业务员信息对象 mem_colonel
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@ApiModel("业务员信息 - mem_colonel分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemColonelPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 业务员手机号 */
    @Excel(name = "业务员手机号")
    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    /** 业务员名 */
    @Excel(name = "业务员名")
    @ApiModelProperty(value = "业务员名")
    private String colonelName;

    /** 业务员级别（职务） */
    @Excel(name = "业务员级别", readConverterExp = "职=务")
    @ApiModelProperty(value = "业务员级别 数据字典(1:业务专员,2:业务经理)")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    @ApiModelProperty(value = "父业务员id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pcolonelId;

    /** 性别（数据字典） */
    @Excel(name = "性别", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "性别")
    private Integer sex;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态 1正常 0停用")
    @ApiModelProperty(value = "状态 1正常 0停用")
    private Integer status;

    /** 来源 APP PC */
    @Excel(name = "来源APP或PC")
    @ApiModelProperty(value = "来源 APP或PC")
    private String source;

    /** 出生日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "出生日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /** 籍贯 */
    @Excel(name = "籍贯")
    @ApiModelProperty(value = "籍贯")
    private String birthplace;

    /** 入职日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "入职日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "入职日期")
    private Date entryDate;

    /** 学历(数据字典) */
    @Excel(name = "学历(数据字典)")
    @ApiModelProperty(value = "学历(数据字典)")
    private String edu;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idcard;

    /** 提成系数 */
    @Excel(name = "提成系数")
    @ApiModelProperty(value = "提成系数")
    private BigDecimal percentageRate;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddr;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否是业务管理员（1:是，2：否） */
    @Excel(name = "是否是业务管理员", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "是否是业务管理员")
    private String isColonelAdmin;

    /** 部门 */
    @Excel(name = "部门")
    @ApiModelProperty(value = "部门")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long deptId;

    /** APP下单改价（1:是，2：否） */
    @Excel(name = "APP下单改价", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "APP下单改价")
    private String appOrderPriceAdjust;

    /** APP退货改价（1:是，2：否） */
    @Excel(name = "APP退货改价", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "APP退货改价")
    private String appAfterPriceAdjust;

    /** 下单自动审核（1:是，2：否） */
    @Excel(name = "下单自动审核", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "下单自动审核")
    private String orderAutoApprove;

    @ApiModelProperty(value = "是否分页  1:分页, 0：不分页")
    private Integer isPage = 1;

    /** 用户ID */
    @Excel(name = "用户ID")
    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long userId;

    /** 用户账号 */
    @Excel(name = "业务员登录名称")
    @ApiModelProperty(value = "业务员登录名称")
    private String userName;


    /** 密码 */
    @Excel(name = "业务员密码")
    @ApiModelProperty(value = "业务员密码")
    private String password;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "0待审核 1审核通过 2审核不通过")
    private Integer auditState = 1;

    /** 审核备注 */
    @Excel(name = "审核备注")
    @ApiModelProperty(value = "审核备注")
    private String auditMemo;
}
