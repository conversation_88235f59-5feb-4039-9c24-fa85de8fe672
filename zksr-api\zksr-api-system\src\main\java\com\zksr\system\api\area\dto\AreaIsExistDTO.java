package com.zksr.system.api.area.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class AreaIsExistDTO implements Serializable {

    @ApiModelProperty(value = "区域等级")
    private Integer level;

    @ApiModelProperty(value = "二级城市")
    private List<Long> downAreaId;

    @ApiModelProperty(value = "二级城市")
    private List<String> downAreaName;

    @ApiModelProperty(value = "关联的运营商Id")
    private List<Long> dcIds;

    @ApiModelProperty(value = "关联的运营商名称")
    private List<String> dcIdNames;

    @ApiModelProperty(value = "关联的入驻商Id")
    private List<Long> supplierIds;

    @ApiModelProperty(value = "关联的入驻商名称")
    private List<String> supplierNames;

    @ApiModelProperty(value = "是否生成订单")
    private Boolean orderData;

    @ApiModelProperty(value = "是否生成业务员")
    private Boolean colonelData;
    @ApiModelProperty(value = "关联业务员id")
    private List<Long> colonelDataIds;
    @ApiModelProperty(value = "关联业务员名称")
    private List<String> colonelDataNames;


    @ApiModelProperty(value = "是否关联门店")
    private Boolean branchData;
    @ApiModelProperty(value = "关联门店id")
    private List<Long> branchDataIds;
    @ApiModelProperty(value = "关联门店名称")
    private List<String> branchDataNames;

    @ApiModelProperty(value = "是否关联商品")
    private Boolean skuData;

    @ApiModelProperty(value = "是否删除成功 0 没有成功删除 1 成功删除了")
    private Boolean isDelete;

}
