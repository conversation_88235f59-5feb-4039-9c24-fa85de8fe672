package com.zksr.product.api.materialApply;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.materialApply.dto.MaterialApplyDTO;
import com.zksr.product.api.materialApply.vo.MaterialApplyVO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(
        contextId = "remoteMaterialApplyApi",
        value = ApiConstants.NAME
)
/**
*
 *  入驻商服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface MaterialApplyApi {

    String PREFIX = ApiConstants.PREFIX + "/materialApply";

    @GetMapping(PREFIX + "/getByMaterialApplyById")
    CommonResult<MaterialApplyDTO> getByMaterialApplyById(@RequestParam("materialApplyId") Long materialApplyId);

    /**
     * 新增素材应用信息
     */
    @PostMapping(PREFIX + "/addMaterialApply")
    CommonResult<Boolean> addMaterialApply(@RequestBody MaterialApplyDTO dto);

    /**
     * 修改素材应用信息
     */
    @PostMapping(PREFIX + "/editMaterialApply")
    CommonResult<Boolean> editMaterialApply(@RequestBody MaterialApplyDTO dto);

    @PostMapping(PREFIX + "/getMaterialApplyByEchoReq")
    CommonResult<MaterialApplyDTO> getMaterialApplyByEchoReq(@RequestBody MaterialApplyDTO dto);

    @GetMapping(PREFIX + "/getByMaterialApplyByApplyIds")
    CommonResult<List<MaterialApplyDTO>> getByMaterialApplyByApplyIds(@RequestParam("applyIds") List<Long> applyIds);

    @PostMapping(PREFIX + "/getByMaterialApplyByMaterial")
    CommonResult<MaterialApplyVO> getByMaterialApplyByMaterial(@RequestBody MaterialApplyVO vo);


}
