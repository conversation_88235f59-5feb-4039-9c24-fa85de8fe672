package com.zksr.system.api.page.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 页面配置
 * @date 2024/4/12 17:41
 */
@Data
@ApiModel(description = "平台首页配置")
public class PagesConfigDTO {
    @ApiModelProperty("页面ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pageId;

    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("平台商ID")
    private Long sysCode;

    @ApiModelProperty("渠道ID")
    private String channelId;

    /** 页面名称 */
    @Excel(name = "页面名称")
    private String pageName;

    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("区域城市ID")
    private Long areaId;

    @ApiModelProperty("配置文件地址")
    private String jsonUrl;

    /** 扩展url 限制1024字符*/
    @ApiModelProperty(value = "扩展url, 限制1024字符")
    private String urlDtl;

    @ApiModelProperty("子页面集合")
    private List<PagesConfigDTO> childList;

    @ApiModelProperty("0-固定模版, 1-时效模版")
    private Integer type;

    @ApiModelProperty("有效开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date startTime;

    @ApiModelProperty("有效结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date endTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PagesConfigDTO configDTO = (PagesConfigDTO) o;
        return Objects.equals(pageId, configDTO.pageId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pageId);
    }
}
