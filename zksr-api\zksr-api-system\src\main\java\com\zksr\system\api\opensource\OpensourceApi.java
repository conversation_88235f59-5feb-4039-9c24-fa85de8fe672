package com.zksr.system.api.opensource;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        contextId = "remoteOpensourceApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface OpensourceApi {

    String PREFIX = ApiConstants.PREFIX + "/opensource";

    @GetMapping(value = "/getInfoByMobileAndOpenid")
    public CommonResult<OpensourceDto> getInfoByOpensouceIdAndSourceSecret(
                                                                @RequestParam(name = "opensourceId") String opensourceId,
                                                                @RequestParam(name = "sourceSecret") String sourceSecret);

    @GetMapping(value = "/getOpensourceInfo")
    CommonResult<LoginOpensource> getOpensourceInfo(@RequestParam(name = "sourceKey") String sourceKey,
                                                    @RequestParam(name = "sourceSecret") String sourceSecret);

    @GetMapping(value = "/updateTokenUuid")
    void updateTokenUuid(@RequestParam(name = "opensourceId")Long opensourceId,
                        @RequestParam(name = "token")String token);

    /**
     * 根据入驻商或平台商ID 获取配置
     * @param merchantId
     * @return
     */
    @GetMapping(value = "/getOpensourceByMerchantId")
    public CommonResult<OpensourceDto> getOpensourceByMerchantId(
            @RequestParam(name = "merchantId") Long merchantId);


    /**
     * 根据入驻商 获取可视化主表配置
     * @param merchantId
     * @return
     */
    @GetMapping(value = "/getVisualSettingMasterByMerchantId")
    public CommonResult<VisualSettingMasterDto> getVisualSettingMasterByMerchantId(
            @RequestParam(name = "merchantId") Long merchantId);

    /**
     * 根据入驻商ID + 模板类型 获取对应可视化详情配置
     * @param key
     * @return
     */
    @GetMapping(value = "/getVisualSettingDetailByMerchantId")
    public CommonResult<VisualSettingDetailDto> getVisualSettingDetailByMerchantId(
            @RequestParam(name = "key") String key);

    /**
     * openapi新增入驻商开放能力API
     *
     * @param data
     */
    @PostMapping(value = PREFIX + "/insertOpensourceSupplierByApi")
    public CommonResult<OpensourceDto> insertOpensourceSupplierByApi(@RequestBody OpensourceDto data);

    /**
     * 根据平台商ID 获取当前平台下的开放能力信息  如果传null 则查询所有
     * @param sysCode
     * @return
     */
    @GetMapping(value = "/getOpenSourceBySysCode")
    public CommonResult<List<OpensourceDto>> getOpenSourceBySysCode(@RequestParam(name = "sysCode",required = false) Long sysCode);
}
