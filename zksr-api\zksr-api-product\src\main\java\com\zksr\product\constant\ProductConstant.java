package com.zksr.product.constant;

/**
*
 * 商品模块相关常量
* <AUTHOR>
* @date 2024/3/1 15:30
*/
public class ProductConstant {

    /** 状态 -- 停用*/
    public static final long PRDT_STATUS_0 = 0;

    /** 状态 -- 启用*/
    public static final long PRDT_STATUS_1 = 1;

    /** 状态 -- 停用*/
    public static final int PRDT_STATUS_INT_0 = 0;

    /** 状态 -- 启用*/
    public static final int PRDT_STATUS_INT_1 = 1;

    /** 是否删除 -- 否*/
    public static final long PRDT_IS_DELETE_0 = 0;

    /** 是否删除 -- 是*/
    public static final long PRDT_IS_DELETE_1 = 1;

    /** 是否开启多规格 -- 否*/
    public static final long PRDT_IS_SPECS_0 = 0;

    /** 是否开启多规格 -- 是*/
    public static final long PRDT_IS_SPECS_1 = 1;

    /** 上架状态 -- 未上架*/
    public static final Integer PRDT_SHELF_STATUS_0 = 0;

    /** 上架状态 -- 已上架*/
    public static final Integer PRDT_SHELF_STATUS_1 = 1;

    /** 移动标识 -- 下移*/
    public static final Integer PRDT_SORT_FLAG_0 = 0;

    /** 移动标识 -- 上移*/
    public static final Integer PRDT_SORT_FLAG_1 = 1;

    /** 价格方案批量定价最大查询保存数*/
    public static final long PRDT_SKU_PRICE_MAX_SAVE_NUM = 9999;

    /** 价格方案批量定价最大查询保存页*/
    public static final long PRDT_SKU_PRICE_MAX_SAVE_PAGE = 1;

    /** 价格方案批量定价  价格选项1*/
    public static final String PRDT_SKU_PRICE_PRICE_OPTION_1 = "salePrice1";

    /** 价格方案批量定价  价格选项2*/
    public static final String PRDT_SKU_PRICE_PRICE_OPTION_2 = "salePrice2";

    /** 价格方案批量定价  价格选项3*/
    public static final String PRDT_SKU_PRICE_PRICE_OPTION_3 = "salePrice3";

    /** 价格方案批量定价  价格选项4*/
    public static final String PRDT_SKU_PRICE_PRICE_OPTION_4 = "salePrice4";

    /** 价格方案批量定价  价格选项5*/
    public static final String PRDT_SKU_PRICE_PRICE_OPTION_5 = "salePrice5";

    /** 价格方案批量定价  价格选项6*/
    public static final String PRDT_SKU_PRICE_PRICE_OPTION_6 = "salePrice6";

    /** 价格方案批量定价  价格类型1 -- 成本价*/
    public static final String PRDT_SKU_PRICE_PRICE_TYPE_1 = "costPrice";

    /** 价格方案批量定价  价格类型2 -- 标准价*/
    public static final String PRDT_SKU_PRICE_PRICE_TYPE_2 = "markPrice";

    /** 价格方案批量定价  单位类型 -- 小单位*/
    public static final String PRDT_SKU_PRICE_UNIT_TYPE_1 = "minUnit";

    /** 价格方案批量定价  单位类型 -- 中单位*/
    public static final String PRDT_SKU_PRICE_UNIT_TYPE_2 = "midUnit";

    /** 价格方案批量定价  单位类型 -- 大单位*/
    public static final String PRDT_SKU_PRICE_UNIT_TYPE_3 = "largeUnit";

    /** 价格方案定价标识 -- 新增*/
    public static final Integer PRDT_SKU_PRICE_SAVE_FLAG_0 = 0;

    /** 价格方案定价标识 -- 批量定价*/
    public static final Integer PRDT_SKU_PRICE_SAVE_FLAG_1 = 1;

    /** 价格方案类型  全国价格方案*/
    public static final Integer PRDT_SKU_PRICE_TYPE_0 = 0;

    /** 价格方案类型  城市价格方案*/
    public static final Integer PRDT_SKU_PRICE_TYPE_1 = 1;

    /** 上架商品  修改标识 修改排序*/
    public static final Integer PRDT_ITEM_UPDATE_FLAG_0 = 0;

    /** 上架商品  修改标识 修改类别*/
    public static final Integer PRDT_ITEM_UPDATE_FLAG_1 = 1;

    /**
     * 全国商品
     */
    public static Integer PRDT_ITEM_TYPE_0 = 0;

    /**
     * 本地商品
     */
    public static Integer PRDT_ITEM_TYPE_1 = 1;

    /**
     * 新增商品类型 后台新增
     */
    public static Integer PRDT_ADD_TYPE_0 = 0;

    /**
     * 新增商品类型 导入新增
     */
    public static Integer PRDT_ADD_TYPE_1 = 1;

    /**
     * 商品调价单 生效类型 0 定时生效
     */
    public static Long PRDT_ADJUST_VALID_TYPE_0 = 0L;

    /**
     * 商品调价单 生效类型 1 立即生效
     */
    public static Long PRDT_ADJUST_VALID_TYPE_1 = 1L;

    /**
     * 商品调价单 定时调价执行状态 0-未执行
     */
    public static Integer PRDT_ADJUST_TASK_EXECUTE_STATUS_0 = 0;

    /**
     * 商品调价单 定时调价执行状态 1-已执行
     */
    public static Integer PRDT_ADJUST_TASK_EXECUTE_STATUS_1 = 1;

    /**
     * 展示分类复制类型 0 单个分类复制 //// 0 上架商品多选复制
     */
    public static Integer PRDT_CLASS_COPY_TYPE_0 = 0;

    /**
     * 展示分类复制类型 1 一键城市分类复制 /// 1 一键复制上架商品
     */
    public static Integer PRDT_CLASS_COPY_TYPE_1 = 1;

    /**
     * 素材应用管理  状态： 0 未生效
     */
    public static Integer PRDT_MATERIAL_APPLY_STATUS_0 = 0;


    /**
     * 素材应用管理  状态： 1 生效中
     */
    public static Integer PRDT_MATERIAL_APPLY_STATUS_1 = 1;

    /**
     * 素材应用管理  状态： 2 已失效
     */
    public static Integer PRDT_MATERIAL_APPLY_STATUS_2 = 2;

    /**
     * 素材应用管理  素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品
     */
    public static final int PRDT_MATERIAL_APPLY_TYPE_1 = 1;

    /**
     * 素材应用管理  素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品
     */
    public static final int PRDT_MATERIAL_APPLY_TYPE_2 = 2;

    /**
     * 素材应用管理  素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品
     */
    public static final int PRDT_MATERIAL_APPLY_TYPE_3 = 3;


}
