package com.zksr.product.api.combine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    组合促销商品
 * @date 2024/12/28 17:09
 */
@Data
@ApiModel(description = "组合促销商品")
public class SpuCombineDTO {
    /**
     * 组合商品ID
     */
    @ApiModelProperty("组合商品ID")
    private Long spuCombineId;

    /**
     * 平台商编号
     */
    @ApiModelProperty("平台商编号")
    private Long sysCode;

    /**
     * 供应商ID
     */
    @ApiModelProperty("供应商ID")
    private Long supplierId;

    /**
     * 区域ID
     */
    @ApiModelProperty("区域ID")
    private Long areaId;

    /**
     * 管理分类ID
     */
    @ApiModelProperty("管理分类ID")
    private Long categoryId;

    /**
     * 组合商品编号
     */
    @ApiModelProperty("组合商品编号")
    private String spuCombineNo;

    /**
     * 组合商品名称
     */
    @ApiModelProperty("组合商品名称")
    private String spuCombineName;

    /**
     * 封面图片
     */
    @ApiModelProperty("封面图片")
    private String thumb;

    /**
     * 封面视频
     */
    @ApiModelProperty("封面视频")
    private String thumbVideo;

    /**
     * 详情页轮播图
     */
    @ApiModelProperty("详情页轮播图")
    private String images;

    /**
     * 组合商品描述,富文本
     */
    @ApiModelProperty("组合商品描述,富文本")
    private String details;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    /**
     * 1-删除, 0-正常
     */
    @ApiModelProperty("1-删除, 0-正常")
    private Integer isDelete;

    /**
     * 1-启用,0-停用
     */
    @ApiModelProperty("1-启用,0-停用")
    private Integer status;

    /**
     * 规格名称
     */
    @ApiModelProperty("规格名称")
    private String specName;

    /**
     * 总限量
     */
    @ApiModelProperty("总限量")
    private Integer totalLimit;

    /**
     * 最新起订
     */
    @ApiModelProperty("最新起订")
    private Integer minOq;

    /**
     * 起订组数
     */
    @ApiModelProperty("起订组数")
    private Integer jumpOq;

    /**
     * 最大限购
     */
    @ApiModelProperty("最大限购")
    private Integer maxOq;

    /**
     * 标准价
     */
    @ApiModelProperty("标准价")
    private BigDecimal markPrice;

    /**
     * 建议零售价
     */
    @ApiModelProperty("建议零售价")
    private BigDecimal suggestPrice;

    /**
     * 销售价1
     */
    @ApiModelProperty("销售价1")
    private BigDecimal salePrice1;

    /**
     * 销售价2
     */
    @ApiModelProperty("销售价2")
    private BigDecimal salePrice2;

    /**
     * 销售价3
     */
    @ApiModelProperty("销售价3")
    private BigDecimal salePrice3;

    /**
     * 销售价4
     */
    @ApiModelProperty("销售价4")
    private BigDecimal salePrice4;

    /**
     * 销售价5
     */
    @ApiModelProperty("销售价5")
    private BigDecimal salePrice5;

    /**
     * 销售价6
     */
    @ApiModelProperty("销售价6")
    private BigDecimal salePrice6;

    /**
     * 单位/字典值
     */
    @ApiModelProperty("单位/字典值")
    private Integer unit;

    /**
     * 组合促销商品明细
     */
    @ApiModelProperty("组合促销商品明细")
    private List<SpuCombineDtlDTO> combineDtls;
}
