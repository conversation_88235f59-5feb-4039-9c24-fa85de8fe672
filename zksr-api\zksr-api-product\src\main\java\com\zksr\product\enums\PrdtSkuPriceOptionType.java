package com.zksr.product.enums;

/**
 * 价格码及价格方案类型
 */
public enum PrdtSkuPriceOptionType {

    SALE_PRICE_1(1,"salePrice1"),

    SALE_PRICE_2(2,"salePrice2"),

    SALE_PRICE_3(3,"salePrice3"),

    SALE_PRICE_4(4,"salePrice4"),

    SALE_PRICE_5(5,"salePrice5"),

    SALE_PRICE_6(6,"salePrice6");

    //价格码
    private final Integer type;

    //价格名称
    private final String name;

    PrdtSkuPriceOptionType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
