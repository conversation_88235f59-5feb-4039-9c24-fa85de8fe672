-- 新增OPENAPI 售后状态接口 相关配置
ALTER TABLE `zksr_trade`.`trd_express_status`
    MODIFY COLUMN `logistics_status` int(8)  DEFAULT null COMMENT '订单物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货） 售后物流状态（1、门店发起售后  2、商家已同意 11、待确认收货 12、已确认收货 13、已入库 21、商家同意退款 22、退款成功））',
    ADD COLUMN `logistics_status_info` VARCHAR(64) NULL COMMENT '物流状态信息',
    ADD COLUMN `source_order_no` VARCHAR(64) NULL COMMENT '外部（ERP）单号',
    ADD COLUMN `start_time` datetime(3) DEFAULT NULL COMMENT '开始时间';

INSERT INTO `zksr_cloud`.`sys_openability`(`openability_id`, `create_by`, `create_time`, `update_by`, `update_time`, `pid`, `merchant_type`, `ability_name`, `ability_key`, `status`, `rate_limit`) VALUES (585073686581477376, 'zksr', '2025-02-20 15:44:43.970', NULL, NULL, NULL, 'supplier', '售后状态', 'afterLog', '0', 10);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '售后状态', '16', 'log_request_type', '', 'default', 'N', '0', 'zksr', '2025-02-20 15:46:26', 'zksr', '2025-02-20 15:47:11', NULL);

-- 欠款订单查询权限
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`,`parent_id`,`order_num`,`menu_code`,`menu_pcode`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`,`func_scop`) VALUES ('欠款订单查询',2262,1,'vNwNejebEVrKgyaVDF','2262','',NULL,NULL,1,0,'F','0','0','trade:order:getDebtOrderPageList','#','zksr','2025-02-17 11:13:24','',NULL,'','software,partner,dc,supplier');