
package com.zksr.system.api.export.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 导出任务分页查询数据
 * @date 2024/10/24 14:46
 */
@Data
@ApiModel(description = "导出任务查询VO")
public class SysExportJobRespVO extends BaseEntity {


    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "任务KEY")
    private String jobKey;
    /**
     * 参见 {@link com.zksr.file.api.constant.ReportState}
     */
    @ApiModelProperty(value = "状态,0-等待中,1-执行中,2-成功,3-失败")
    private String state;

    @ApiModelProperty(value = "文件地址, 导出成功才有")
    private String file;

    /**
     * 参加 {@link com.zksr.file.api.constant.ExportType}
     */
    @ApiModelProperty("导出类型")
    private String exportType;

    /** 模板开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "执行时间")
    private Date executeTime;

    /** 模板开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    @ApiModelProperty(value = "请求IP")
    private String remoteIp;

    @ApiModelProperty(value = "用户key")
    private String userKey;

    /**
     * 平台商编号
     */
    @ApiModelProperty(value = "平台商编号")
    private Long sysCode;

}
