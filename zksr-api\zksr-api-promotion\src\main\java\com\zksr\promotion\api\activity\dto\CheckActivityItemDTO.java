package com.zksr.promotion.api.activity.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
* 全国/城市 商品下架商品校验是否参与促销实体 DTO
* @date 2024/11/8 17:03
* <AUTHOR>
*/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CheckActivityItemDTO {

    /** 活动id */
    @ApiModelProperty("促销活动ID")
    private Long activityId;

    /** 活动名称 */
    @Excel(name = "活动名称")
    @ApiModelProperty("活动名称")
    private String activityName;

    /** sku_id */
    @Excel(name = "sku_id")
    private Long skuId;

    /** 促销类型（数据字典）;SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数=据字典")
    @ApiModelProperty("SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价")
    private String prmNo;

    /** 促销单号 */
    @Excel(name = "促销单号")
    @ApiModelProperty("促销单号")
    private String prmSheetNo;
}
