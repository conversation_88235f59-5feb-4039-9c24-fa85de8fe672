package com.zksr.system.api.partnerConfig.dto;

import com.zksr.common.core.enums.PartnerProfitModelEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 软件商支付配置类
 * @Author: liuxingyu
 * @Date: 2024/3/26 9:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayConfigDTO {
    /**
     * 商城订单支付平台
     */
    @ApiModelProperty(value = "商城订单支付平台", notes = "入驻商T+1结算所在平台, 含 自提点充值, 商场订单支付")
    private String storeOrderPayPlatform;

    /**
     * 内部储值账体系支付平台
     */
    @ApiModelProperty(value = "内部储值账体系支付平台", notes = "平台给各方分佣所在平台, 含 入驻商充值, 商场订单分佣")
    private String interiorStoredPayPlatform;

    @ApiModelProperty("是否开启在线支付, 0-关闭, 1-开启")
    private String switchStoreOrderPay = StringPool.ONE;

    /** 在线支付分润模式, 1-直接分账, 2-入驻商充值 */
    @ApiModelProperty("在线支付分润模式, 1-直接分账, 2-入驻商充值")
    private String payDivideModel = StringPool.ONE;

    /**
     * 参见 {@link PartnerProfitModelEnum}
     */
    @ApiModelProperty("利润模式: 0=(售价*比例=利润), 1=(售价-进货价=利润)")
    private String profitModel = StringPool.ONE;

    @ApiModelProperty("按销售管理分类默认扣点, 最大1, 1.4% = 0.14, profitModel=0必填, 不可大于0.29")
    private String defaultSaleTotalCategoryRate;

    @ApiModelProperty("是否开启钱包支付, 0-关闭, 1-开启")
    private String switchWalletPay;

    @ApiModelProperty("钱包充值支付平台, 字典wallet_pay_platform")
    private String walletPayPlatform;

    @ApiModelProperty("钱包充值软件商分佣比例, 最大1, 14% = 0.14, 最大0.29")
    private String walletSoftwareRate;

    @ApiModelProperty(value = "是否统一平台商业务员分账, 0-不开启,1-开启", notes = "需求背景是把所有业务员的提成在分账的时候, 全部分润到同一个商户号下")
    private String switchPartnerColonel;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;


}
