package com.zksr.promotion.api.coupon.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 优惠券模板对象 prm_coupon_template
 *
 * <AUTHOR>
 * @date 2024-03-31
 */
@ApiModel("优惠券模板 - prm_coupon_template分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ColonelAppPrmCouponTemplatePageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;


    /** 优惠券模板名称 */
    @Excel(name = "优惠券模板名称")
    @ApiModelProperty(value = "优惠券模板名称")
    private String couponName;

    /** 门店编号 */
    @Excel(name = "门店编号")
    @ApiModelProperty(value = "门店编号")
    private Long branchId;

    /** 状态 */
    @Excel(name = "活动状态")
    @ApiModelProperty(value = "活动状态 0=可发券,1=已失效")
    private Integer activityStatus;

    /** 优惠类型(数据字典);0-满减券  1-折扣券 */
    @Excel(name = "优惠类型(数据字典);0-满减券  1-折扣券")
    @ApiModelProperty(value = "业务员发券 默认传0 优惠类型(数据字典);0-满减券  1-折扣券 ")
    private Integer discountType;

    /** 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（返券规则表）3-新用户注册  4-门店积分兑换 5-业务员发券*/
    @Excel(name = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", readConverterExp = "返=券规则表")
    @ApiModelProperty(value = "业务员发券 默认传5 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（返券规则表）3-新用户注册  4-门店积分兑换 5-业务员发券")
    private Integer receiveType;

}