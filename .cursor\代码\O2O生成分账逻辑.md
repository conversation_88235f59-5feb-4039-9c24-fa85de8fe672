# O2O订单生成结算和分账功能实现说明

## 更新记录

### 2024-08-04 - 添加售后场景处理

修改了 `TrdSupplierAfterServiceImpl#createSettleAndAdvideFlowByO2O` 方法，添加了售后场景的处理：

#### 主要修改：

1. **查询售后数量**：
   - 在处理订单明细前，批量查询所有订单明细的售后数量
   - 使用 `trdSupplierAfterDtlMapper.getAfterDtlNumBySupplierDtlId()` 方法
   - 将结果转换为 Map 便于快速查找

2. **修改实际数量计算公式**：
   ```java
   // 原公式：实际数量 = 发货数量 - 拒收数量
   // 新公式：实际数量 = 发货数量 - 拒收数量 - 售后数量
   BigDecimal actualQty = sendQty.subtract(rejectQty).subtract(afterQty);
   ```

3. **添加安全检查**：
   - 确保实际数量不为负数，如果为负数则设置为0
   - 添加详细的警告日志记录
   - 修复除零错误（计算门店分润比例时）

#### 代码变更：

```java
// 查询所有订单明细的售后数量
List<Long> orderDtlIds = orderDtlList.stream()
    .map(TrdSupplierOrderDtl::getSupplierOrderDtlId)
    .collect(Collectors.toList());
List<OrderAfterDtlResDTO> afterDtlList = trdSupplierAfterDtlMapper.getAfterDtlNumBySupplierDtlId(orderDtlIds);
Map<Long, OrderAfterDtlResDTO> afterDtlMap = afterDtlList.stream()
    .collect(Collectors.toMap(OrderAfterDtlResDTO::getSupplierOrderDtlId, Function.identity()));

// 在循环中获取售后数量
BigDecimal afterQty = BigDecimal.ZERO;
OrderAfterDtlResDTO afterDtlResDTO = afterDtlMap.get(dtl.getSupplierOrderDtlId());
if (afterDtlResDTO != null && afterDtlResDTO.getSumReturnNum() != null) {
    afterQty = afterDtlResDTO.getSumReturnNum();
}

BigDecimal actualQty = sendQty.subtract(rejectQty).subtract(afterQty);
```

---

## 实现概述

已成功实现 `TrdOrderServiceImpl#orderO2OGenerateSettleDivideDtl` 方法，该方法用于根据指定条件查询需要生成分账的O2O订单，并为这些订单生成结算和分账记录。

## 实现的功能

### 1. 查询需要生成分账的订单

实现了复杂的SQL查询，包含以下条件：
- 签收时间距离当前时间已满8天且不超过30天
- 订单状态不是取消状态（已付款、货到付款未付款、货到付款已付款）
- 排除取消和拒收状态的订单
- 订单有实际数量（排除全部取消的订单）
- 在分账系统中没有分账记录
- 售后状态条件：没有售后或所有售后都已完成

### 2. 生成结算和分账记录

对于每个符合条件的订单：
- 调用现有的 `createO2OSettleAndDivide` 方法
- 生成 TrdSettle 结算记录
- 创建分账流水记录
- 更新订单结算状态

## 新增的文件

1. **O2OGenerateSettleOrderDTO.java** - 查询结果DTO
   - 包含入驻商订单编号和签收时间

2. **TrdSupplierOrderMapper.xml** - 新增SQL查询
   - `selectO2OOrdersForDivide` 方法实现复杂的多表关联查询

## 修改的文件

1. **TrdOrderServiceImpl.java**
   - 实现 `orderO2OGenerateSettleDivideDtl` 主方法
   - 添加 `processO2OOrderSettleAndDivide` 私有方法
   - 添加必要的依赖注入

2. **TrdSupplierOrderMapper.java**
   - 添加 `selectO2OOrdersForDivide` 方法声明
   - 添加必要的导入

3. **TrdSupplierOrderSaveReqVO.java**
   - 添加 @Builder 注解支持

## 方法调用流程

```
orderO2OGenerateSettleDivideDtl(paramVO)
  ↓
查询需要生成分账的订单 (selectO2OOrdersForDivide)
  ↓
遍历每个订单
  ↓
processO2OOrderSettleAndDivide(supplierOrderNo)
  ↓
调用 trdSupplierAfterService.createO2OSettleAndDivide()
  ↓
生成结算记录和分账流水
```

## 错误处理

- 对单个订单处理失败不会影响其他订单的处理
- 详细的日志记录便于问题排查
- 适当的异常处理和错误信息

## 使用方式

该方法通过定时任务调用，参数包括：
- `sysCode`: 系统编码
- `orderNos`: 可选的订单号列表
- `supplierOrderNos`: 可选的入驻商订单号列表

## 注意事项

1. 该实现复用了现有的结算和分账逻辑
2. 查询条件严格，确保只处理符合条件的订单
3. 支持批量处理，提高效率
4. 具有良好的容错性和可维护性
