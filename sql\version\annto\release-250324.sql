-- 后台/业务员APP：新增门店生命周期配置 新增表
-- 门店表新增生命周期字段
ALTER TABLE `zksr_member`.`mem_branch`
    ADD COLUMN `lifecycle_stage` tinyint(2) NULL COMMENT '生命周期阶段' AFTER `district_name`;

-- 门店生命周期拉链表
CREATE TABLE `zksr_member`.`mem_branch_lifecycle_zip` (
                                                          `branch_lifecycle_zip_id` bigint(20) NOT NULL COMMENT '门店生命周期拉链表ID',
                                                          `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                          `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                          `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                                          `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                          `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                                          `branch_id` bigint(20) NOT NULL COMMENT '门店id',
                                                          `last_lifecycle_stage` tinyint(2) DEFAULT NULL COMMENT '上一次生命周期code',
                                                          `lifecycle_stage` tinyint(2) NOT NULL COMMENT '生命周期code',
                                                          `start_date` datetime NOT NULL COMMENT '开始日期',
                                                          `end_date` datetime NOT NULL COMMENT '结束日期',
                                                          `start_memo` varchar(255) DEFAULT NULL COMMENT '触发事件备注',
                                                          PRIMARY KEY (`branch_lifecycle_zip_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店生命周期拉链表';


-- 后台/业务员APP：新增门店生命周期配置 新增数据字典信息
INSERT INTO `zksr_cloud`.`sys_dict_type`( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('门店生命周期', 'branch_lifecycle_stage_type', '0', 'zksr', '2025-03-14 15:04:28', 'zksr', '2025-03-14 15:06:17', '门店生命周期');

INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '新店', '1', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:06:49', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '活跃', '2', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:06:56', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '沉默', '3', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:07:02', '', NULL, NULL);
INSERT INTO `zksr_cloud`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '流失', '4', 'branch_lifecycle_stage_type', NULL, 'default', 'N', '0', 'zksr', '2025-03-14 15:07:10', '', NULL, NULL);

-- 后台/业务员APP：新增门店生命周期配置 新增菜单权限
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('门店生命周期配置', 2021, 12, 'UexvrBvxFwqgXMhBpt', '2021', 'storeCycle', 'operation/storeCycle/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'zksr', '2025-03-14 09:57:23', 'zksr', '2025-03-18 15:01:50', '', 'dc');
INSERT INTO `zksr-cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('客户状态记录', 2192, 5, 'XEArFqwejzulrJZEqT', '2192', 'shopInformation/customerStatusRecord', 'operation/shopInformation/customerStatusRecord/index', NULL, 1, 0, 'C', '0', '0', 'member:branchLifeCycleZip:list', '#', 'zksr', '2025-03-14 14:19:06', 'zksr', '2025-03-14 14:54:29', '', 'dc');
UPDATE `zksr_cloud`.`sys_menu` SET `func_scop` = 'partner,dc' WHERE `menu_code` = '2021';

-- 后台/业务员APP：新增门店生命周期配置 新增定时任务  需手动开启
INSERT INTO `xxl_job`.`xxl_job_info`(`job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES ( 2, '刷新门店生命周期', '2025-03-14 09:40:26', '2025-03-14 09:40:26', '蒋剑超', '', 'CRON', '0 00 4 * * ?', 'DO_NOTHING', 'FIRST', 'refreshBranchLifecycleZipHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-14 09:40:26', '', 0, 0, 0);

