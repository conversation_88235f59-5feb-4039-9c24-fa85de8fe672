package com.zksr.promotion.api.activity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 满赠规则对象 prm_fg_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FgRuleDTO extends GiveRuleDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /** 满赠规则 */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long fgRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 满赠活动id */
    @Excel(name = "满赠活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 满赠金额 */
    @Excel(name = "满赠金额")
    private BigDecimal fullAmt;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /**
     * 购买品项数(sku种类数)
     */
    @Excel(name = "购买品项数(sku种类数)")
    private Integer buySkuNum;

    /**
     * 赠送方式(默认为全赠 0仅一种，1任选，2全赠)
     */
    @Excel(name = "赠送方式(默认为全赠 0仅一种，1任选，2全赠)")
    private Integer giftGroupType;

    /**
     * 赠送单位数量
     */
    @Excel(name = "赠送单位数量")
    private Integer giftSkuUnitQty;
}
