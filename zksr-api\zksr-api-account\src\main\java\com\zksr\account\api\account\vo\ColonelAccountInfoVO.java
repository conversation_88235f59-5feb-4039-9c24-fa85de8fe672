package com.zksr.account.api.account.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/17 16:18
 */

@ApiModel(description = "业务员账户余额信息")
@Data
public class ColonelAccountInfoVO extends AccAccountRespVO{

    @ApiModelProperty("账户名称")
    private String accountName;

    @ApiModelProperty("银行卡号")
    private String accountNo;

    @ApiModelProperty("银行")
    private String bankBranch;

    @ApiModelProperty("已提现金额")
    private BigDecimal totalWithdrawAmt;
}
