package com.zksr.member.api.branch.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class BranchDeleteReqVO {


    @ApiModelProperty(value = "有订单的门店")
    private List<String> orderOnBranchNames;

    @ApiModelProperty(value = "有业务员的门店")
    private List<String> colonelOnBranchNames;

    @ApiModelProperty(value = "有渠道的门店")
    private List<String> channelOnBranchNames;

    @ApiModelProperty(value = "有全国分组的门店")
    private List<String> groupOnBranchNames;

    @ApiModelProperty(value = "成功删除的门店")
    private List<String> deleteBranchNames;

}
