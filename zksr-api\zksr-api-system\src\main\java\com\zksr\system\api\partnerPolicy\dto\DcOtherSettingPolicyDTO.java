package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 运营商其他配置
 * @Author: ch<PERSON><PERSON>qing
 * @Date: 2024/12/11 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DcOtherSettingPolicyDTO {

    @ApiModelProperty("钱包充值平台商分佣比例, 最大1, 14% = 0.14, 最大0.29")
    private String walletPartnerRate;

    @ApiModelProperty("运营商展示分类一级分类展示在顶部或左侧, 1顶部2左侧")
    private String classShow;

    @ApiModelProperty("配置名称")
    private String configName = "运营商其他配置";

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType = "22";
}
