package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信b2b支付配置
 * @date 2024/8/9 17:40
 */
@Data
@ApiModel(description = "微信b2b支付配置")
public class WxB2bPayConfigDTO {

    /**
     * appKey
     */
    @ApiModelProperty("商户支付秘钥")
    private String appKey;

    /**
     * 微信支付商户ID
     */
    @ApiModelProperty("收款商户")
    private String mchid;

    /**
     * 支付回调解密验证key
     */
    @ApiModelProperty("支付回调解密验证key")
    private String encodingAESKey;
}
