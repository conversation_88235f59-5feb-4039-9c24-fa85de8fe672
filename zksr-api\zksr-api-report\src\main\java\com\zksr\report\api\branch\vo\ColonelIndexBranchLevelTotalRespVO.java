package com.zksr.report.api.branch.vo;

import com.zksr.common.core.enums.branch.BranchTagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员首页数据报表统计,
 * @date 2024/11/25 9:59
 */
@Data
@ApiModel(description = "业务员首页数据报表统计 - response")
public class ColonelIndexBranchLevelTotalRespVO {

    @ApiModelProperty("客户总数")
    private Long totalBranchNum;

    @ApiModelProperty("总门店销售额")
    private BigDecimal totalBranchSaleAmt;

    @ApiModelProperty("总活跃门店数")
    private Long activeBranchNum;

    @ApiModelProperty("销售提成")
    private BigDecimal profit;

    @ApiModelProperty("销售毛利率%")
    private BigDecimal profitOdd;

    @ApiModelProperty("门店用户等级数量")
    private List<LevelTag> levelTagList = new ArrayList<>();

    @Data
    @ApiModel(description = "等级标签")
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LevelTag {

        @ApiModelProperty("标签类型")
        private BranchTagEnum branchTag;

        @ApiModelProperty("标签门店数量")
        private Long branchTagVal;

        @ApiModelProperty("销售额")
        private BigDecimal saleAmt;
    }
}
