package com.zksr.promotion.api.couponBatch.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/7 9:35
 * @注释
 */
@Data
public class CouponBatchDtlDTO {

    @ApiModelProperty(value = "0-优惠券模板 1-门店")
    private Long prmCouponBatchDtlId;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "优惠券批量发送id")
    private Long batchCouponId;

    @ApiModelProperty(value = "门店id")
    private Long branchId;

    @ApiModelProperty(value = "优惠券模板")
    private Long couponTemplateId;

    @ApiModelProperty(value = "0-优惠券模板 1-门店")
    private Integer scopeType;
}
