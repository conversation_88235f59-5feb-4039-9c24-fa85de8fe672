package com.zksr.system.api.group;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.group.dto.GroupDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteGroupApi",
        value = ApiConstants.NAME
)

/**
*
 * 全国城市分组服务
* <AUTHOR>
* @date 2024/3/22 17:17
*/
public interface GroupApi {

    String PREFIX = ApiConstants.PREFIX + "/group";

    @GetMapping(PREFIX + "/getByGroupId")
    public CommonResult<GroupDTO> getByGroupId(@RequestParam("groupId") Long groupId);

    /**
     * 根据平台商ID查询全国城市分组信息Map集合
     * @param sysCode 平台商ID
     * @return
     */
    @GetMapping(PREFIX + "/getGroupListBySysCode")
    public CommonResult<Map<Long, GroupDTO>> getGroupListBySysCode(@RequestParam(value = "sysCode",required = false) Long sysCode);

    /**
     * 获取全国分组默认第一个
     * @return
     */
    @GetMapping(PREFIX + "/getDefaultGrouping")
    CommonResult<GroupDTO> getDefaultGrouping();

    /**
     * 根据平台商ID查询全国城市分组信息List集合
     * @param sysCode 平台商ID
     * @return
     */
    @GetMapping(PREFIX + "/getListBySysCode")
    CommonResult<List<GroupDTO>> getListBySysCode(@RequestParam(value = "sysCode",required = false) Long sysCode);
}
