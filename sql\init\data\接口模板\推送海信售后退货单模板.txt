{
    "refundOrder": {
        "orderCode": "${supplierOrderNo}",
        "supplierId": "${supplierId}",
        "supplierName": "${supplierName}",
        "createTime": "${createTimeString}",
        "shopId": "${branchId}",
        "shopName": "${branchName}",
        #if("$!{memo}")
        "reason": "${memo}",
        #end
        "refundNo": "${supplierAfterNo}"
    },
    "refundSkuOrders": [
        #foreach( $sub in $detailList)
        {
            #set($qty = $sub.returnUnitQty)
            "erpItemNo": "${sub.itemSourceNo}",
            "quantity": ${qty},
            "skuCode": "${sub.skuId}",
            "skuName": "${sub.spuName}"
        }#if($foreach.hasNext),#end
        #end
    ]
}