package com.zksr.promotion.api.activity.dto;

import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动验证商品
 * @date 2024/5/18 9:58
 */
@Data
@ApiModel(description = "促销活动验证商品结果")
public class ActivityVerifyResultDTO {

    @ApiModelProperty(value = "命中共享促销活动", notes = "如果一个促销有两个商品都参与了就会在这个集合里面")
    private List<ActivityLabelInfoVO> activityList = new ArrayList<>();

    @ApiModelProperty("活动和商品促销的关系")
    private Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> activityProductRelation;

    @ApiModelProperty("活动商品")
    List<ActivityVerifyItemDTO> verifyItemList;

    @ApiModelProperty("经过促销活动计算需要优惠的金额")
    private BigDecimal totalCouponAmt = BigDecimal.ZERO;

    @ApiModelProperty("最佳绑定类型, 商品绑定关系")
    private Map<String, Map<ActivityVerifyItemDTO, SupplierActivityDTO>> optimumRelation;
}
