package com.zksr.product.api.supplierClass.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.SysTimeTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.logging.Log;

import java.util.Objects;
import java.util.function.Function;

@Data
@ApiModel("入驻商-平台商管理分类 关联关系 - PrdtSupplierClassRespDTO")
public class SupplierClassRespDTO {
    @ApiModelProperty(value = "平台商管理分类id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long catgoryId;

    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "管理分类名称")
    private String catgoryName;

    @ApiModelProperty(value = "管理分类名称")
    private String pidName;

    @ApiModelProperty(value = "管理分类状态（数据字典 sys_common_status）")
    private Integer status;

    /**
     * 1：是 0：否
     */
    @ApiModelProperty(value = "是否可售后 （数据字典 sys_status_type）")
    private Integer isAfterSales;

    @ApiModelProperty(value = "售后时间类型（数据字典 sys_time_type）")
    private Integer afterSalesTimeType;

    @ApiModelProperty(value = "可售后时间")
    private Integer afterSalesTime;


    /**
     * 去重验证key
     * @return
     */
    public String getDistinctKey() {
        return StringUtils.format("{}_{}", supplierId, catgoryId);
    }


    public Long getAfterSalesTimeMinute(Long orderFinishTime) {
        // 设置为不可售后时，售后时长默认为0
        if (Objects.equals(isAfterSales, NumberPool.INT_ZERO)) {
            return NumberPool.LONG_ZERO;
        }
        // 当售后时间类型 或 可售后时间 为空，返还运营商设置的订单完成时间
        if (ToolUtil.isEmpty(afterSalesTime) || ToolUtil.isEmpty(afterSalesTimeType)) {
            return orderFinishTime;
        }
        // 获取售后时间（分钟）
        Integer afterSalesTimeMinute = SysTimeTypeEnum.getAfterSalesTimeMinute(afterSalesTimeType, afterSalesTime);
        // 如果订单完成时间 大于 入驻商配置的售后时间，则返回售后时间，否则返回订单完成时间
        return orderFinishTime >= afterSalesTimeMinute ? afterSalesTimeMinute : orderFinishTime;
    }
}
