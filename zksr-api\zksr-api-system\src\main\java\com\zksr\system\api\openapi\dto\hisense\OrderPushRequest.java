package com.zksr.system.api.openapi.dto.hisense;

import java.math.BigDecimal;
import java.util.List;

// 主订单类
public class OrderPushRequest {
    private ShopOrder shopOrder;
    private Receiver receiver;
    private List<SkuOrder> skuOrders;
    private Invoice invoice;

    // Getters and Setters
    public ShopOrder getShopOrder() {
        return shopOrder;
    }

    public void setShopOrder(ShopOrder shopOrder) {
        this.shopOrder = shopOrder;
    }

    public Receiver getReceiver() {
        return receiver;
    }

    public void setReceiver(Receiver receiver) {
        this.receiver = receiver;
    }

    public List<SkuOrder> getSkuOrders() {
        return skuOrders;
    }

    public void setSkuOrders(List<SkuOrder> skuOrders) {
        this.skuOrders = skuOrders;
    }

    public Invoice getInvoice() {
        return invoice;
    }

    public void setInvoice(Invoice invoice) {
        this.invoice = invoice;
    }
}

// 商店订单类
class ShopOrder {
    private Long supplierId;
    private String payTime;
    private String supplierName;
    private Long orderCode;
    private String buyerNote;
    private String shopId;
    private String shopName;
    private BigDecimal transAmt;
    private BigDecimal subDiscountAmt;
    private BigDecimal subPayAmt;
    private BigDecimal subOrderAmt;

    // Getters and Setters
    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Long getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(Long orderCode) {
        this.orderCode = orderCode;
    }

    public String getBuyerNote() {
        return buyerNote;
    }

    public void setBuyerNote(String buyerNote) {
        this.buyerNote = buyerNote;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public BigDecimal getTransAmt() {
        return transAmt;
    }

    public void setTransAmt(BigDecimal transAmt) {
        this.transAmt = transAmt;
    }

    public BigDecimal getSubDiscountAmt() {
        return subDiscountAmt;
    }

    public void setSubDiscountAmt(BigDecimal subDiscountAmt) {
        this.subDiscountAmt = subDiscountAmt;
    }

    public BigDecimal getSubPayAmt() {
        return subPayAmt;
    }

    public void setSubPayAmt(BigDecimal subPayAmt) {
        this.subPayAmt = subPayAmt;
    }

    public BigDecimal getSubOrderAmt() {
        return subOrderAmt;
    }

    public void setSubOrderAmt(BigDecimal subOrderAmt) {
        this.subOrderAmt = subOrderAmt;
    }
}

// 收货人类
 class Receiver {
    private String mobile;
    private Long userId;
    private String receiveUserName;
    private String detail;
    private String region;
    private String regionCode;
    private String province;
    private String provinceCode;
    private String city;
    private String cityCode;
    private String street;
    private String streetCode;

    // Getters and Setters
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getStreetCode() {
        return streetCode;
    }

    public void setStreetCode(String streetCode) {
        this.streetCode = streetCode;
    }
}

// SKU订单类
 class SkuOrder {
    private Long erpItemNo;
    private Integer lineNum;
    private Integer quantity;
    private BigDecimal orderUnitPrice;
    private BigDecimal originFee;
    private BigDecimal discount;
    private String skuCode;
    private String skuName;

    // Getters and Setters
    public Long getErpItemNo() {
        return erpItemNo;
    }

    public void setErpItemNo(Long erpItemNo) {
        this.erpItemNo = erpItemNo;
    }

    public Integer getLineNum() {
        return lineNum;
    }

    public void setLineNum(Integer lineNum) {
        this.lineNum = lineNum;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getOrderUnitPrice() {
        return orderUnitPrice;
    }

    public void setOrderUnitPrice(BigDecimal orderUnitPrice) {
        this.orderUnitPrice = orderUnitPrice;
    }

    public BigDecimal getOriginFee() {
        return originFee;
    }

    public void setOriginFee(BigDecimal originFee) {
        this.originFee = originFee;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
}

// 发票类
class Invoice {
    private String phoneNumber;
    private String title;
    private Integer type;
    private Integer titleType;
    private String companyName;
    private String taxRegisterNo;
    private String registerAddress;
    private String registerPhone;
    private String registerBank;
    private String bankAccount;

    // Getters and Setters
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getTitleType() {
        return titleType;
    }

    public void setTitleType(Integer titleType) {
        this.titleType = titleType;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getTaxRegisterNo() {
        return taxRegisterNo;
    }

    public void setTaxRegisterNo(String taxRegisterNo) {
        this.taxRegisterNo = taxRegisterNo;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getRegisterPhone() {
        return registerPhone;
    }

    public void setRegisterPhone(String registerPhone) {
        this.registerPhone = registerPhone;
    }

    public String getRegisterBank() {
        return registerBank;
    }

    public void setRegisterBank(String registerBank) {
        this.registerBank = registerBank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }
}