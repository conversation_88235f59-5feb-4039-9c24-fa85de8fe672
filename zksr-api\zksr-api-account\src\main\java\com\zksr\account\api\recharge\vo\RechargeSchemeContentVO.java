package com.zksr.account.api.recharge.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import java.math.BigDecimal;


/**
 * 储值充值套餐内容
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Data
@ApiModel("储值充值套餐配置 - acc_recharge_scheme content VO")
public class RechargeSchemeContentVO {

    @ApiModelProperty(value = "充值金额", required = true, example = "0.01")
    @DecimalMin(value = "0.01", message = "至少充值1分钱")
    private BigDecimal rechargeAmt;

    @ApiModelProperty(value = "赠送金额", required = true, example = "0.00")
    @DecimalMin(value = "0.00", message = "赠送金额不能为负数")
    private BigDecimal giveAmt;
}
