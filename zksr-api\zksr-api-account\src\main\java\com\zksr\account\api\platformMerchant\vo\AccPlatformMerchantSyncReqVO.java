package com.zksr.account.api.platformMerchant.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("支付平台商户 - 同步商户请求")
public class AccPlatformMerchantSyncReqVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商户类型
     * 参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty("商户类型: partner-平台商, dc-运营商, supplier-入驻商, colonel-业务员")
    private String merchantType;

    /**
     * 商户ID
     */
    @ApiModelProperty("商户ID: sysCode, dcId, supplierId, colonelId")
    private Long merchantId;

    /**
     * 支付平台信息
     */
    @ApiModelProperty("支付平台信息: hlb-合利宝, mideaPay-美的支付, mock-模拟支付")
    private String platform;

    /**
     * 平台商ID
     */
    @ApiModelProperty("平台商ID")
    private Long sysCode;
}
