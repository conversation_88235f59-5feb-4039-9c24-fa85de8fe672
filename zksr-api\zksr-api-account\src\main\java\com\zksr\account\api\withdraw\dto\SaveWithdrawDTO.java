package com.zksr.account.api.withdraw.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 增加提现单
 * @date 2024/4/15 9:12
 */
@Data
@ApiModel("账户提现单 - acc_withdraw 保存 VO")
public class SaveWithdrawDTO {
    /**
     * 商户类型
     * 参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "商户类型", hidden = true)
    private String merchantType;

    /** 账户ID */
    @ApiModelProperty(value = "账户ID", required = true)
    private Long accountId;

    /** 账户ID */
    @ApiModelProperty(value = "商户ID", hidden = true, required = true, notes = "会验证账户是否属于这个商户")
    private Long merchantId;

    /** 提现金额 */
    @ApiModelProperty(value = "提现金额, 最低0.01元", required = true)
    @NotNull
    @DecimalMin(value = "0.01", message = "最低提现0.01元")
    private BigDecimal withdrawAmt;
}
