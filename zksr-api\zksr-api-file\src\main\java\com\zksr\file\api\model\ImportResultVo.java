package com.zksr.file.api.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImportResultVo {
    @ApiModelProperty("失败文件信息")
    private SysFile sysFile;
    @ApiModelProperty("总数")
    private Integer total;
    @ApiModelProperty("成功数量")
    private Integer successCount;
    @ApiModelProperty("失败数量")
    private Integer failCount;
    @ApiModelProperty("返回消息")
    private String msg;
}