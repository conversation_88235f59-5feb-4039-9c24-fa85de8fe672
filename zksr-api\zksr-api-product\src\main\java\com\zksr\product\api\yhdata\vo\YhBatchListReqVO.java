
package com.zksr.product.api.yhdata.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:50
 */
@Accessors(chain = true)
@Data
public class YhBatchListReqVO extends PageParam {

    @ApiModelProperty(value = "门店ID", hidden = true)
    private Long branchId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "三级展示分类ID")
    private Long threeSaleClassId;

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @JsonFormat(pattern = YYYY_MM_DD, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "批次日期")
    private Date batchDate;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty("是否选中, 1-选中, 2-未选中")
    private Integer checked;

    @ApiModelProperty("0-全量, 1-仅计算数据")
    private Integer searchMode = 0;
}
