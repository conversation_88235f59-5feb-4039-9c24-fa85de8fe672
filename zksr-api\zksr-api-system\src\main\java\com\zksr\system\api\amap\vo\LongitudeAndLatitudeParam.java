package com.zksr.system.api.amap.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  地址经纬度查询请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LongitudeAndLatitudeParam {

    /**
     * 用户在高德地图官网申请Web服务API类型Key
     */
    @ApiModelProperty("高德Key")
    private String key;


    /**
     * 地址
     */
    @ApiModelProperty("address")
    private String address;
}
