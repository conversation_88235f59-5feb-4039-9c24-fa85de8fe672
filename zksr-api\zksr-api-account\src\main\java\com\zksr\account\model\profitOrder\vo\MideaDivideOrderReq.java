package com.zksr.account.model.profitOrder.vo;

import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
* @description: 分账请求
* @author: 陈永培
* @createtime: 2025/6/23 19:30
*/
@Data
@Accessors(chain = true)
@ToString
public class MideaDivideOrderReq {
    
    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;
    
    /** 支付平台(数据字典) */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;
    
    @ApiModelProperty("商户分账订单号")
    private String outProfitNo;
    
    @ApiModelProperty("原商户交易订单号")
    private String outTradeNo;
    
    @ApiModelProperty("分账详情，json字符串（支持多级嵌套对象）")
    private List<ProfitOrderDetailDTO> profitOrderDetails;
    
    @ApiModelProperty("商户附言")
    private String attach;
}
