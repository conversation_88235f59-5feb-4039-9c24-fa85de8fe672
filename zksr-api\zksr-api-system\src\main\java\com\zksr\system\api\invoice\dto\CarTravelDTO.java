package com.zksr.system.api.invoice.dto;

import lombok.Data;

/**
 * @Description: 行程单详情DTO（滴滴行程单信息）
 * @Date: 2025/07/16
 */
@Data
public class CarTravelDTO {

    /**
     * 车型
     */
    private String carType;

    /**
     * 上车时间
     * 示例: 01-10 23:05 周五
     */
    private String timeGeton;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 起点
     */
    private String stationGeton;

    /**
     * 终点
     */
    private String stationGetoff;

    /**
     * 里程
     */
    private String mileage;

    /**
     * 金额
     */
    private String amount;
}
