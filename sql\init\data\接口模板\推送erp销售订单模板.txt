{
    #set($deliveryType = $detailList.get(0))
    #if($deliveryType)
    "b2bDeliveryType": ${deliveryType.itemType},
    #else
    "b2bDeliveryType": 0,
    #end
    "b2bSheetNo": "${supplierOrderNo}",
    "b2bTransNo": "${orderNo}",
    "remark": "${memo}",
    #if($payWay == 0)
    "collectedWay": "ZXZF",
    "pendingVerifiedAmt": ${realSaleAmt},
    #elseif($payWay == 2)
    "collectedWay": "YWSK",
    #else
    "collectedWay": "",
    #end
    "consumerNo": "${branchNo}",
    "sheetAmt":"${realOrderAmt}",
    "beforeDiscountAmt": "${subOrderAmt}",
    "discountAmt": "${subDiscountAmt}",
    "colonelName": "${colonelName}",
    "addressList": [
        {
            "contactMan": "${contactName}",
            "telephoneNum": "${contactPhone}",
            "shippingAddress": "${branchAddr}"
        }
    ],
    "stockShortFlag": ${stockShortFlag},
    "subList": [
        #foreach( $sub in $detailList)
        {
            #set($qty = $sub.orderUnitQty)
            #set($size = $sub.orderUnitSize)
            #set($price = $sub.exactPrice)
            #set($itemPrice = $size * $price)
            #set($minDetailQty = $qty * $size)
            #set($dtlDiscountAmt = $sub.subOrderAmt-$sub.totalAmt)
            "itemNo": "${sub.itemSourceNo}",
            "realQty": ${qty},
            "detailQty": ${qty},
            "itemBarcode": "${sub.itemBarcode}",
            #if($sub.orderUnitType == 1)
            "packageType": "0",
            #elseif($sub.orderUnitType == 2)
            "packageType": "1",
            #else
            "packageType": "2",
            #end
            "itemType": "${sub.giftFlag}",
            "lineNo": ${sub.lineNum},
            "itemPrice": ${itemPrice},
            "minDetailQty": ${minDetailQty},
            "detailPrice": ${sub.totalAmt}
            "detailPrice": ${sub.totalAmt},
            "beforeDiscountAmt": ${sub.subOrderAmt},
            "beforeDiscountPrice": ${sub.orderUnitPrice},
            "discountAmt": ${dtlDiscountAmt},
            "stockShortFlag": ${sub.stockShortFlag}
        }#if($foreach.hasNext),#end
        #end
    ]
}