package com.zksr.system.api.fileImport.vo;

import com.zksr.system.api.domain.SysFileImportDtl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FileImportHandlerVo {

    @ApiModelProperty("成功数量")
    private Integer successNum;

    @ApiModelProperty("失败数量")
    private Integer failureNum;

    @ApiModelProperty("总数")
    private Integer totalNum;

    @ApiModelProperty("状态 0 成功 1 失败")
    private Integer status;

    private String msg;

    private StringBuffer stringBuffer;



    private List<SysFileImportDtl> list;

    public FileImportHandlerVo(){
        this.successNum = 0;
        this.failureNum = 0;
        this.totalNum = 0;
        stringBuffer = new StringBuffer();
    }

}
