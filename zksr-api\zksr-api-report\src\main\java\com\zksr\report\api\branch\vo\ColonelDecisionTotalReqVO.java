package com.zksr.report.api.branch.vo;

import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员门店等级数量 page request
 * @date 2024/11/23 9:07
 */
@Data
@ApiModel(description = "业务员门店等级数量 page request")
public class ColonelDecisionTotalReqVO {

    @ApiModelProperty("人员类型: 0-默认(全部下级), 1-指定下级")
    private Integer totalType;

    @ApiModelProperty("指定业务员数据")
    private Long colonelId;

    @ApiModelProperty("月份格式yyyy-MM")
    private String monthDate;

    public String getYear() {
        return monthDate.split("-")[0];
    }

    public String getMonth() {
        return monthDate.split("-")[1];
    }

    public boolean assign() {
        return Objects.nonNull(totalType) && totalType == 1;
    }

    public Integer getMonthId() {
        return Integer.parseInt(getMonthDate().replace(StringPool.DASH, StringPool.EMPTY));
    }
}
