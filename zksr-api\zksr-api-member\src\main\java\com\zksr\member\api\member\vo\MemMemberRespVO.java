package com.zksr.member.api.member.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 用户信息对象 mem_member
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Data
@ApiModel("用户信息 - mem_member Response VO")
@NoArgsConstructor
@AllArgsConstructor
public class MemMemberRespVO {
    private static final long serialVersionUID = 1L;

    /** 用户id;用户id */
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 用户手机号;用户手机号 */
    @Excel(name = "用户手机号;用户手机号")
    @ApiModelProperty(value = "用户手机号;用户手机号")
    private String memberPhone;

    /** 用户名;用户名 */
    @Excel(name = "用户名;用户名")
    @ApiModelProperty(value = "用户名;用户名")
    private String memberName;

    /** 微信unionid */
    @Excel(name = "微信unionid")
    @ApiModelProperty(value = "微信unionid")
    private String wxUnionid;

    /** 头像 */
    @Excel(name = "头像")
    @ApiModelProperty(value = "头像")
    private String avatar;

    /** 状态：1正常  0禁用 */
    @Excel(name = "状态：1正常  0禁用")
    @ApiModelProperty(value = "状态：1正常  0禁用")
    private Integer status;

    /** 小程序openid */
    @Excel(name = "小程序openid")
    @ApiModelProperty(value = "小程序openid")
    private String xcxOpenid;

    /** 注册业务员id */
    @Excel(name = "注册业务员id")
    @ApiModelProperty(value = "注册业务员id")
    private Long registerColonelId;

    /** 登录token */
    @Excel(name = "登录token")
    @ApiModelProperty(value = "登录token")
    private String loginToken;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;


    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 门店编号集合 */
    @ApiModelProperty(value = "门店编号集合")
    private List<String> branchIds;

    /** 最后一次登录时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "最后一次登录时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "最后一次登录时间")
    private Date lastLoginTime;

    /** 用户账号 */
    @Excel(name = "用户账号")
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 用户密码 */
    @Excel(name = "用户密码")
    @ApiModelProperty(value = "用户密码")
    @JsonIgnore
    private String password;

    /** 是否为店长用户 */
    @Excel(name = "是否为店长用户")
    @ApiModelProperty(value = "是否为店长用户 0否 1是")
    private Integer isShopManager;

    /** 父ID */
    @ApiModelProperty(value = "父ID")
    @Excel(name = "父ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    public MemMemberRespVO(Long pid) {
        this.pid = pid;
    }
}
