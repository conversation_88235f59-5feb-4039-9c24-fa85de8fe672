package com.zksr.account.api.account.dto;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户
 * @date 2024/3/23 11:15
 */
@Data
@ApiModel(description = "账户数据传递")
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccAccountDTO {

    /** 账户id */
    @TableId(type = IdType.AUTO)
    private Long accountId;

    /** 平台商id */
    @ApiModelProperty("平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty("商户类型")
    private String merchantType;

    /** 商户id */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /** 可提现金额 */
    @ApiModelProperty("可提现金额")
    private BigDecimal withdrawableAmt;

    /** 冻结金额 */
    @ApiModelProperty("冻结金额")
    private BigDecimal frozenAmt;

    /** 授信额度 */
    @ApiModelProperty("授信额度")
    private BigDecimal creditAmt;

    /** 支付平台(数据字典);当merchant_type=branch时，无此值
     * {@link com.zksr.common.core.enums.PayChannelEnum}
     * */
    @ApiModelProperty("支付平台(数据字典);当merchant_type=branch时，无此值")
    private String platform;

    @ApiModelProperty("第三方支付平台商户信息")
    private AccPlatformAccountDTO platformAccount;

    /**
     * 获取有效金额
     * @return
     */
    public BigDecimal getValidAccountAmt() {
        if (Objects.nonNull(withdrawableAmt) && Objects.nonNull(frozenAmt) && Objects.nonNull(creditAmt)) {
            return  withdrawableAmt.subtract(frozenAmt).add(creditAmt);
        }
        return BigDecimal.ZERO;
    }
    public boolean debt() {
        if (Objects.isNull(withdrawableAmt) || Objects.isNull(frozenAmt) || Objects.isNull(creditAmt)) {
            return true;
        }
        // 账户授信额度
        BigDecimal validBalance = withdrawableAmt.subtract(frozenAmt).add(creditAmt);
        return NumberUtil.isGreater(BigDecimal.ZERO, validBalance);
    }
}
