ALTER TABLE `zksr-product`.`prdt_supplier_item`
    ADD COLUMN `min_shelf_status` tinyint(1) NULL DEFAULT 0 COMMENT '小单位上架状态(0-未上架, 1-已上架)' AFTER `shelf_date`,
    ADD COLUMN `mid_shelf_status` tinyint(1) NULL DEFAULT 0 COMMENT '中单位上架状态(0-未上架, 1-已上架)' AFTER `min_shelf_status`,
    ADD COLUMN `large_shelf_status` tinyint(1) NULL DEFAULT 0 COMMENT '大单位上架状态(0-未上架, 1-已上架)' AFTER `mid_shelf_status`;

ALTER TABLE `zksr-product`.`prdt_area_item`
    ADD COLUMN `min_shelf_status` tinyint(1) NULL DEFAULT 0 COMMENT '小单位上架状态(0-未上架, 1-已上架)' AFTER `shelf_date`,
    ADD COLUMN `mid_shelf_status` tinyint(1) NULL DEFAULT 0 COMMENT '中单位上架状态(0-未上架, 1-已上架)' AFTER `min_shelf_status`,
    ADD COLUMN `large_shelf_status` tinyint(1) NULL DEFAULT 0 COMMENT '大单位上架状态(0-未上架, 1-已上架)' AFTER `mid_shelf_status`;

ALTER TABLE `zksr-product`.`prdt_spu`
    ADD COLUMN `min_unit` varchar(16) NULL COMMENT '小单位, 具体单位数据字典' AFTER `memo`,
    ADD COLUMN `mid_unit` varchar(16) NULL COMMENT '中单位,具体单位数据字典' AFTER `min_unit`,
    ADD COLUMN `large_unit` varchar(16) NULL COMMENT '大耽误,具体单位数据字段' AFTER `mid_unit`,
    ADD COLUMN `mid_size` int(10) NULL COMMENT '中单位转小单位比例, 一个中单位等于多少个小单位' AFTER `large_unit`,
    ADD COLUMN `large_size` int(10) NULL COMMENT '大单位转小单位比例, 一个大单位等于多少个小单位' AFTER `mid_size`;

ALTER TABLE `zksr-product`.`prdt_sku`
    ADD COLUMN `mid_barcode` varchar(64) NULL COMMENT '中单位条码' AFTER `max_oq`,
    ADD COLUMN `mid_mark_price` decimal(12, 2) NULL COMMENT '中单位标准价' AFTER `mid_barcode`,
    ADD COLUMN `mid_cost_price` decimal(12, 2) NULL COMMENT '中单位成本价' AFTER `mid_mark_price`,
    ADD COLUMN `mid_suggest_price` decimal(12, 2) NULL COMMENT '中单位零售价' AFTER `mid_cost_price`,
    ADD COLUMN `mid_min_oq` bigint(8) NULL COMMENT '中单位最小订购' AFTER `mid_suggest_price`,
    ADD COLUMN `mid_jump_oq` bigint(8) NULL COMMENT '中单位订货组' AFTER `mid_min_oq`,
    ADD COLUMN `mid_max_oq` bigint(8) NULL COMMENT '中单位最大限购' AFTER `mid_jump_oq`,
    ADD COLUMN `large_barcode` varchar(64) NULL COMMENT '大单位国际条码' AFTER `mid_max_oq`,
    ADD COLUMN `large_mark_price` decimal(12, 2) NULL COMMENT '大单位标准价' AFTER `large_barcode`,
    ADD COLUMN `large_cost_price` decimal(12, 2) NULL COMMENT '大单位成本价' AFTER `large_mark_price`,
    ADD COLUMN `large_suggest_price` decimal(12, 2) NULL COMMENT '大单位零售价' AFTER `large_cost_price`,
    ADD COLUMN `large_min_oq` bigint(8) NULL COMMENT '大单位最小订购' AFTER `large_suggest_price`,
    ADD COLUMN `large_jump_oq` bigint(8) NULL COMMENT '大单位订货组' AFTER `large_min_oq`,
    ADD COLUMN `large_max_oq` bigint(8) NULL COMMENT '大单位最大限购' AFTER `large_jump_oq`;

ALTER TABLE `zksr-product`.`prdt_sku_price`
    ADD COLUMN `mid_sale_price1` decimal(10, 2) NULL DEFAULT NULL COMMENT '中单位-销售价1' AFTER `type`,
    ADD COLUMN `mid_sale_price2` decimal(10, 2) NULL DEFAULT NULL COMMENT '中单位-销售价2' AFTER `mid_sale_price1`,
    ADD COLUMN `mid_sale_price3` decimal(10, 2) NULL DEFAULT NULL COMMENT '中单位-销售价3' AFTER `mid_sale_price2`,
    ADD COLUMN `mid_sale_price4` decimal(10, 2) NULL DEFAULT NULL COMMENT '中单位-销售价4' AFTER `mid_sale_price3`,
    ADD COLUMN `mid_sale_price5` decimal(10, 2) NULL DEFAULT NULL COMMENT '中单位-销售价5' AFTER `mid_sale_price4`,
    ADD COLUMN `mid_sale_price6` decimal(10, 2) NULL DEFAULT NULL COMMENT '中单位-销售价6' AFTER `mid_sale_price5`,
    ADD COLUMN `large_sale_price1` decimal(10, 2) NULL DEFAULT NULL COMMENT '大单位-销售价1' AFTER `mid_sale_price6`,
    ADD COLUMN `large_sale_price2` decimal(10, 2) NULL DEFAULT NULL COMMENT '大单位-销售价2' AFTER `large_sale_price1`,
    ADD COLUMN `large_sale_price3` decimal(10, 2) NULL DEFAULT NULL COMMENT '大单位-销售价3' AFTER `large_sale_price2`,
    ADD COLUMN `large_sale_price4` decimal(10, 2) NULL DEFAULT NULL COMMENT '大单位-销售价4' AFTER `large_sale_price3`,
    ADD COLUMN `large_sale_price5` decimal(10, 2) NULL DEFAULT NULL COMMENT '大单位-销售价5' AFTER `large_sale_price4`,
    ADD COLUMN `large_sale_price6` decimal(10, 2) NULL DEFAULT NULL COMMENT '大单位-销售价6' AFTER `large_sale_price5`;

ALTER TABLE `zksr-product`.`prdt_spu`
    ADD COLUMN `is_linkage` tinyint(1) NULL COMMENT '是否开启联动换算 1-是 0-否' AFTER `large_size`;
ALTER TABLE `zksr-product`.`prdt_spu`
    ADD COLUMN `expiration_date` int(10) NULL COMMENT '保质期(天)' AFTER `is_linkage`;



