package com.zksr.account.api.platformMerchant.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.io.Serializable;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("支付平台商户 - acc_platform_merchant Response VO")
public class AccPlatformMerchantRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "商户名称")
    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /** 支付平台商户id */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long platformMerchantId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型 partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 ")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    @ApiModelProperty(value = "分账方商户编号")
    private String altMchNo;

    /** 分账方名称 */
    @Excel(name = "分账方名称")
    @ApiModelProperty(value = "分账方名称")
    private String altMchName;

    /** 0-停用  1-启用 */
    @Excel(name = "0-停用  1-启用")
    private String mchStatus;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台")
    private String platform;

    /** 银行名称 */
    @Excel(name = "银行名称")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 银行支行 */
    @Excel(name = "银行支行")
    @ApiModelProperty(value = "银行支行")
    private String bankBranch;

    /** 银行卡号 */
    @Excel(name = "银行卡号")
    @ApiModelProperty(value = "银行卡号")
    private String accountNo;

    /** 银行户名 */
    @Excel(name = "银行户名")
    @ApiModelProperty(value = "银行卡持有人名称")
    private String accountName;

    /** 支付平台进件单号 */
    @ApiModelProperty(value = "支付平台进件单号")
    private String thirdOrderNo;

    /**
     * 子商户类型
     */
    @ApiModelProperty("子商户类型,0-个人, 1-个人工商, 2-企业")
    private String busiMerchantType;

    /** 结算卡类型 */
    @ApiModelProperty(value = "结算卡类型TOPRIVATE-对私,TOPUBLIC-对公")
    private String bankType;

    /** 审核状态(字典:platform_merchant_audit_status): INIT-待审核, OVERRULE-驳回, AUDITED-审核通过, AUDITING-审核中, ACCOUNT_NEED_VERIFY-待账户验证, UNSIGNED-账户待签约 */
    @ApiModelProperty("审核状态(字典:platform_merchant_audit_status): INIT-待审核, OVERRULE-驳回, AUDITED-审核通过, AUDITING-审核中, ACCOUNT_NEED_VERIFY-待账户验证, UNSIGNED-账户待签约")
    private String auditStatus;

    /** 身份证 */
    @ApiModelProperty("身份证")
    private String idCard;

    /** 联行号 */
    @ApiModelProperty("联行号")
    private String bankChannelNo;

    /** 联系人名称 */
    @ApiModelProperty("联系人名称")
    private String contractName;

    /** 联系人手机号 */
    @ApiModelProperty("联系人手机号")
    private String contractPhone;

    @ApiModelProperty(value = "法人姓名")
    private String legalPerson;//法人姓名

    @ApiModelProperty(value = "营业执照编号  分账方类型为个体工商户和企业类型必填，商户类型为个人则不填")
    private String licenseNo;//营业执照编号  分账方类型为个体工商户和企业类型必填，商户类型为个人则不填

    @ApiModelProperty("认证信息")
    private String authMsg;

    /** 图片审核状态: SUCCESS-成功, DOING-处理中, FAIL-失败 */
    @ApiModelProperty("图片审核状态: SUCCESS-成功, DOING-处理中, FAIL-失败")
    private String picStatus;

    /** 凭证上传结果 */
    @ApiModelProperty("凭证上传结果")
    private String picMsg;

    /** 身份证正面（国徽页）图片 */
    @ApiModelProperty(value = "身份证正面")
    private String cardPositiveUrl;

    /** 身份证反面（头像页）图片 */
    @ApiModelProperty(value = "身份证反面")
    private String cardNegativeUrl;

    /** 营业执照 图片 */
    @ApiModelProperty(value = "营业执照 图片")
    private String tradeLicenceUrl;

    /** 开户许可证图片 */
    @ApiModelProperty(value = "开户许可证图片")
    private String openAccountLicenceUrl;

    /** 变更状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过 */
    @ApiModelProperty("变更状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过")
    private String editStatus;

    /** 变更结果 */
    @ApiModelProperty(value = "变更结果")
    private String editMsg;

    /** 外部认证链接 */
    @ApiModelProperty(value = "外部认证链接(微信B2B支付在 ACCOUNT_NEED_VERIFY,UNSIGNED 需要显示二维码,跳转外部认证 )")
    private String outLink;
}
