package com.zksr.system.api.partnerPolicy.dto;

import com.zksr.common.core.enums.ConditionEnum;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户等级定义
 * @date 2024/11/12 8:43
 */
@Data
@ApiModel(description = "客户等级定义")
public class BranchLevelConfigDTO {

    @ApiModelProperty("用户标签")
    private BranchTagEnum branchTag;

    @ApiModelProperty("条件")
    private ConditionEnum condition;

    @ApiModelProperty("0-优先级最高")
    private Integer sort;

    @ApiModelProperty("条件值1")
    private BigDecimal valA;

    @ApiModelProperty("条件值2")
    private BigDecimal valB;
}
