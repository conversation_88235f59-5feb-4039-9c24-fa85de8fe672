package com.zksr.report.api.branch.dto;

import com.zksr.common.core.enums.branch.BranchTagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户活跃定义
 * @date 2024/11/12 8:43
 */
@Data
@ApiModel(description = "客户活跃定义")
public class BranchActiveConfigDTO {

    @ApiModelProperty(value = "用户标签ID", hidden = true)
    private Long tagDefId;

    @ApiModelProperty("标签类型")
    private BranchTagEnum branchTag = BranchTagEnum.ACTIVE_CUST;

    @ApiModelProperty("A-计划按照自然月, B-计划按照指定分类自然月")
    private String plan;

    @ApiModelProperty("管理分类列表")
    private List<String> catgoryList;

    @ApiModelProperty("满足目标金额")
    private BigDecimal targetAmt;
}
