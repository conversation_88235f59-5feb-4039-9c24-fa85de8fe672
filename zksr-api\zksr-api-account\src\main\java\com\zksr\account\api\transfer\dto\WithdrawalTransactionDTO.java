package com.zksr.account.api.transfer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WithdrawalTransactionDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transactionTime;      // 交易时间
    private String b2bPaymentOrderNo;  // B2B支付业务单号
    private String fundFlowOrderNo;    // 资金流水单号
    private String businessName;       // 业务名称
    private String businessType;       // 业务类型
    private String transactionType;    // 收支类型（收入/支出）
    private BigDecimal amount;         // 收支金额（元）
    private BigDecimal accountBalance; // 账户结余（元）
}