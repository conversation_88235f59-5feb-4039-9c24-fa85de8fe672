package com.zksr.promotion.api.coupon.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/12/7 14:15
 * @注释
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "优惠券异步领取请求参数")
public class NormalCouponReceiveSingleAsyncReqVo {

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "用户ID")
    private Long memberId;

    @ApiModelProperty(value = "优惠券模版ID")
    private Long couponTemplateId;

    @ApiModelProperty(value = "是否校验库存")
    private Boolean checkStock;

    @ApiModelProperty(value = "优惠券批次ID")
    private Long couponBatchId;

    @ApiModelProperty("优惠券ID:状态ID")
    private Map<Long, Long> couponStatusMap = new HashMap<>();

}
