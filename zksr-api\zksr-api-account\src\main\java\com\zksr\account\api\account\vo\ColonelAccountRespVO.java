package com.zksr.account.api.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员账户余额
 * @date 2024/4/25 18:32
 */
@Data
public class ColonelAccountRespVO {

    @ApiModelProperty("入驻商充值分润账户")
    private ColonelAccountInfoVO storageAccount;

    @ApiModelProperty(value = "门店充值(钱包支付账户)", notes = "默认显示这个")
    private ColonelAccountInfoVO branchRechargeAccount;

    /**
     * 商城订单支付平台
     */
    @ApiModelProperty(value = "商城订单支付平台", notes = "入驻商T+1结算所在平台, 含 自提点充值, 商场订单支付")
    private String storeOrderPayPlatform;

    /**
     * ===================================================================  以下为兼容 数据            =======================================================
     * ===================================================================  可以理解为默认提现账户     =======================================================
     */
    @ApiModelProperty(value = "账户ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long accountId;

    @ApiModelProperty(value = "储值支付可提现金额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    @ApiModelProperty(value = "储值支付冻结金额")
    private BigDecimal frozenAmt = BigDecimal.ZERO;

    @ApiModelProperty(value = "储值支付授信额度")
    private BigDecimal creditAmt = BigDecimal.ZERO;

    @ApiModelProperty("已提现金额")
    private BigDecimal totalWithdrawAmt = BigDecimal.ZERO;

    @ApiModelProperty("账户名称")
    private String accountName;

    @ApiModelProperty("银行卡号")
    private String accountNo;

    @ApiModelProperty("银行")
    private String bankBranch;

    @ApiModelProperty("最小提现金额")
    private BigDecimal minimumWithdrawalAmount;
}
