package com.zksr.account.api.platformMerchant.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 平台商户信息
 * @date 2024/4/8 15:22
 */
@Data
@ToString
@ApiModel(description = "平台商户信息")
@Accessors(chain = true)
public class PlatformMerchantDTO {

    @ApiModelProperty(value = "平台商户ID", notes = "更新时必填")
    private Long platformMerchantId;

    /** 商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum} */
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    /** 平台ID */
    @ApiModelProperty(value = "平台ID")
    private Long sysCode;

    /** 商户id */
    @ApiModelProperty(value = "商户id", notes = "入驻商ID,业务员ID等等等", required = true)
    private Long merchantId;

    /** 分账方商户编号 */
    @ApiModelProperty(value = "分账方商户编号", required = true)
    private String altMchNo;

    /** 分账方名称 */
    @ApiModelProperty(value = "分账方名称", required = true)
    private String altMchName;

    /** 商户KEY */
    @Excel(name = "商户KEY")
    @ApiModelProperty("商户KEY")
    private String altMchKey;

    /** 支付平台(数据字典) */
    @ApiModelProperty(value = "支付平台(数据字典)", required = true)
    private String platform;

    /** 银行名称 */
    @ApiModelProperty(value = "银行名称", required = true, example = "浦发银行")
    private String bankName;

    /** 银行支行 */
    @ApiModelProperty(value = "银行支行", required = true, example = "浦发银行麓谷支行")
    private String bankBranch;

    /** 银行卡号 */
    @ApiModelProperty(value = "银行卡号", required = true, example = "***************")
    private String accountNo;

    /** 支付平台进件单号 */
    @ApiModelProperty(value = "支付平台进件单号", required = true, example = "***************")
    private String thirdOrderNo;
}
