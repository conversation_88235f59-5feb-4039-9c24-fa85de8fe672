package com.zksr.promotion.api.coupon.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

/**
 * 优惠券模版拓展统计对象 prm_coupon_template_extend
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CouponTemplateExtendDTO extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 优惠券模版ID */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 大区 */
    @Excel(name = "大区")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 已使用优惠券张数 */
    @Excel(name = "已使用优惠券张数")
    private Integer usedCount;

    /** 实际领取优惠券张数 */
    @Excel(name = "实际领取优惠券张数")
    private Integer recordCount;

    /** 优惠券关联的订单合计销售金额 */
    @Excel(name = "优惠券关联的订单合计销售金额")
    private Integer totalSaleAmt;

    /** 优惠券在订单上合计优惠了多少 */
    @Excel(name = "优惠券在订单上合计优惠了多少")
    private Integer totalCouponAmt;

}
