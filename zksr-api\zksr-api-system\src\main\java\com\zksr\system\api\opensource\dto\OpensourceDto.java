package com.zksr.system.api.opensource.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
*  开放能力DTO
* @date 2024/10/20 10:11
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OpensourceDto {

    /** 开放能力 */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long opensourceId;

    /** 平台商id */
    private Long sysCode;

    /** 资源id */
    private Long merchantId;

    /** 资源类型（数据字典 supplier-入驻商等） */
    private String merchantType;

    /** 对开放资源外加密串 */
    private String sourceSecret;

    /** 开放资源key */
    private String sourceKey;

    /** ip白名单 */
    private String ipWhiteList;

    /** token uuid */
    private String token;

    /** ERP接口配置 公钥 */
    private String publicKey;

    /** ERP接口配置 私钥 */
    private String privateKey;

    @Excel(name = "商城显示物流信息 0第三方  1本地")
    @ApiModelProperty(value = "商城显示物流信息 0第三方  1本地")
    private String logisticsInfo;

    /**
     * 订单是否自动推送第三方
     */
    @ApiModelProperty("订单是否自动推送第三方 0手动推送  1 自动推送")
    private Integer orderAutoPush;

    /**
     * 订单延时推送时间
     */
    @ApiModelProperty("订单延时推送时间")
    private Integer orderDelayPushTime;

    /** 对接地址 */
    @Excel(name = "对接地址")
    private String sendUrl;

    /** 对接唯一编码 */
    @Excel(name = "对接唯一编码")
    private String sendCode;

    /** 可视化接口配置主表ID */
    @Excel(name = "可视化接口配置主表ID")
    private Long visualMasterId;

    /**
     * 是否同步标准价格 0-不同步 1-同步（默认同步）
     */
    private Integer syncMarkPrice;

    /**
     * 是否同步供货价格 0-不同步 1-同步（默认同步）
     */
    private Integer syncCostPrice;

    /**
     * 是否开启统配入驻商  0否 1是
     */
    @Excel(name = "是否开启统配入驻商  0否 1是")
    private Integer superSupplier;
    /**
     * 统配入驻商Id
     */
    @Excel(name = "统配入驻商Id")
    private Long superSupplierId;
    /**
     * 是否开启货到付款清账功能 0否 1是
     */
    @Excel(name = "是否开启货到付款清账功能")
    private Integer isHdfkSettle;

    /**
     * 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）
     */
    @Excel(name = "赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）")
    @ApiModelProperty("赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价(暂不做)）")
    private Integer giftPriceType;

    /**
     * 订单同步是否合单（0否，1是）
     */
    @Excel(name = "订单同步是否合单（0否，1是）")
    @ApiModelProperty("订单同步是否合单（0否，1是）")
    private Integer orderMergeFlag;

    /** 告警邮箱  多个邮箱用逗号隔开*/
    @Excel(name = "告警邮箱")
    private String alarmEmail;

    /** 接口发送邮件订阅 选择需要发送的接口 用逗号隔开 */
    @Excel(name = "接口发送邮件订阅 选择需要发送的接口 用逗号隔开")
    private String subscribeSendEmail;


    /**
     * 设置公共配置
     * @param opensourceDto
     * @param sendUrl
     * @param publicKey
     * @param privateKey
     */
    public static void setOpensourcePublicSetting(OpensourceDto opensourceDto,String sendUrl,String publicKey,String privateKey){
        opensourceDto.setSendUrl(sendUrl);
        opensourceDto.setPublicKey(publicKey);
        opensourceDto.setPrivateKey(privateKey);
    }
}
