package com.zksr.member.api.branchLifecycle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
* 门店生命周期触发事件备注项
* @date 2025/3/13 14:41
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BranchLifecycleStageStartMemoDataDTO {

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "下单金额")
    private BigDecimal amt;

    @ApiModelProperty(value = "触发事件")
    private String event;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "触发下单时间")
    private Date orderDate;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "触发时间")
    private Date eventDate;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "是否是订单取消")
    private Boolean isOrderCancel = false;
}
