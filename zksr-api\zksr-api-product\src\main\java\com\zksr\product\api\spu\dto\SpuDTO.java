package com.zksr.product.api.spu.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpuDTO {

    /** 商品SPU_id */
    private Long spuId;

    /** 平台商id */
    private Long sysCode;

    /** 入驻商id */
    private Long supplierId;

    /** 平台商管理分类id */
    private Long catgoryId;

    /** 平台商品牌id */
    private Long brandId;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    private String spuNo;

    /** 商品SPU名称 */
    private String spuName;

    /** 封面图（url） */
    private String thumb;

    /** 封面视频（url） */
    private String thumbVideo;

    /** 详情页轮播（json） */
    private String images;

    /** 详情信息(富文本) */
    private String details;

    /** 库存数量 */
    private Long stock;

    /** 是否删除 1-是 0-否 */
    private Long isDelete;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = DateUtils.TIMEZONE)
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = DateUtils.TIMEZONE)
    private Date latestDate; 		 // 最新生产日期

    /** 是否开启多规格 1-是 0-否 */
    private Long isSpecs;

    /** 状态(数据字典 sys_common_status) */
    private Long status;

    /** 产地 */
    private String originPlace;

    /** 备注 */
    private String memo;

    /** 规格名称 */
    private String specName;

    /** 商品类型 0：全国商品 1：本地商品 */
    private Integer itemType;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典", readConverterExp = "sys_prdt_unit")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    private BigDecimal midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    private BigDecimal largeSize;

    /** 是否开启联动换算 1-是 0-否 */
    @Excel(name = "是否开启联动换算 1-是 0-否")
    private Integer isLinkage;

    /** 保质期 */
    @Excel(name = "保质期")
    private Integer expirationDate; 		 // 保质期

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    private String sourceNo;

    /** 外部来源商品编号 */
    @Excel(name = "来源（B2B、ERP）")
    private String source;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

    /** 关联关键词 */
    @Excel(name = "关联关键词")
    private String keywords;

    /**
     * 商品计价方式类型
     */
    private Integer pricingWay;
    
    /** 是否零售(0否1是) */
    @Excel(name = "是否零售(0否1是)")
    private Integer enableRetail;
    
    /** 零售分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额) */
    @Excel(name = "零售分润模式 字典：retail_profit_mode")
    private Integer retailProfitMode;
    
    /** 是否B2b(0否1是) */
    @Excel(name = "是否B2b(0否1是)")
    private Integer enableWholesale;
    
    /** B2b分润模式(1-不分润、2-按比例、3-按金额、4-按平台规则、5-按供货价差额) */
    @Excel(name = "B2b分润模式，字典：wholesale_profit_mode")
    private Integer wholesaleProfitMode;
    
    /** 其他属性 */
    @Excel(name = "其他属性")
    private String otherAttr;

    /**
     * 获取单位换算数量
     * @return
     */
    public BigDecimal getUnitSizeQty(Integer unit){
        if (Objects.isNull(unit)) {
            return BigDecimal.ONE;
        }
        if (unit == UnitTypeEnum.UNIT_MIDDLE.getType().intValue())    return midSize;
        if (unit == UnitTypeEnum.UNIT_LARGE.getType().intValue())  return largeSize;
        return BigDecimal.ONE;
    }

    /**
     * 获取单位换算比例
     * @return
     */
    public BigDecimal getUnitSizeConvertRatio(Integer sourceUnit, Integer targetUnit){
        if (Objects.isNull(sourceUnit) || Objects.isNull(targetUnit) || sourceUnit < UnitTypeEnum.UNIT_SMALL.getType() || sourceUnit > UnitTypeEnum.UNIT_LARGE.getType() || targetUnit < UnitTypeEnum.UNIT_SMALL.getType() || targetUnit > UnitTypeEnum.UNIT_LARGE.getType()) {
            return BigDecimal.ZERO;
        }
        BigDecimal[] sizeArray = new BigDecimal[] {null, BigDecimal.ONE, midSize, largeSize};
        if (sizeArray[sourceUnit] == null || sizeArray[targetUnit] == null || sizeArray[targetUnit].compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return sizeArray[sourceUnit].divide(sizeArray[targetUnit], 8, RoundingMode.HALF_UP);
    }

    /**
     * 获取单位
     * @return
     */
    public String getUnit(Integer unitSize){
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midUnit)) return midUnit.toString();
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeUnit)) return largeUnit.toString();
        return Objects.isNull(minUnit) ? null : minUnit.toString();
    }

    public BigDecimal stockConvert(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return BigDecimal.ONE;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midUnit)) return midSize;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeUnit)) return largeSize;
        return BigDecimal.ONE;
    }

    /**
     * 验证单位是否存在
     * @param unitSize
     * @return
     */
    public boolean isUnitExist(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return false;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.isNull(midUnit)) return false;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.isNull(largeUnit)) return false;
        return true;
    }

    public List<UnitTypeEnum> hasUnitList() {
        List<UnitTypeEnum> unitTypeEnums = new ArrayList<>();
        unitTypeEnums.add(UnitTypeEnum.UNIT_SMALL);
        if (Objects.nonNull(this.midUnit)) {
            unitTypeEnums.add(UnitTypeEnum.UNIT_MIDDLE);
        }
        if (Objects.nonNull(this.largeUnit)) {
            unitTypeEnums.add(UnitTypeEnum.UNIT_LARGE);
        }
        return unitTypeEnums;
    }
}
