package com.zksr.account.api.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/13 11:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiAccAccountFlowPageVO {
    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(required = true, value = "商户类型")
    private String merchantType;

    /**
     * 商户id 根据操作账户类型不同传入
     */
    @ApiModelProperty(required = true, value = "商户id")
    private Long merchantId;

    @ApiModelProperty("收入/支出, 0-收入,1-支出")
    private Integer ioType;

    @ApiModelProperty("是否执行完成,0-未执行, 1-已执行")
    private Integer processFlag;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("流水执行开始时间")
    private Date processStartTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("流水执行结束时间")
    private Date processEndTime;

    /** 影响字段 */
    @Excel(name = "影响字段")
    @ApiModelProperty(value = "影响字段")
    private String busiFields;
}
