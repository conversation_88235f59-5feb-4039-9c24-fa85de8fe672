package com.zksr.account.model.pay.vo;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单支付状态查询
 * @date 2025/3/11 14:15
 */
@Data
@ApiModel(description = "订单支付状态查询")
public class PayOrderQueryVO {

    @ApiModelProperty(value = "交易订单号", required = true)
    private String tradeNo;

    @ApiModelProperty(value = "如果提前查询到了支付结果, 是否进行成功的支付成功回调, 默认不进行")
    private boolean advanceNoticeOrder = false;

    @ApiModelProperty(value = "支付流水", hidden = true, notes = "不需要传递, 这是内部自动填充的")
    private PayFlowDTO payFlow;
}
