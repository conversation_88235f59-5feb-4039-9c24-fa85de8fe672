package com.zksr.member.api.searchHis.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/1/16 17:34
 * @注释
 */
@Data
@ApiModel("搜索词条")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchHisReqVO {

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 门店用户id */
    @ApiModelProperty(value = "门店用户id")
    private Long memberId;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 搜索词 */
    @ApiModelProperty(value = "搜索词")
    private String words;
}
