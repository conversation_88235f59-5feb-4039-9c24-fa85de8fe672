---
description: 
globs: 
alwaysApply: false
---
# SaasBizB2B 项目开发规范（Project Guidelines）

---

## 一、技术架构

- **整体架构**：采用 Spring Cloud 微服务架构，服务拆分为认证、网关、业务、API、通用、门户、可视化等多个独立模块，支持高可用、易扩展。
- **服务注册与配置中心**：Nacos（v2.2.3）。
- **数据库**：MySQL 5.7.23。
- **缓存**：Redis 5。
- **消息中间件**：RocketMQ 4.9.4。
- **分布式事务**：Seata 1.6.1。
- **任务调度**：XXL-JOB。
- **全文检索**：Elasticsearch 7.17.16（预留）。
- **API文档**：Swagger、Knife4j。
- **其他依赖**：MyBatis-Plus、PageHelper、Hutool、MapStruct、Lombok、Minio、FastDFS、Druid、JetCache、Guava、HanLP、Jieba 等。

---

## 二、代码规范

### 2.1 目录结构
- 按照功能模块分层：controller、service、domain、vo、dto、mapper、resources 等。
- SQL 脚本按 init、release、version 分类，版本号命名，便于升级和回滚。
- readme/ 目录下存放项目和代码相关文档。

### 2.2 命名规范
- 包名、类名、方法名、变量名：有意义的英文单词，驼峰命名法。
- 常量：全大写+下划线。
- SQL 文件：以功能或版本号命名。

### 2.3 注释与文档
- 类、方法、字段需有中文注释，描述功能和注意事项。
- 重要接口、DTO、VO 使用 Swagger 注解（@ApiModel、@ApiModelProperty、@ApiOperation）。
- 复杂业务逻辑需详细注释。

### 2.4 代码风格
- 遵循阿里巴巴 Java 开发手册。
- 统一使用 4 空格缩进。
- 代码提交前请本地格式化并自查。

---

## 三、分支管理与提交规范

### 3.1 分支管理
- 主分支：`main` 或 `master`，仅发布版本合并。
- 开发分支：`feature/xxxx/xxx`，按功能或需求命名。
- 修复分支：`fix/xxxx/xxx`。
- 版本分支：`release/xxxx`。

### 3.2 提交规范
- 提交信息需简明扼要，首行为动词+内容，如：`feat: 新增商品导入接口`。
- 关联 issue 或需求时，注明编号。
- 禁止提交敏感信息（如密码、密钥等）。

---

## 四、数据库脚本管理

- 初始化脚本：`sql/init/`，新环境初始化使用。
- 版本升级脚本：`sql/version/`，以版本号命名，升级时按顺序执行。
- 发布脚本：`sql/release/`，每次发布需同步执行。
- 脚本需包含注释，说明变更内容、执行顺序、回滚方案。
- 禁止直接在生产库手动变更表结构。

---

## 五、接口与文档

- 所有接口需遵循 RESTful 设计。
- 必须使用 Swagger 注解，自动生成接口文档。
- 参数校验使用 JSR-303 注解（如 @Valid、@Size、@Pattern）。
- 权限控制通过注解（如 @RequiresPermissions、@RequiresRoles）和 AOP 切面实现。
- 重要操作需加 @Log 注解，便于审计。

---

## 六、配置管理

- 配置文件采用 yml 格式，按环境分文件管理（如 bootstrap.yml、application-dev.yml）。
- 敏感信息（如数据库、邮箱密码）通过 Nacos 配置中心管理，避免硬编码。
- MQ、定时任务、白名单等配置需在 Nacos、yml、SQL 三方同步。

---

## 七、开发注意事项

- **接口幂等性**：重要接口需保证幂等，避免重复操作。
- **批量操作**：批量导入、批量修改需做好数据校验和异常处理。
- **权限与安全**：所有敏感操作必须加权限注解，防止越权。
- **日志与异常**：关键流程需记录日志，异常需详细抛出并记录。
- **任务调度**：XXL-JOB 任务需在配置和 SQL 中同步维护。
- **消息队列**：RocketMQ topic、group、bindings 配置需与代码、Nacos 保持一致。
- **环境依赖**：开发、测试、生产环境依赖需提前准备，避免因环境不一致导致问题。
- **模块依赖**：各模块间依赖通过 Maven 管理，需保证依赖版本一致。

---

## 八、测试与部署

- 单元测试、集成测试用例需覆盖主要业务流程。
- 发布前需在测试环境全量回归。
- 发布流程：代码合并 -> 依赖检查 -> 构建打包 -> 数据库升级 -> 配置同步 -> 服务重启。
- 发布后需重点关注日志、监控、报警。

---

## 九、团队协作与新成员指引

- 新成员请先阅读 readme/ 目录下文档，熟悉项目结构和开发规范。
- 日常开发遇到问题请先查阅文档和注释，无法解决再向团队成员请教。
- 重要变更需在群内同步，涉及多模块需提前沟通。

---

## 十、附录

- 参考文档：阿里巴巴 Java 开发手册、Spring Cloud 官方文档、MyBatis-Plus 官方文档等。
- 相关工具：IDEA、Maven、Nacos、Redis、MySQL、RocketMQ、XXL-JOB、Swagger、Knife4j。

---

如有规范遗漏或需补充，请团队成员及时补充完善。