package com.zksr.system.api.sms.dto.request;

import com.zksr.common.core.utils.ServletUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 安得消息中心请求实体
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "安得消息中心请求实体")
public class SmsReqDTO {

    @ApiModelProperty(value = "来源系统", required = true)
    @NotEmpty(message = "来源系统")
    private String appSystem;

    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private List<String> mobiles;

    @ApiModelProperty(value = "短信内容", required = true)
    @NotEmpty(message = "短信内容不能为空")
    private String content;

    @ApiModelProperty(value = "租户", required = true)
    @NotEmpty(message = "租户不能为空")
    private String tenantCode;

    @ApiModelProperty(value = "签名，默认为：安得智联", required = true)
    @NotEmpty(message = "签名不能为空")
    private String signName;

    @ApiModelProperty(value = "业务节点描述")
    private String nodeName;

    private int handleType = 1;
}
