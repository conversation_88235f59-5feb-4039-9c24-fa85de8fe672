package com.zksr.product.api.model.event;

import cn.hutool.core.collection.ListUtil;
import com.zksr.product.api.model.EsProductEvent;
import com.zksr.product.api.model.EsProductEventType;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快速获取事件
 * @date 2024/2/29 19:56
 */
public class EsProductEventBuild {

    /**
     * 本地商品上架发布事件
     * 改价
     * 上下架
     * 改库存 都定义重新上下架
     * @param itemIds   上架商品ID
     * @return  事件
     */
    public static EsProductEvent<EsReleaseProductEvent> localReleaseEvent(List<Long> itemIds) {
        return new EsProductEvent<>(EsProductEventType.LOCAL_RELEASE, new EsReleaseProductEvent(itemIds));
    }

    /**
     * 全国上架发布事件
     * 改价
     * 上下架
     * 改库存 都定义重新上下架
     * @param itemIds   上架商品ID
     * @return  事件
     */
    public static EsProductEvent<EsReleaseProductEvent> globalReleaseEvent(List<Long> itemIds) {
        return new EsProductEvent<>(EsProductEventType.GLOBAL_RELEASE, new EsReleaseProductEvent(itemIds));
    }

    /**
     * SPU变动事件
     * @param spuId   SPU_ID
     * @return  事件
     */
    public static EsProductEvent<EsSpuUpdateProductEvent> spuEvent(Long spuId) {
        return new EsProductEvent<>(EsProductEventType.SPU_UPDATE, new EsSpuUpdateProductEvent(spuId));
    }

    /**
     * SPU变动事件
     * @param spuId   SPU_ID
     * @return  事件
     */
    public static EsProductEvent<EsSpuUpdateProductEvent> spuEvent(List<Long> spuId) {
        return new EsProductEvent<>(EsProductEventType.SPU_UPDATE, new EsSpuUpdateProductEvent(spuId));
    }

    /**
     * SKU变动事件
     * @param skuId   SKU_ID
     * @return  事件
     */
    public static EsProductEvent<EsSkuUpdateProductEvent> skuEvent(Long skuId) {
        return new EsProductEvent<>(EsProductEventType.SKU_UPDATE, new EsSkuUpdateProductEvent(skuId));
    }

    /**
     * SKU变动事件
     * @param skuIdList   SKU_ID
     * @return  事件
     */
    public static EsProductEvent<EsSkuUpdateProductEvent> skuEvent(List<Long> skuIdList) {
        return new EsProductEvent<>(EsProductEventType.SKU_UPDATE, new EsSkuUpdateProductEvent(skuIdList));
    }


    /**
     * SPU变动事件
     * @param spuCombineId   spuCombineId
     * @return  事件
     */
    public static EsProductEvent<EsCbProductEvent> spuCombineEvent(List<Long> spuCombineId) {
        return new EsProductEvent<>(EsProductEventType.CB_PRODUCT_UPDATE, new EsCbProductEvent(spuCombineId));
    }

    /**
     * 删除SKU
     */
    public static EsProductEvent<EsRemoveProductEvent> removeSku(List<Long> skuIdList) {
        return new EsProductEvent<>(EsProductEventType.REMOVE, EsRemoveProductEvent.builder().skuId(skuIdList).build());
    }

    /**
     * 删除SPU
     */
    public static EsProductEvent<EsRemoveProductEvent> removeSpu(List<Long> skuIdList) {
        return new EsProductEvent<>(EsProductEventType.REMOVE, EsRemoveProductEvent.builder().spuId(skuIdList).build());
    }

    /**
     * 删除itemId
     */
    public static EsProductEvent<EsRemoveProductEvent> removeItem(List<Long> itemIds) {
        return new EsProductEvent<>(EsProductEventType.REMOVE, EsRemoveProductEvent.builder().itemIds(itemIds).build());
    }

    /**
     * 删除itemId
     */
    public static EsProductEvent<?> removeItem(Long itemId) {
        return new EsProductEvent<>(EsProductEventType.REMOVE, EsRemoveProductEvent.builder().itemIds(ListUtil.toList(itemId)).build());
    }
}
