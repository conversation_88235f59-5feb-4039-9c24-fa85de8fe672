package com.zksr.account.api.platformMerchant.vo;

import com.zksr.account.api.platformMerchant.vo.PlatformMerchantRegisterSaveReqVO;
import com.zksr.account.api.platformMerchant.vo.PlatformMerchantUploadSaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("支付平台商户 - acc_platform_merchant 进件保存")
public class AccPlatformMerchantUploadPicReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户类型
     * 参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty("商户类型: partner-平台商, dc-运营商, supplier-入驻商, colonel-业务员")
    private String merchantType;

    /**
     * 商户ID
     */
    @ApiModelProperty("商户ID: sysCode, dcId, supplierId, colonelId")
    private Long merchantId;

    /**
     * 支付平台信息
     */
    @ApiModelProperty("支付平台信息: hlb-合利宝, mideaPay-美的支付, mock-模拟支付")
    private String platform;

    /**
     * 商户凭证信息
     */
    @ApiModelProperty("商户凭证信息")
    private List<PlatformMerchantUploadSaveReqVO> platformMerchantUploadSaveReqVO;
}
