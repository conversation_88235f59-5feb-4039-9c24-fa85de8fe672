package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 短信配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmsConfigDTO {


    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    /**============================================================== START 阿里云短信 START ===============================================*/

    /**
     * 短信平台
     */
    @ApiModelProperty("短信平台")
    private String smsPlatform;

    /**
     * accessKeyId
     */
    @ApiModelProperty("accessKeyId")
    private String accessKeyId;

    /**
     * accessKeySecret
     */
    @ApiModelProperty("accessKeySecret")
    private String accessKeySecret;

    /**============================================================== END 阿里云短信 END ===============================================*/

    /**============================================================== START 安得腾讯云 START ===============================================*/

    /**
     * 鹊桥配置clientId
     */
    @ApiModelProperty("bopClientId")
    private String bopClientId;

    /**
     * 鹊桥配置clientSecret
     */
    @ApiModelProperty("bopClientSecret")
    private String bopClientSecret;

    /**
     * 鹊桥配置accessToken
     */
    @ApiModelProperty("bopAccessToken")
    private String bopAccessToken;

    /**
     * 鹊桥url
     */
    @ApiModelProperty("bopUrl")
    private String bopUrl;

    /**
     * 消息中心租户编码
     */
    @ApiModelProperty("tenantCode")
    private String tenantCode;

    /**
     * 消息中心签名
     */
    @ApiModelProperty("signName")
    private String signName;

    /**============================================================== END 安得腾讯云 END ===============================================*/
}
