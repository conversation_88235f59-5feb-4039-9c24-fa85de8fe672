package com.zksr.system.api.openapi;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.openapi.dto.AnntoErpRequestDTO;
import com.zksr.system.api.openapi.dto.AnntoErpResultDTO;
import com.zksr.system.api.openapi.dto.hisense.InvoiceUpdateRequest;
import com.zksr.system.api.openapi.dto.hisense.InvoiceUpdateResponse;
import com.zksr.system.api.openapi.dto.hisense.OrderCancelRequest;
import com.zksr.system.api.openapi.dto.hisense.OrderCancelResponse;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 对接ERP API
 */
@FeignClient(
        contextId = "remoteHisenseErpApi",
        value = ApiConstants.NAME
)
public interface HisenseErpApi {
    String PREFIX = ApiConstants.PREFIX + "/hisenseErp";

    @PostMapping(value = PREFIX + "/orderCancel")
    OrderCancelResponse orderCancel(@RequestBody OrderCancelRequest request);

    @PostMapping(value = PREFIX + "/updateInvoice")
    InvoiceUpdateResponse updateInvoice(@RequestBody InvoiceUpdateRequest request);
}
