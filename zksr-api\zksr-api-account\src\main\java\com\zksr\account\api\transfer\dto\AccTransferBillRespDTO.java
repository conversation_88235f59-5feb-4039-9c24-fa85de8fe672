package com.zksr.account.api.transfer.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/1/22 9:51
 * @注释
 */
@Data
@ApiModel("交易对账单返回实体")
public class AccTransferBillRespDTO extends BaseEntity {

    /** 交易账单ID */
    @ApiModelProperty(value = "交易账单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long transferBillId;

    /** 账单日期 */
    @Excel(name = "账单日期")
    @ApiModelProperty(value = "账单日期")
    private String billDate;

    /** 交易笔数 */
    @Excel(name = "交易笔数")
    @ApiModelProperty(value = "交易笔数")
    private Integer transferNum;

    /** 商户总支付金额 */
    @Excel(name = "商户总支付金额")
    @ApiModelProperty(value = "商户总支付金额")
    private BigDecimal merchantTotalPayAmt;

    /** 平台总支付金额 */
    @Excel(name = "平台总支付金额")
    @ApiModelProperty(value = "平台总支付金额")
    private BigDecimal platformTotalPayAmt;

    /** 商户总退款金额 */
    @Excel(name = "商户总退款金额")
    @ApiModelProperty(value = "商户总退款金额")
    private BigDecimal merchantTotalRefundAmt;

    /** 平台总退款金额 */
    @Excel(name = "平台总退款金额")
    @ApiModelProperty(value = "平台总退款金额")
    private BigDecimal platformTotalRefundAmt;

    /** 商户总支付手续费 */
    @Excel(name = "商户总支付手续费")
    @ApiModelProperty(value = "商户总支付手续费")
    private BigDecimal merchantTotalPayFree;

    /** 平台总支付手续费 */
    @Excel(name = "平台总支付手续费")
    @ApiModelProperty(value = "平台总支付手续费")
    private BigDecimal platformTotalPayFree;

    /** 商户总退款手续费 */
    @Excel(name = "商户总退款手续费")
    @ApiModelProperty(value = "商户总退款手续费")
    private BigDecimal merchantTotalRefundFree;

    /** 平台总退款手续费 */
    @Excel(name = "平台总退款手续费")
    @ApiModelProperty(value = "平台总退款手续费")
    private BigDecimal platformTotalRefundFree;

    /** 退款笔数 */
    @Excel(name = "退款笔数")
    @ApiModelProperty(value = "退款笔数")
    private Integer refundCount;

    /** 支付渠道, hlb-合利宝,wxb2b-微信b2b */
    @ApiModelProperty(value = "支付渠道, hlb-合利宝,wxb2b-微信b2b")
    private String platform;
}
