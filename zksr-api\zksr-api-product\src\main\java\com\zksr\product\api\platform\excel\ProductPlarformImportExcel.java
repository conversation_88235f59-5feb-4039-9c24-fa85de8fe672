package com.zksr.product.api.platform.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;


/**
 *  商品库导入商品信息
 */
@Data
@ApiModel(description = "用于商品库商品信息导入")
public class ProductPlarformImportExcel {

    @Excel(name = "商品编号", headerColor = IndexedColors.RED)
    private String spuNo;

    @Excel(name = "商品名称", headerColor = IndexedColors.RED)
    private String spuName;

    @Excel(name = "品牌名称", headerColor = IndexedColors.RED)
    private String brandName;

    @Excel(name = "商品类别(三级管理类别)", headerColor = IndexedColors.RED)
    private String categoryName;

    @Excel(name = "单位", prompt = "sys_prdt_unit字典", headerColor = IndexedColors.RED)
    private String minUnitLabel;

    @Excel(name = "条码", headerColor = IndexedColors.RED)
    private String barcode;

    @Excel(name = "中单位", prompt = "sys_prdt_unit字典")
    private String midUnitLabel;

    @Excel(name = "中单位换算数量")
    private Integer midSize;

    @Excel(name = "中单位条码")
    private String midBarcode;

    @Excel(name = "大单位", prompt = "sys_prdt_unit字典")
    private String largeUnitLabel;

    @Excel(name = "大单位换算数量")
    private Integer largeSize;

    @Excel(name = "大单位条码")
    private String largeBarcode;

    @Excel(name = "图片地址")
    private String pictureUrl;

    @Excel(name = "详情图片地址")
    private String detailImages;

}
