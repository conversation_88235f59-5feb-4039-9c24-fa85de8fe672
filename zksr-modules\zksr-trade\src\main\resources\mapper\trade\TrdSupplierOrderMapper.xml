<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.trade.mapper.TrdSupplierOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <resultMap id="homePage" type="com.zksr.trade.controller.order.vo.HomePageRespVO">
        <result property="monthAmount" column="monthAmount" />
        <result property="todayOrder" column="todayOrder" />
        <result property="todayAmount" column="todayAmount" />
        <collection property="thirtyDaysOrders" column="{supplierId = supplier_id,startDate = startDate,endDate = endDate}" select="getThirtyDaysOrders"/>
    </resultMap>

    <select id="getThirtyDaysOrders" resultType="com.zksr.trade.controller.order.vo.HomeOrdersRespVO">
        SELECT
            DATE( create_time ) AS createTime,
            IFNULL( SUM( sub_order_amt - sub_cancel_amt ), 0 ) AS countTotalAmt,
            COUNT( 1 ) AS countOrder,
            IFNULL( SUM( sub_order_num - sub_cancel_qty ), 0 ) AS countTotalNum
        FROM
            trd_supplier_order
        WHERE
            supplier_id = #{supplierId}
            AND sub_order_num != sub_cancel_qty
            <!-- 已付款, 或者货到付款未付款 -->
            AND pay_state IN (1, 3)
        <if test="null != startDate and '' != startDate and null != endDate and '' != endDate">
            AND create_time BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY
            DATE( create_time )
        ORDER BY
            createTime DESC
    </select>

    <select id="selectHomePageMonth" resultMap="homePage">
        SELECT
            tso.supplier_id,
            case
                when '${startDate}' IS NULL then NULL
                else '${startDate}' end as startDate ,
            case
                when '${endDate}' IS NULL then NULL
                else '${endDate}' end as endDate ,
            IFNULL(SUM(sub_order_amt - sub_cancel_amt), 0) AS monthAmount
        FROM
            trd_supplier_order tso
            INNER JOIN trd_order tor ON tso.order_id = tor.order_id
        WHERE
            tso.supplier_id = #{supplierId}
            <!-- 下单数量 != 发货前取消数量 -->
            AND sub_order_num != sub_cancel_qty
            <!-- 本月开始时间 -->
            AND tso.create_time &gt;= DATE_FORMAT(NOW() - INTERVAL (DAYOFMONTH(NOW()) - 1) DAY, '%Y-%m-01')
            <!-- 已付款, 或者货到付款未付款 -->
            AND tso.pay_state IN (1, 3)
    </select>
    <select id="selectHomePageDay" resultType="com.zksr.trade.controller.order.vo.HomePageRespVO">
        SELECT
            IFNULL(COUNT(1), 0) todayOrder,
            IFNULL(SUM(tso.sub_order_num), 0) todayAmount
        FROM
            trd_supplier_order tso
            INNER JOIN trd_order tor ON tso.order_id = tor.order_id
        WHERE
            tso.supplier_id = #{supplierId}
            <!-- 下单数量 != 发货前取消数量 -->
            AND sub_order_num != sub_cancel_qty
            <!-- 今日开始时间 -->
            AND tso.create_time &gt;= DATE_FORMAT(NOW() - INTERVAL (HOUR(NOW()) * 60 + MINUTE(NOW()) * 60 + SECOND(NOW())) SECOND, '%Y-%m-%d 00:00:00')
            <!-- 已付款, 或者货到付款未付款 -->
            AND tso.pay_state IN (1, 3)
    </select>

    <!-- 根据订单查询入驻商订单支付金额信息 -->
    <select id="getSupplierOrderPayInfo" resultType="com.zksr.trade.api.order.dto.TrdSupplierResDto" parameterType="com.zksr.trade.api.order.vo.TrdSupplierPageVO">
        SELECT
            tso.supplier_id,
            tso.supplier_order_no AS subOrderNo,
            tso.supplier_order_id,
            tso.sub_pay_amt AS subOrderAmt,
            tso.sub_pay_fee,
            tor.platform,
            tor.cz_principal_pay_amt,
            tor.cz_give_pay_amt,
            tor.pay_amt,
            tor.order_id,
            <!--增加销售模式-->
            tor.distribution_mode
        FROM
            trd_order tor
            INNER JOIN trd_supplier_order tso ON tor.order_id = tso.order_id
        WHERE
            tor.order_no = #{orderNo}
            AND tor.sys_code = #{sysCode}
    </select>

    <!-- 根据供应商订单号查询供应商订单收款信息 -->
    <select id="getOrderReceiptInfoBySupplierOrderNo"
            resultType="com.zksr.trade.api.order.dto.OrderReceiptRespDTO"
            parameterType="com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO">

        SELECT
            table_supplierOrder.supplier_order_no AS supplierSheetNo
            , table_supplierOrder.subPayAmt AS receiptAmt
            , table_supplierOrder.subExactPayAmt AS receiptExactAmt
            , 'XS' AS sheetType
            , 'XSS' AS supplierSheetType
            , tor.pay_way AS sheetPayWay
        FROM
            (
                SELECT
                    tso.order_id
                    , tso.supplier_order_no
                    , SUM(tsod.total_amt - IFNULL((SELECT SUM(tsad.return_amt) FROM trd_supplier_after_dtl tsad WHERE tsad.supplier_order_dtl_id = tsod.supplier_order_dtl_id AND tsad.is_cancel = 0), 0) ) AS subPayAmt
                    , SUM(tsod.total_amt - IFNULL((SELECT SUM(tsad.exact_return_amt) FROM trd_supplier_after_dtl tsad WHERE tsad.supplier_order_dtl_id = tsod.supplier_order_dtl_id AND tsad.is_cancel = 0), 0) ) AS subExactPayAmt
                    <!--
                        , SUM((tsod.total_num - IFNULL((SELECT SUM(tsad.return_qty) FROM trd_supplier_after_dtl tsad WHERE tsad.supplier_order_dtl_id = tsod.supplier_order_dtl_id AND tsad.is_cancel = 0), 0)) * tsod.exact_price) AS subPayAmt
                    -->
                FROM
                    trd_supplier_order tso
                    LEFT JOIN trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
                WHERE
                    tso.supplier_order_no = #{supplierSheetNo}
                GROUP BY
                    tso.order_id
                    , tso.supplier_order_no
            ) table_supplierOrder
            LEFT JOIN trd_order tor ON table_supplierOrder.order_id = tor.order_id

    </select>
    <select id="getOrderAfterReceiptInfoBySupplierOrderNo"
            resultType="com.zksr.trade.api.order.dto.OrderReceiptRespDTO">
        (
            SELECT
                tso.supplier_order_no AS supplierSheetNo,
                tso.sub_order_amt AS receiptAmt,
                tsod.exact_total_amt AS receiptExactAmt,
                'XS' AS sheetType,
                'XSS' AS supplierSheetType,
                tor.pay_way AS sheetPayWay
            FROM
                trd_supplier_order tso
                    LEFT JOIN trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
                    LEFT JOIN trd_order tor ON tso.order_id = tor.order_id
            WHERE
                tso.supplier_order_no = #{supplierSheetNo}
        )
        UNION ALL
        (
            SELECT
                tsa.supplier_after_no AS supplierSheetNo,
                -tsa.sub_refund_amt AS receiptAmt,
                (SELECT -SUM(tsad.exact_return_amt)
                 FROM trd_supplier_after_dtl tsad
                 WHERE tsad.supplier_after_id = tsa.supplier_after_id) AS receiptExactAmt,
                'SH' AS sheetType,
                IFNULL(tsa.trans_no, 'SHS') AS supplierSheetType,
                tar.pay_way AS sheetPayWay
            FROM
                trd_supplier_after tsa
                    LEFT JOIN trd_after tar ON tsa.after_id = tar.after_id
            WHERE
                tsa.supplier_order_no = #{supplierSheetNo}
              AND tsa.trans_no IN ('SHC', 'SHJ','SC','SJ')
        );
    </select>
    <select id="selectBranchTransitQty" resultType="java.lang.Long">
        SELECT
            SUM(tsod.total_num - tsod.cancel_qty)
        FROM
            trd_order tor
            INNER JOIN trd_supplier_order_dtl tsod ON tsod.order_id = tor.order_id
        WHERE
            -- 30天内的
            tor.create_time > DATE_SUB(NOW(), interval 30 DAY)
            AND tor.branch_id = #{branchId}
            AND tsod.area_item_id = #{areaItemId}
            AND tor.pay_state IN (1, 3, 4)
            AND tsod.delivery_state NOT IN (2, 5)
    </select>
    <select id="selectLastOrderTime" resultType="com.zksr.trade.api.order.vo.SupplierOrderDtlInfoExportVO">
        select tor.branch_id as branchId ,max(tor.create_time) lasterOrderTime
        from trd_order tor
        INNER JOIN trd_supplier_order_dtl tsod ON tsod.order_id = tor.order_id
        <where>
            <if test="branchIds != null and branchIds.size > 0">
                tor.branch_id in
                <foreach collection="branchIds" item="branchId" separator="," open="(" close=")">
                    #{branchId}
                </foreach>
            </if>
            AND (tor.del_flag = 0 OR tor.del_flag IS NULL)
            AND (tor.pay_state != 2 and  tsod.delivery_state != 2)
        </where>
        group by tor.branch_id
    </select>

    <!-- 查询需要生成分账的O2O订单 -->
    <select id="selectO2OOrdersForDivide" resultType="com.zksr.trade.api.order.dto.O2OGenerateSettleOrderDTO"
            parameterType="com.zksr.trade.api.order.vo.O2OGenerateSettleParamVO">
        SELECT
            tso.supplier_order_no AS supplierOrderNo,
            tsod.receive_time AS receiveTime
        FROM
            zksr_trade.trd_supplier_order tso
            INNER JOIN zksr_trade.trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
            INNER JOIN zksr_trade.trd_order t ON tso.order_id = t.order_id
        WHERE
            <!-- 1. 签收时间距离当前时间大于等于8天但是小于等于30天 -->
            tsod.receive_time IS NOT NULL
            AND DATEDIFF(NOW(), tsod.receive_time) >= 8
            AND DATEDIFF(NOW(), tsod.receive_time) &lt;= 30
            <if test="isTest != null and isTest">
                AND NOW() >= DATE_ADD(tsod.receive_time, INTERVAL #{signAfterMinutes} MINUTE) <!--订单签收完N分钟 这个主要给测试人员测试的-->
            </if>
            <!-- 2. 订单状态不是取消状态-->
            AND tso.pay_state IN (1)  <!-- 已付款 -->
            AND tsod.delivery_state != 50  <!-- 排除取消 -->
            <!-- 3. 订单有实际数量（排除全部取消的订单）-->
            AND (tsod.total_num - tsod.cancel_qty) > 0
            <!-- 4. 没有分账记录-->
            AND NOT EXISTS (
                SELECT 1
                FROM acc_divide_dtl add_check
                WHERE add_check.trade_no = tso.supplier_order_no
                AND add_check.merchant_type = 'SUPPLIER'
                AND add_check.merchant_id = tso.supplier_id
            )
            <!-- 5. 售后状态条件：没有售后或所有售后都已完成-->
            AND (
                <!-- 5.1 没有任何售后申请-->
                NOT EXISTS (
                    SELECT 1
                    FROM zksr_trade.trd_supplier_after tsa
                    WHERE tsa.supplier_order_no = tso.supplier_order_no
                )
                OR
                <!-- 5.2 所有售后都已完成处理（不存在进行中的售后）-->
                NOT EXISTS (
                    SELECT 1
                    FROM zksr_trade.trd_supplier_after tsa
                    INNER JOIN zksr_trade.trd_supplier_after_dtl tsad ON tsa.after_id = tsad.after_id
                    WHERE tsa.supplier_order_no = tso.supplier_order_no
                    AND (
                        tsad.after_phase IN (2)  <!-- 发货后售后-->
                        OR tsad.approve_state = 0   <!-- 审核中-->
                        OR (tsad.approve_state = 1 AND tsad.refund_state IN (0, 1,3))  <!-- 已通过但退款处理中-->
                    )
                )
            )
            AND t.sys_code = #{sysCode}
            AND t.distribution_mode = 'O2O'
            <if test="orderNos != null and orderNos.size() > 0">
                AND tso.order_no IN
                <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="supplierOrderNos != null and supplierOrderNos.size() > 0">
                AND tso.supplier_order_no IN
                <foreach collection="supplierOrderNos" item="supplierOrderNo" open="(" separator="," close=")">
                    #{supplierOrderNo}
                </foreach>
            </if>
    </select>

</mapper>
