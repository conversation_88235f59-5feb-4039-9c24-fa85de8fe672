package com.zksr.product.api.combine.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/4 14:25
 * @注释
 */
@Data
@NoArgsConstructor
@ApiModel("组合商品实体")
public class SpuCombineDTO {

    /**
     * 组合商品id
     */
    @ApiModelProperty(value = "销售价6")
    private Long spuCombineId;

    /**
     * 平台商id
     */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 入驻商id
     */
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /**
     * 商品管理分类ID
     */
    @ApiModelProperty(value = "商品管理分类ID")
    private Long categoryId;

    /**
     * 组合商品编号
     */
    @ApiModelProperty(value = "组合商品编号")
    private String spuCombineNo;

    /**
     * 组合商品名
     */
    @ApiModelProperty(value = "组合商品名")
    private String spuCombineName;

    /**
     * 封面图（url）
     */
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /**
     * 封面视频（url）
     */
    @ApiModelProperty(value = "封面视频")
    private String thumbVideo;

    /**
     * 详情页轮播（json）
     */
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    /**
     * 详情信息(富文本)
     */
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 是否删除 1-是 0-否
     */
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /**
     * 状态 1-启用 0-停用
     */
    @ApiModelProperty(value = "状态 1-启用 0-停用")
    private Long status;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /**
     * 总限量
     */
    @ApiModelProperty(value = "总限量")
    private Long totalLimit;

    /**
     * 起订
     */
    @ApiModelProperty(value = "起订")
    private Long minOq;

    /**
     * 订货组数
     */
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;

    /**
     * 限购
     */
    @ApiModelProperty(value = "限购")
    private Long maxOq;

    /**
     * 单位-数据字典（sys_prdt_unit）
     */
    @ApiModelProperty(value = "单位-数据字典")
    private Long unit;

    /**
     * 标准价
     */
    @ApiModelProperty(value = "标准价")
    private BigDecimal markPrice;

    /**
     * 建议零售价
     */
    @ApiModelProperty(value = "建议零售价")
    private BigDecimal suggestPrice;

    /**
     * 销售价1
     */
    @ApiModelProperty(value = "销售价1")
    private BigDecimal salePrice1;

    /**
     * 销售价2
     */
    @ApiModelProperty(value = "销售价2")
    private BigDecimal salePrice2;

    /**
     * 销售价3
     */
    @ApiModelProperty(value = "销售价3")
    private BigDecimal salePrice3;

    /**
     * 销售价4
     */
    @ApiModelProperty(value = "销售价4")
    private BigDecimal salePrice4;

    /**
     * 销售价5
     */
    @ApiModelProperty(value = "销售价5")
    private BigDecimal salePrice5;

    /**
     * 销售价6
     */
    @ApiModelProperty(value = "销售价6")
    private BigDecimal salePrice6;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 限购类型
     */
    @ApiModelProperty(value = "限购类型")
    private Long timesRule;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 组合商品详情集合
     */
    @ApiModelProperty(value = "组合商品详情集合")
    private List<SpuCombineDtlRespDTO> prdtSpuCombineDtlList;
}
