# 多级分账接口文档



Ran tool
以下是关于“为什么需要多级分账和分账退回”的详细总结，以及你关心的名词解释和流程图，全部用小朋友能看懂的方式说明。

---

# 为什么需要多级分账和分账退回？

## 一、为什么要多级分账？

想象一下，你在网上买了一本书，这本书其实是由很多人一起合作卖给你的，比如：

- 有一个大商场（平台）帮你收钱
- 这个大商场把钱分给了一个卖书的商家（交易商户）
- 这个卖书的商家又要把钱分给出版社、快递公司、甚至推荐这本书的博主

这就像切蛋糕一样，蛋糕先分给大人，大人再分给小朋友，小朋友还可以再分给自己的朋友。  
**多级分账**就是让每个人都能拿到自己应该得到的那一份钱，大家都开心。

## 二、为什么要分账退回？

有时候，分蛋糕的时候出错了，比如：

- 有人分多了
- 有人不该拿到钱
- 买家退货了

这时候，就需要把多分出去的钱退回来，这个过程就叫**分账退回**。  
退回的钱会按照原路返回给该拿钱的人，保证大家的钱都对得上。

---

# 生僻名词小白解析

| 名词                | 小白解释                                                                 |
|---------------------|--------------------------------------------------------------------------|
| 合作者身份ID        | 就像你在学校的学号，是你在支付平台的唯一编号，别人不能和你一样。           |
| 商户分账订单号      | 每次分蛋糕（分账）时，给这次分蛋糕起的唯一编号，不能和别的分蛋糕重复。       |
| 交易商户 vs 商户    | 交易商户是最先收钱的那个商家，商户是所有参与分钱的商家，交易商户也是商户。   |
| 原商户交易订单号    | 你最早买东西时的那个订单号，是所有分账、退账的起点。                        |
| 分账退回的时机      | 当分错了钱、买家退货、或者有特殊情况时，就会发起分账退回。发起方通常是平台或商户，退回方登录号就是原来收钱的商户编号。|
| 分账退回后台通知    | 就像老师退回作业后要告诉你一声，平台退回了钱后，会自动通知商户，商户才能知道钱退回来了。|

---

# 分账和退账流程图

## 分账流程

文档：https://open.mideaepay.com/#/unionPage/apiInterface?id=1037&name=%E6%8B%85%E4%BF%9D

```mermaid
flowchart TD
    A[买家付款] --> B[钱进入平台]
    B --> C[平台分账给交易商户]
    C --> D1[交易商户分账给商户A]
    C --> D2[交易商户分账给商户B]
    D1 --> E1[商户A分账给个人用户]
    D2 --> E2[商户B分账给个人用户]
```

## 分账退回流程

```mermaid
flowchart TD
    F[发现有问题或需要退款] --> G[发起分账退回]
    G --> H[平台退回钱给原来的商户]
    H --> I[平台通知商户退回结果]
```

---

# 总结

- 多级分账让每个人都能拿到自己应得的钱，像分蛋糕一样一层一层分下去。
- 分账退回是为了修正分错的钱或买家退货时，把钱退回去，保证大家的钱都对。
- 平台会自动通知商户分账和退账的结果，大家都能及时知道自己的钱有没有到账。


## 基本参数（所有接口通用）

| 参数名         | 含义                   | 类型/长度      | 是否必填 | 说明（小白化）                   |
| -------------- | ---------------------- | -------------- | -------- | ------------------------------- |
| service        | 接口名称               | String(30)     | 是       | 具体接口的名字                   |
| version        | 接口版本号             | String(7/30)   | 是       | 固定为3.0.0                      |
| req_seq_no     | 请求序列号             | String(32)     | 是       | 每次请求唯一编号，不能重复        |
| partner        | 合作者身份ID/商户号    | String(10)     | 是       | 你在美的支付的商户编号，10位数字  |
| input_charset  | 参数编码字符集         | String(5)      | 是       | 固定为UTF-8                      |
| language       | 语言类型               | String(16)     | 否       | 例如ZH-CN表示中文                |
| terminal_type  | 终端类型               | String(10)     | 否       | 例如MOBILE（手机）、PC（电脑）、ITG（后台） |
| sign_type      | 签名方式               | String(10)     | 是       | 固定为MD5_RSA_TW，表示加密方式              |
| sign           | 签名                   | String         | 是       | 用于校验数据是否被篡改                      |
| notify_url     | 异步通知地址           | String(255)    | 否       | 支付平台通知你结果的地址                    |

---

## 1. 分账下单

**请求地址**：  
生产：https://in.mideaepay.com/gateway.htm  
测试：https://in.mideaepayuat.com/gateway.htm  
**请求方法**：POST

### 业务参数

| 参数名                  | 含义                   | 类型/长度      | 是否必填 | 说明（小白化）                                                                 |
| ----------------------- | ---------------------- | -------------- | -------- | ------------------------------------------------------------------------------ |
| out_profit_no           | 商户分账订单号         | String(32)     | 是       | 你自己系统里唯一的分账单号，每次分账要不一样                                   |
| out_trade_no            | 原商户交易订单号       | String(32)     | 是       | 原始支付订单号                                                                 |
| profit_sharing_details  | 分账详情               | String         | 否       | 分账的具体分配方案，json格式，见下文详细说明                                   |
| profit_sharing_type     | 分账类型               | String(10)     | 是       | 固定为FIX，表示固定金额分账                                                    |
| release                 | 释放标志               | String(5)      | 是       | TRUE表示剩余钱直接给收款商户，FALSE表示还可以继续分账                          |
| attach                  | 商户附言               | String(256)    | 否       | 你自定义的备注信息，平台会原样返回                                             |
| out_profit_time         | 提交订单时间           | String(14)     | 是       | 下单时间，格式yyyyMMddHHmmss                                                   |

#### 【分账接收方列表】详细说明

分账详情（profit_sharing_details）是一个嵌套的json结构，描述了钱如何被分给不同的人或商户。  
**每个分账接收方包含：**

| 字段名           | 含义           | 类型/长度   | 是否必填 | 说明（小白化）                                                                 |
| ---------------- | -------------- | ----------- | -------- | ------------------------------------------------------------------------------ |
| user_login_name  | 登录名         | String(10)  | 是       | 收钱的人或商户的编号                                                           |
| user_type        | 用户类型       | String(1)   | 是       | B=商户，C=个人                                                                 |
| pay_amount       | 分账金额       | String(20)  | 否       | 分给这个人的钱，单位是“分”（1元=100分）                                        |
| desc             | 分账描述       | String(50)  | 否       | 这笔钱为什么要分给他                                                           |
| receivers        | 下级分账列表   | List        | 否       | 如果这个人还要再分给别人，这里继续列出下一级分账对象                            |

**举例说明：**  
假如有1000元要分账，分给商户104（500元），商户104再分给商户105（300元）、个人lzw（100元）、商户106（100元），还直接分给个人lzw（200元）。  
json结构如下：

```json
[
  {
    "user_login_name": "1000000104",
    "user_type": "B",
    "pay_amount": "500",
    "desc": "分给商户104",
    "receivers": [
      {
        "user_login_name": "1000011005",
        "user_type": "B",
        "pay_amount": "300",
        "desc": "分给商户105",
        "receivers": [
          {
            "user_login_name": "lzw",
            "user_type": "C",
            "pay_amount": "100",
            "desc": "分给个人用户lzw"
          },
          {
            "user_login_name": "1000000106",
            "user_type": "B",
            "pay_amount": "100",
            "desc": "分给商户106"
          }
        ]
      }
    ]
  },
  {
    "user_login_name": "lzw",
    "user_type": "C",
    "pay_amount": "200",
    "desc": "分给lzw"
  }
]
```

**通俗解释：**  
- “分账接收方列表”就像分蛋糕，每个人可以再把自己的蛋糕分给别人，层层递进。
- 每一级的“receivers”就是下一级要分给谁，金额是多少。
- 如果某一级分账失败，整个分账就算失败，只有所有人都分成功才算成功。

---

## 2. 后台通知

**请求地址**：下单时传入的notify_url  
**请求方法**：POST

### 业务参数

| 参数名                  | 含义                   | 类型/长度      | 是否必填 | 说明（小白化）                                                                 |
| ----------------------- | ---------------------- | -------------- | -------- | ------------------------------------------------------------------------------ |
| partner                 | 合作者身份ID           | String(10)     | 是       | 你的商户编号                                                                   |
| out_profit_no           | 商户分账订单号         | String(32)     | 是       | 你自己系统里的分账单号                                                         |
| out_trade_no            | 原商户交易订单号       | String(32)     | 是       | 原始支付订单号                                                                 |
| profit_no               | 支付系统分账订单号     | String(25)     | 是       | 平台生成的分账流水号                                                           |
| profit_sharing_details  | 分润详情               | String         | 否       | 分账的具体分配方案，json格式                                                   |
| total_amount            | 分账总金额             | String         | 否       | 本次分账总金额                                                                 |
| release_amount          | 释放金额               | String         | 否       | 剩余释放给收款商户的金额                                                       |
| release                 | 释放标志               | String(5)      | 是       | TRUE=剩余钱直接给收款商户，FALSE=还可继续分账                                  |
| trade_status            | 订单支付状态           | String(20)     | 是       | SUCCESS=成功，FAIL=失败                                                        |
| trade_status_info       | 订单状态描述           | String(32)     | 否       | 订单状态的详细描述                                                             |
| profit_sharing_accept_time | 订单接收时间        | String(14)     | 否       | 平台接收订单的时间                                                             |
| pay_time                | 分账成功时间           | String(14)     | 否       | 分账成功的时间                                                                 |
| notify_time             | 通知时间               | String(14)     | 否       | 平台通知你的时间                                                               |
| attach                  | 商户附言               | String(5)      | 否       | 你自定义的备注信息                                                             |
| error_code              | 失败错误码             | String(9)      | 否       | 分账失败时的错误码                                                             |
| error_info              | 失败错误描述           | String(128)    | 否       | 分账失败时的详细原因                                                           |

---

## 3. 分账查询

**请求地址**：  
生产：https://in.mideaepay.com/gateway.htm  
测试：https://in.mideaepayuat.com/gateway.htm  
**请求方法**：POST

### 业务参数

| 参数名         | 含义           | 类型/长度   | 是否必填 | 说明（小白化）                         |
| -------------- | -------------- | ---------- | -------- | -------------------------------------- |
| out_profit_no  | 商户分账订单号 | String(32) | 是       | 你自己系统里的分账单号                 |

**返回业务参数（部分）：**

| 参数名                  | 含义                   | 类型/长度      | 是否必填 | 说明（小白化）                                                                 |
| ----------------------- | ---------------------- | -------------- | -------- | ------------------------------------------------------------------------------ |
| partner                 | 合作者身份ID           | String(10)     | 否       | 你的商户编号                                                                   |
| out_profit_no           | 商户分账订单号         | String(32)     | 是       | 你自己系统里的分账单号                                                         |
| out_trade_no            | 原商户交易订单号       | String(32)     | 是       | 原始支付订单号                                                                 |
| profit_no               | 支付系统分账订单号     | String(25)     | 否       | 平台生成的分账流水号                                                           |
| profit_sharing_details  | 分润详情               | String         | 否       | 分账的具体分配方案，json格式                                                   |
| release                 | 解冻标志               | String(5)      | 是       | TRUE=剩余钱直接给收款商户，FALSE=还可继续分账                                  |
| total_amount            | 分账总金额             | String         | 否       | 本次分账总金额                                                                 |
| release_amount          | 释放金额               | String         | 否       | 剩余释放给收款商户的金额                                                       |
| attach                  | 商户附言               | String(5)      | 否       | 你自定义的备注信息                                                             |
| trade_status            | 订单支付状态           | String(20)     | 否       | NOT_EXIST=订单不存在，WAIT_PAY=已接收，SUCCESS=成功，FAIL=失败                 |
| trade_status_info       | 订单状态描述           | String(32)     | 否       | 订单状态的详细描述                                                             |
| profit_sharing_accept_time | 订单接收时间        | String(14)     | 否       | 平台接收订单的时间                                                             |
| pay_time                | 分账成功时间           | String(14)     | 否       | 分账成功的时间                                                                 |
| error_code              | 失败错误码             | String(9)      | 否       | 分账失败时的错误码                                                             |
| error_info              | 失败错误描述           | String(128)    | 否       | 分账失败时的详细原因                                                           |

---

## 4. 分账退回

**请求地址**：  
生产：https://in.mideaepay.com/gateway.htm  
测试：https://in.mideaepayuat.com/gateway.htm  
**请求方法**：POST

### 业务参数

| 参数名             | 含义               | 类型/长度   | 是否必填 | 说明（小白化）                         |
| ------------------ | ------------------ | ---------- | -------- | -------------------------------------- |
| out_profit_no      | 原商户分账订单号   | String(32) | 是       | 你原来分账的订单号                     |
| out_refund_no      | 商户退回订单号     | String(32) | 是       | 你自己系统里的退回单号                 |
| return_login_name  | 退回方登录号       | String(10) | 是       | 退回钱的商户编号（目前只支持退回到商户）|
| refund_amount      | 退回金额           | String(20) | 是       | 退回的钱，单位“分”                     |
| currency_type      | 货币类型           | String(3)  | 否       | 货币类型，CNY表示人民币                |
| out_refund_time    | 提交订单时间       | String(14) | 是       | 下单时间，格式yyyyMMddHHmmss           |
| desc               | 分账退回描述       | String(50) | 否       | 退回原因说明                           |
| attach             | 商户附言           | String(256)| 否       | 你自定义的备注信息                     |
| risk_params        | 风控参数           | String     | 是       | 风险控制相关参数，具体见风控参数说明   |

---

## 5. 分账退回查询

**请求地址**：  
生产：https://in.mideaepay.com/gateway.htm  
测试：https://in.mideaepayuat.com/gateway.htm  
**请求方法**：POST

### 业务参数

| 参数名         | 含义               | 类型/长度   | 是否必填 | 说明（小白化）                         |
| -------------- | ------------------ | ---------- | -------- | -------------------------------------- |
| out_refund_no  | 商户退回订单号     | String(32) | 是       | 你自己系统里的退回单号                 |

**返回业务参数（部分）：**

| 参数名             | 含义               | 类型/长度   | 是否必填 | 说明（小白化）                         |
| ------------------ | ------------------ | ---------- | -------- | -------------------------------------- |
| refund_no          | 退回订单号         | String(25) | 否       | 平台生成的退回交易单号                 |
| out_profit_no      | 原商户分账订单号   | String(32) | 否       | 你原来分账的订单号                     |
| refund_accept_time | 交易订单创建时间   | String(14) | 否       | 平台创建订单的时间                     |
| return_login_name  | 退回方登录号       | String(10) | 否       | 退回钱的商户编号                       |
| currency_type      | 货币类型           | String(10) | 否       | CNY表示人民币                          |
| refund_amount      | 退回金额           | long       | 否       | 退回的钱，单位“分”                     |
| pay_time           | 支付订单时间       | String(14) | 否       | 平台支付订单的时间                     |
| refund_status      | 订单状态           | String(10) | 是       | NOT_EXIST=订单不存在，WAIT_PAY=未支付，SUCCESS=成功，FAIL=失败 |
| refund_status_info | 状态描述           | String(32) | 否       | 订单状态的详细描述                     |
| error_code         | 失败错误码         | String(9)  | 否       | 退回失败时的错误码                     |
| error_info         | 失败错误描述       | String(128)| 否       | 退回失败时的详细原因                   |
| attach             | 商户自定义信息     | String(256)| 否       | 你自定义的备注信息                     |

---

## 6. 分账退回后台通知

**请求地址**：下单时传入的notify_url  
**请求方法**：POST

### 业务参数

| 参数名             | 含义               | 类型/长度   | 是否必填 | 说明（小白化）                         |
| ------------------ | ------------------ | ---------- | -------- | -------------------------------------- |
| out_refund_no      | 商户退回订单号     | String(32) | 是       | 你自己系统里的退回单号                 |
| refund_no          | 退回订单号         | String(25) | 是       | 平台生成的退回交易单号                 |
| out_profit_no      | 原商户分账单号     | String(32) | 是       | 你原来分账的订单号                     |
| refund_accept_time | 退回单创建时间     | String(14) | 否       | 平台创建退回单的时间                   |
| notify_time        | 通知时间           | String(14) | 是       | 平台通知你的时间                       |
| return_login_name  | 退回方登录号       | String(10) | 是       | 退回钱的商户编号                       |
| currency_type      | 货币类型           | String(3)  | 否       | CNY表示人民币                          |
| refund_amount      | 退回金额           | long       | 是       | 退回的钱，单位“分”                     |
| pay_time           | 退回执行时间       | String(14) | 否       | 退回交易执行的时间                     |
| refund_status      | 退回结果           | String(10) | 是       | SUCCESS=成功，FAIL=失败                |
| refund_status_info | 退回结果信息描述   | String(256)| 是       | 退回结果的详细描述                     |
| error_code         | 失败错误码         | String(9)  | 否       | 退回失败时的错误码                     |
| error_info         | 失败错误描述       | String(128)| 否       | 退回失败时的详细原因                   |
| attach             | 商户自定义信息     | String(256)| 否       | 你自定义的备注信息                     |

---

> **温馨提示：**  
> - “分账接收方列表”可以无限嵌套，像树一样分下去，每一级都可以再分给下一级。  
> - 所有金额单位都是“分”，1元=100分。  
> - 只有所有分账都成功，整个分账才算成功。  
> - 失败时请关注error_code和error_info字段。

