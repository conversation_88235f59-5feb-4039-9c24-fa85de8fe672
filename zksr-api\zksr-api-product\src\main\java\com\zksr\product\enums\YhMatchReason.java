package com.zksr.product.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货匹配失败原因
 * @date 2024/12/10 10:10
 */
@Getter
public enum YhMatchReason {
    NONE(-1, "无"),
    STOCK_DEFICIENCY(0, "库存不足"),
    NOT_RELEASE(1, "未上架"),
    UNDEFINED(2, "匹配失败"),
    ;
    private Integer state;
    private String name;

    YhMatchReason(Integer state, String name) {
        this.state = state;
        this.name = name;
    }

    public static YhMatchReason formValue(Integer failReason) {
        if (Objects.isNull(failReason)) {
            return NONE;
        }
        for (YhMatchReason value : values()) {
            if (value.getState().equals(failReason)) {
                return value;
            }
        }
        return NONE;
    }
}
