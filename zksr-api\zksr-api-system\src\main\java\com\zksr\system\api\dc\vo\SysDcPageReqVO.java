package com.zksr.system.api.dc.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import java.math.BigDecimal;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 运营商对象 sys_dc
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@ApiModel("运营商 - sys_dc分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysDcPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /**
     * 运营商编号
     */
    @ApiModelProperty(value = "联系电话")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /**
     * 平台编号
     */
    @Excel(name = "平台编号")
    @ApiModelProperty(value = "平台编号")
    private Long sysCode;

    /**
     * 运营商状态0=正常,1=停用（0正常 1停用）
     */
    @Excel(name = "运营商状态0=正常,1=停用", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "运营商状态0=正常,1=停用", example = "0")
    private String status;
    /**
     * 运营商备注
     */
    @Excel(name = "运营商备注")
    @ApiModelProperty(value = "运营商备注")
    private String memo;

    /**
     * 运营商地址
     */
    @Excel(name = "运营商地址")
    @ApiModelProperty(value = "运营商地址")
    private String address;

    /**
     * 运营商编号
     */
    @Excel(name = "运营商编号")
    @ApiModelProperty(value = "运营商编号")
    private String dcCode;

    /**
     * 运营商名称
     */
    @Excel(name = "运营商名称")
    @ApiModelProperty(value = "运营商名称")
    private String dcName;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contractName;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contractPhone;

    /**
     * 本地起送价
     */
    @Excel(name = "本地起送价")
    @ApiModelProperty(value = "本地起送价")
    private BigDecimal minAmt;

    /**
     * 全国起送价
     */
    @Excel(name = "全国起送价")
    @ApiModelProperty(value = "全国起送价")
    private BigDecimal globalMinAmt;

}
