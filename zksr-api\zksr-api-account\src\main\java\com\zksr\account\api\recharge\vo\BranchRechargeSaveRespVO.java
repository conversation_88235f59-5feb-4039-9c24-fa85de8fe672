package com.zksr.account.api.recharge.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店创建充值单
 * @date 2024/3/23 15:19
 */
@Data
@NoArgsConstructor
@ApiModel(description = "门店创建充值单")
public class BranchRechargeSaveRespVO {

    @ApiModelProperty("充值单号")
    private String rechargeNo;

    public BranchRechargeSaveRespVO(String rechargeNo) {
        this.rechargeNo = rechargeNo;
    }
}
