package com.zksr.report.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月24小时下单分布数据
 * @date 2024/12/11 14:52
 */
@Data
@NoArgsConstructor
@ApiModel(description = "月24小时下单分布数据")
public class ColonelHoursSaleRespVO {

    @ApiModelProperty("小时销售数据")
    private List<HoursSaleVO> hoursSaleList;

    public ColonelHoursSaleRespVO(List<HoursSaleVO> hoursSaleList) {
        this.hoursSaleList = hoursSaleList;
    }
}
