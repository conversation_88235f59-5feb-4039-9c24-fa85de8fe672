package com.zksr.promotion.api.coupon.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单商品验证优惠券信息
 * @date 2024/4/3 11:40
 */
@Data
@ToString
@ApiModel(description = "订单商品验证优惠券信息")
public class OrderValidItemDTO {

    @NotNull(message = "carId 不能为空")
    @ApiModelProperty(value = "购物车ID", required = true)
    private String carId;

    @Min(value = 1, message = "商品数量最小1个")
    @ApiModelProperty(value = "商品数量", required = true)
    private Integer num;

    @ApiModelProperty(value = "金额", required = true, hidden = true, notes = "产品售价, 不需要前端传入")
    private BigDecimal amt;

    @ApiModelProperty(value = "skuId", required = true, hidden = true, notes = "不需要前端传入")
    private Long spuId;

    @ApiModelProperty(value = "skuId", required = true, hidden = true, notes = "不需要前端传入")
    private Long skuId;

    @ApiModelProperty(value = "品牌ID", required = true, hidden = true, notes = "不需要前端传入")
    private Long brandId;

    @ApiModelProperty(value = "展示类目ID", required = true, hidden = true, notes = "不需要前端传入")
    private Long classId;

    @ApiModelProperty(value = "管理分类ID", required = true, hidden = true, notes = "不需要前端传入")
    private Long categoryId;

    @ApiModelProperty(value = "入驻商ID", required = true, hidden = true, notes = "不需要前端传入")
    private Long supplierId;

    /**
     * {@link ProductType}
     */
    @ApiModelProperty(value = "商品类型", required = true, hidden = true, notes = "不需要前端传入")
    private String productType;

    @ApiModelProperty(value = "unit, 单位")
    private String unit = StringPool.ZERO;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize = 1;

    @JsonIgnore
    public String getUniqueKey() {
        return StringUtils.format("{}_{}", skuId, unitSize);
    }
}
