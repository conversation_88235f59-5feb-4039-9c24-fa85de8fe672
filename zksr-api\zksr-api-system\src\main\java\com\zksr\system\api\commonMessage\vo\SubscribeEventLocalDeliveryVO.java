package com.zksr.system.api.commonMessage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 本地配送商品订阅消息事件
 * @date 2024/6/14 15:28
 */
@ApiModel(description = "本地配送商品订阅消息事件")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class SubscribeEventLocalDeliveryVO {

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("入驻商ID")
    private Long supplierId;
}
