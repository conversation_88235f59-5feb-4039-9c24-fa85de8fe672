# O2O订单生成结算和分账功能实现说明

## 实现概述

已成功实现 `TrdOrderServiceImpl#orderO2OGenerateSettleDivideDtl` 方法，该方法用于根据指定条件查询需要生成分账的O2O订单，并为这些订单生成结算和分账记录。

## 实现的功能

### 1. 查询需要生成分账的订单

实现了复杂的SQL查询，包含以下条件：
- 签收时间距离当前时间已满8天且不超过30天
- 订单状态不是取消状态（已付款、货到付款未付款、货到付款已付款）
- 排除取消和拒收状态的订单
- 订单有实际数量（排除全部取消的订单）
- 在分账系统中没有分账记录
- 售后状态条件：没有售后或所有售后都已完成

```
    -- explain
    select tso.supplier_order_no, tsod.receive_time,tso.pay_state,tsod.delivery_state,tsod.total_num,tsod.cancel_qty
    FROM
    zksr_trade.trd_supplier_order tso
    INNER JOIN zksr_trade.trd_supplier_order_dtl tsod ON tso.supplier_order_id = tsod.supplier_order_id
    INNER JOIN zksr_trade.trd_order t ON tso.order_id = t.order_id
    WHERE
    -- 1. 签收时间距离当前时间已满8天
    tsod.receive_time IS NOT null
    AND DATEDIFF(NOW(), tsod.receive_time) >= 8
    AND DATEDIFF(NOW(), tsod.receive_time) <= 30
    -- 2. 订单状态不是取消状态
    AND tso.pay_state IN (1,3)  -- 已付款、货到付款未付款、货到付款已付款
    AND tsod.delivery_state !=50  -- 排除取消和拒收状态
    -- 3. 订单有实际数量（排除全部取消的订单）
    AND (tsod.total_num - tsod.cancel_qty) > 0
    -- 4. 在分账系统中没有分账记录
    AND NOT EXISTS (
    SELECT 1
    FROM acc_divide_dtl add_check
    WHERE add_check.trade_no = tso.supplier_order_no
    AND add_check.merchant_type = 'SUPPLIER'
    AND add_check.merchant_id = tso.supplier_id
    )
    -- 5. 售后状态条件：没有售后或所有售后都已完成
    AND (
    -- 5.1 没有任何售后申请
    NOT EXISTS (
    SELECT 1
    FROM zksr_trade.trd_supplier_after tsa
    WHERE tsa.supplier_order_no = tso.supplier_order_no
    )
    OR
    -- 5.2 所有售后都已完成处理（不存在进行中的售后）
    NOT EXISTS (
    SELECT 1
    FROM zksr_trade.trd_supplier_after tsa
    INNER JOIN zksr_trade.trd_supplier_after_dtl tsad ON tsa.after_id = tsad.after_id
    WHERE tsa.supplier_order_no = tso.supplier_order_no
    AND (
    tsad.after_phase IN (2)  -- 发货后售后
    OR tsad.approve_state = 0   -- 审核中
    OR (tsad.approve_state = 1 AND tsad.refund_state IN (0, 1,3))  -- 已通过但退款处理中
    )
    )
    )
    -- AND t.sys_code = #{sysCode}
    AND t.distribution_mode = 'O2O'
```

### 2. 生成结算和分账记录

对于每个符合条件的订单：
- 调用现有的 `createO2OSettleAndDivide` 方法
- 生成 TrdSettle 结算记录
- 创建分账流水记录
- 更新订单结算状态

## 新增的文件

1. **O2OGenerateSettleOrderDTO.java** - 查询结果DTO
    - 包含入驻商订单编号和签收时间

2. **TrdSupplierOrderMapper.xml** - 新增SQL查询
    - `selectO2OOrdersForDivide` 方法实现复杂的多表关联查询

## 修改的文件

1. **TrdOrderServiceImpl.java**
    - 实现 `orderO2OGenerateSettleDivideDtl` 主方法
    - 添加 `processO2OOrderSettleAndDivide` 私有方法
    - 添加必要的依赖注入

2. **TrdSupplierOrderMapper.java**
    - 添加 `selectO2OOrdersForDivide` 方法声明
    - 添加必要的导入

3. **TrdSupplierOrderSaveReqVO.java**
    - 添加 @Builder 注解支持

## 方法调用流程

```
orderO2OGenerateSettleDivideDtl(paramVO)
  ↓
查询需要生成分账的订单 (selectO2OOrdersForDivide)
  ↓
遍历每个订单
  ↓
processO2OOrderSettleAndDivide(supplierOrderNo)
  ↓
调用 trdSupplierAfterService.createO2OSettleAndDivide()
  ↓
生成结算记录和分账流水
```

## 错误处理

- 对单个订单处理失败不会影响其他订单的处理
- 详细的日志记录便于问题排查
- 适当的异常处理和错误信息

## 使用方式

该方法通过定时任务调用，参数包括：
- `sysCode`: 系统编码
- `orderNos`: 可选的订单号列表
- `supplierOrderNos`: 可选的入驻商订单号列表

## 注意事项

1. 该实现复用了现有的结算和分账逻辑
2. 查询条件严格，确保只处理符合条件的订单
3. 支持批量处理，提高效率
4. 具有良好的容错性和可维护性
	