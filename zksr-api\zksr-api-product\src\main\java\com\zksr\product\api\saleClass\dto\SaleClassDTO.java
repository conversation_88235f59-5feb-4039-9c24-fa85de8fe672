package com.zksr.product.api.saleClass.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 平台商展示分类对象 prdt_sale_class
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleClassDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 平台商展示分类id
     */
    private Long saleClassId;

    /**
     * 平台商id
     */
    private Long sysCode;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父id
     */
    private Long pid;

    /**
     * 分类图标
     */
    private String icon;

    /**
     * 备注
     */
    private String memo;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 状态
     */
    private Long status;

    /**
     * 平台商城市分组id
     */
    private Long groupId;

    /** 是否展示生产日期 0-关闭, 1-开启 */
    @Excel(name = "是否展示生产日期 0-关闭, 1-开启")
    @ApiModelProperty(value = "是否展示生产日期 0-关闭, 1-开启")
    private Integer showProduceDate = NumberPool.INT_ONE;

    /** 生产日期格式 yy/MM/dd 年月日 yy/MM 年月 */
    @Excel(name = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    @ApiModelProperty(value = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    private String produceDateFormat;

}
