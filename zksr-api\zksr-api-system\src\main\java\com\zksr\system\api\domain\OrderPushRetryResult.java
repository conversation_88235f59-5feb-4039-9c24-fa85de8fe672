package com.zksr.system.api.domain;

import lombok.Data;

/**
 * @Description: 订单推送重试结果
 * @Date: 2025/07/22
 */
@Data
public class OrderPushRetryResult {
    
    /** 扫描的记录数量 */
    private Integer scannedCount = 0;
    
    /** 重推成功数量 */
    private Integer successCount = 0;
    
    /** 重推失败数量 */
    private Integer failedCount = 0;
    
    /** 跳过的数量（已达到最大重试次数） */
    private Integer skippedCount = 0;
    
    /** 执行开始时间 */
    private Long startTime;
    
    /** 执行结束时间 */
    private Long endTime;
    
    /** 执行耗时（毫秒） */
    private Long duration;
    
    /** 错误信息 */
    private String errorMessage;

    /**
     * 增加扫描数量
     */
    public void incrementScannedCount() {
        this.scannedCount++;
    }

    /**
     * 增加成功数量
     */
    public void incrementSuccessCount() {
        this.successCount++;
    }

    /**
     * 增加失败数量
     */
    public void incrementFailedCount() {
        this.failedCount++;
    }

    /**
     * 增加跳过数量
     */
    public void incrementSkippedCount() {
        this.skippedCount++;
    }

    /**
     * 计算执行耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (scannedCount == 0) {
            return 0.0;
        }
        return (double) successCount / scannedCount * 100;
    }

    /**
     * 获取处理总数（成功+失败，不包括跳过）
     */
    public int getProcessedCount() {
        return successCount + failedCount;
    }

    @Override
    public String toString() {
        return String.format(
            "OrderPushRetryResult{扫描=%d, 成功=%d, 失败=%d, 跳过=%d, 成功率=%.2f%%, 耗时=%dms}",
            scannedCount, successCount, failedCount, skippedCount, getSuccessRate(), duration
        );
    }
}
