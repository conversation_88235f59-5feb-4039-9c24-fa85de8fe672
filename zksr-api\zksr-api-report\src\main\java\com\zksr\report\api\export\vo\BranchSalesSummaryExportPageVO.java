package com.zksr.report.api.export.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("门店月销售汇总请求实体 -  Request VO")
public class BranchSalesSummaryExportPageVO extends PageParam {
    @ApiModelProperty(value = "选择查询月份，格式为 yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date monthId; // 查询月份，格式为 yyyy-MM

    @ApiModelProperty(value = "门店编号")
    private String branchId; // 门店编号

    @ApiModelProperty(value = "门店名称")
    private String branchName; // 门店名称

    @ApiModelProperty(value = "平台商编号")
    private Long sysCode;
}
