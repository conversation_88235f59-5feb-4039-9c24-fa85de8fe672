package com.zksr.promotion.api.coupon.handler;

import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.promotion.api.coupon.handler.applySpuScope.BrandCouponCouponSpuScopeStrategy;
import com.zksr.promotion.api.coupon.handler.applySpuScope.ClassCouponCouponSpuScopeStrategy;
import com.zksr.promotion.api.coupon.handler.applySpuScope.QcCouponSpuScopeStrategy;
import com.zksr.promotion.api.coupon.handler.applySpuScope.SpuCouponCouponSpuScopeStrategy;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 处理全国商品
 * @date 2024/4/3 15:59
 */
public class AllProductSpuScopeStrategy implements CouponSpuScopeStrategy{

    static List<CouponSpuScopeStrategy> applySpuScopeList = new ArrayList<>();
    static {
        // 全场券
        applySpuScopeList.add(new QcCouponSpuScopeStrategy());
        // 类别券
        applySpuScopeList.add(new ClassCouponCouponSpuScopeStrategy());
        // 品牌券
        applySpuScopeList.add(new BrandCouponCouponSpuScopeStrategy());
        // 商品券
        applySpuScopeList.add(new SpuCouponCouponSpuScopeStrategy());
        // 入驻商券
        //applySpuScopeList.add(new SupplierCouponCouponSpuScopeStrategy());
    }

    public static AllProductSpuScopeStrategy getInstance() {
        return new AllProductSpuScopeStrategy();
    }

    @Override
    public void productFilter(List<OrderValidItemDTO> items, Set<CouponDTO> coupons, Map<Long, CouponTemplateDTO> couponTemplateMap, Set<CouponDTO> availableSet) {
        // 全部商品
        for (CouponSpuScopeStrategy spuScopeStrategy : applySpuScopeList) {
            spuScopeStrategy.productFilter(items, coupons, couponTemplateMap, availableSet);
        }
    }
}
