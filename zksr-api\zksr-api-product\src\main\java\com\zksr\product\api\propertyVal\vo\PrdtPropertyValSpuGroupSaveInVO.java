package com.zksr.product.api.propertyVal.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*
 * 商品基本信息新增保存 --规格值 请求实体
* <AUTHOR>
* @date 2024/3/2 11:35
*/
@Data
@ApiModel("商品基本信息新增保存 --规格值 请求实体")
public class PrdtPropertyValSpuGroupSaveInVO {

    /** 规格值id */
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值" ,required = true)
    private Long propertyValId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值" ,required = true)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", example = "示例值" ,required = true)
    private Long supplierId;

    /** 规格名称id */
    @Excel(name = "规格名称id")
    @ApiModelProperty(value = "规格名称id", example = "示例值" ,required = true)
    private Long propertyId;

    /** 规格值名称 */
    @Excel(name = "规格值名称")
    @ApiModelProperty(value = "规格值名称", example = "示例值" ,required = true)
    private String name;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id",  example = "商品SPU_id" ,required = true)
    private Long spuId;
}
