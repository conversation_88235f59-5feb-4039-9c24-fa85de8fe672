package com.zksr.product.api.yhdata.vo;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货批量删除请求
 * @date 2024/12/31 8:44
 */
@Data
public class YhBatchRemoveReqVO {

    @ApiModelProperty(value = "0-下单删除", notes = "因为什么原因删除")
    private Integer delType = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "业务单号", notes = "暂用于日志, 例如订单号")
    private String businessNo;

    @ApiModelProperty("要货ID集合")
    private List<Long> yhIdList;
}
