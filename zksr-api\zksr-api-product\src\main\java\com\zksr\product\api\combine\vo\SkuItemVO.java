package com.zksr.product.api.combine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/12/31 15:17
 * @注释
 */
@Data
@ApiModel("子商品信息 - VO")
public class SkuItemVO {


    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;


    /**
     * 商品SPU id
     */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /**
     * 商品sku id
     */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /**
     * 城市上架商品id
     */
    @Excel(name = "城市上架商品id")
    @ApiModelProperty(value = "城市上架商品id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaItemId;

    /**
     * 全国上架商品id
     */
    @Excel(name = "全国上架商品id")
    @ApiModelProperty(value = "全国上架商品id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierItemId;

    /**
     * 商品数量
     */
    @Excel(name = "商品数量")
    @ApiModelProperty(value = "商品数量")
    private Long qty;


    /**
     * 商品数量
     */
    @Excel(name = "是否为赠品")
    @ApiModelProperty(value = "是否为赠品")
    private Long giftFlag;

    /**
     * 单位-数据字典（sys_prdt_unit）
     */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典", example = "示例值")
    private Long unit;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @ApiModelProperty(name = "最小单位-数据字典")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @ApiModelProperty(name = "中单位-数据字典")
    private Long midUnit;

    /** 大单位-数据字典（sys_prdt_unit） */
    @ApiModelProperty(name = "大单位-数据字典")
    private Long largeUnit;

    /**
     * 显示单位
     */
    @Excel(name = "显示单位", readConverterExp = "显示单位")
    @ApiModelProperty(value = "显示单位")
    private String unitName;

    /**
     * 商品SPU名称
     */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /**
     * 国际条码
     */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码", example = "示例值")
    private String barcode;

    /**
     * 入驻商名称
     */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /**
     * 单买售价
     */
    @Excel(name = "单买售价")
    @ApiModelProperty(value = "单买售价", example = "示例值")
    private BigDecimal markPrice;


}
