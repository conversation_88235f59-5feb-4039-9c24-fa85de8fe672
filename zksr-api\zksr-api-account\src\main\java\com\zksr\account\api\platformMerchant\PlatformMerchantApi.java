package com.zksr.account.api.platformMerchant;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantWxb2bDTO;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/23
 * @desc 支付平台商户信息
 */
@FeignClient(
        contextId = "remotePlatformMerchantApi",
        value = ApiConstants.NAME
)
public interface PlatformMerchantApi {

    String PREFIX = ApiConstants.PREFIX + "";
    /**
     * 新增支付平台商户
     * @param platformMerchant  商户信息
     * @return  商户ID
     */
    @PostMapping(PREFIX + "/savePlatformMerchant")
    CommonResult<Long> savePlatformMerchant(@RequestBody PlatformMerchantDTO platformMerchant);


    /**
     * 获取平台商户信息
     * @param merchantType  商户类型参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param merchantId    商户ID, 入驻商ID, 业务员ID....
     * @param sysCode       平台ID
     * @return  商户信息
     */
    @PostMapping(PREFIX + "/getPlatformMerchantRespVO")
    CommonResult<AccPlatformMerchantRespVO> getPlatformMerchantRespVO(
            @RequestParam("merchantType") String merchantType,
            @RequestParam("merchantId") Long merchantId,
            @RequestParam("sysCode") Long sysCode
    );

    /**
     * 获取平台商户信息
     * @param merchantType  商户类型参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param merchantId    商户ID, 入驻商ID, 业务员ID....
     * @param sysCode       平台ID
     * @return  商户信息
     */
    @PostMapping(PREFIX + "/getPlatformMerchant")
    CommonResult<PlatformMerchantDTO> getPlatformMerchant(
            @RequestParam("merchantType") String merchantType,
            @RequestParam("merchantId") Long merchantId,
            @RequestParam("sysCode") Long sysCode
    );

    /**
     * 获取平台商户信息
     * @param merchantType  商户类型参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param merchantId    商户ID, 入驻商ID, 业务员ID....
     * @param payPlatform   支付平台
     * @return  商户信息
     */
    @PostMapping(PREFIX + "/getPlatformMerchant02")
    CommonResult<PlatformMerchantDTO> getPlatformMerchant(
            @RequestParam("merchantType") String merchantType,
            @RequestParam("merchantId") Long merchantId,
            @RequestParam("sysCode") String payPlatform
    );


    /**
     * 根据平台编号 获取支付平台商户的分账方商户编号和商户key(去重)
     */
    @PostMapping(PREFIX + "/getPlatformMerchantNameAndKey")
    CommonResult<List<PlatformMerchantNameAndKeyDTO>> getPlatformMerchantNameAndKey(
            @RequestParam("merchantType") String merchantType,
            @RequestParam("sysCode") Long sysCode,
            @RequestParam("payPlatform") String payPlatform
    );

    /**
     * 获取微信B2B商户配置信息
     * @param merchantId        商户ID
     * @param merchantType      商户类型
     * @return
     */
    @PostMapping(PREFIX + "/getWxB2bPlatformMerchant")
    CommonResult<PlatformMerchantWxb2bDTO> getWxB2bPlatformMerchant(@RequestParam("merchantId") Long merchantId, @RequestParam("merchantType") String merchantType);

    /**
     * 更新支付平台商户
     * @param platformMerchant  商户信息
     * @return  商户ID
     */
    @PostMapping(PREFIX + "/updatePlatformMerchant")
    CommonResult<Boolean> updatePlatformMerchant(@RequestBody PlatformMerchantDTO platformMerchant);

    /**
     * 商户进件
     * @param reqVO 进件信息
     * @return  结果
     */
    @PostMapping(PREFIX + "/register")
    CommonResult<Long> register(@RequestBody AccPlatformMerchantRegisterReqVO reqVO);

    /**
     * 更新商户资质
     * @param reqVO 资质信息
     * @return  结果
     */
    @PostMapping(PREFIX + "/uploadPic")
    CommonResult<Long> uploadPic(@RequestBody AccPlatformMerchantUploadPicReqVO reqVO);

    /**
     * 更新商户信息
     * @param reqVO 商户信息
     * @return  结果
     */
    @PostMapping(PREFIX + "/updateRegister")
    CommonResult<Long> updateRegister(@RequestBody AccPlatformMerchantRegisterReqVO reqVO);

    /**
     * 处理更新, 商户变更状态
     * @param minId 最小ID
     * @return  批次最大ID
     */
    @GetMapping(PREFIX + "/processMerchantStatus")
    CommonResult<Long> processMerchantStatus(@RequestParam("minId") Long minId);

    /**
     * 更新业务员绑定微信openid 信息
     * @param reqVO 更新请求
     * @return
     */
    @PostMapping(PREFIX + "/processMerchantStatus")
    CommonResult<Boolean> updateWxB2bColonelOpenidBind(@RequestBody WxB2bColonelOpenidBindReqVO reqVO);

    /**
     * 根据商户号获取入驻商Id
     * @param altNo
     * @param sysCode
     * @return
     */
    @PostMapping(PREFIX + "/getMerchantIdByAltNo")
    CommonResult<List<Long>> getMerchantIdByAltNo(@RequestParam("merchantType") String merchantType,@RequestParam("altNo") String altNo,@RequestParam("sysCode") Long sysCode);

    /**
     * 获取商户列表
     * @param pageReqVO 请求参数
     * @return  商户列表
     */
    @PostMapping(PREFIX + "/getMerchantList")
    CommonResult<List<AccPlatformMerchantRespVO>> getMerchantList(@RequestBody AccPlatformMerchantPageReqVO pageReqVO);
}
