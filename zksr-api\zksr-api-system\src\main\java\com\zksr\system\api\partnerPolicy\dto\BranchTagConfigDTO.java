package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 平台商客户标签配置
 * @date 2024/11/12 8:43
 */
@Data
public class BranchTagConfigDTO {

    @ApiModelProperty("客户活跃定义")
    private BranchActiveConfigDTO branchActive;

    @ApiModelProperty("客户等级定义")
    private List<BranchLevelConfigDTO> branchLevelConfig;

    @ApiModelProperty("客户销售类占比定义")
    private List<BranchSaleClassConfigDTO> saleClassConfig;

    @ApiModelProperty("客户毛利等级定义")
    private List<BranchProfitLevelConfigDTO> branchProfitLevelConfig;

    @ApiModelProperty("客户订货频次")
    private List<BranchCreateOrderConfigDTO> branchCreateOrderConfig;

    @ApiModelProperty("客户异常拜访规则")
    private BranchAbnormalVisitConfigDTO branchAbnormalVisitConfig;
}
