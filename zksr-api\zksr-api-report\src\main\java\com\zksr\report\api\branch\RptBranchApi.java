package com.zksr.report.api.branch;

import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.report.api.branch.dto.BranchTagConfigDTO;
import com.zksr.report.api.branch.vo.*;
import com.zksr.report.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/11/12
 * @desc
 */
@FeignClient(
        contextId = "remoteRptBranchApi",
        value = ApiConstants.NAME
)
public interface RptBranchApi {

    String PREFIX = ApiConstants.PREFIX + "/branch";

    /**
     * 保存门店标签配置
     */
    @PostMapping(value = PREFIX + "/saveBranchTagDTO")
    CommonResult<Boolean> saveBranchTagDTO(@RequestBody BranchTagConfigDTO branchTagConfigDTO);

    /**
     * 获取门店上月标签
     */
    @GetMapping(value = PREFIX + "/getBranchLastMonthTag")
    CommonResult<List<BranchTagEnum>> getBranchLastMonthTag(@RequestParam("branchId") Long branchId);

    /**
     * 统计业务员门店销售总计
     */
    @PostMapping(value = PREFIX + "/totalColonelBranchSale")
    CommonResult<BigDecimal> totalColonelBranchSale(@RequestBody RptColonelBranchReqVO reqVO);

    /**
     * 业务员首页门店标签统计
     */
    @PostMapping(value = PREFIX + "/indexBranchLevelTotal")
    CommonResult<ColonelIndexBranchLevelTotalRespVO> indexBranchLevelTotal(@RequestBody RptColonelBranchReqVO reqVO);

    /**
     * 业务员首页月周下单统计
     */
    @PostMapping(value = PREFIX + "/indexWeekSaleTotal")
    CommonResult<ColonelBranchMonthWeekTotalRespVO> indexWeekSaleTotal(@RequestBody RptColonelBranchReqVO reqVO);

    /**
     * 月订货类别占比
     */
    @PostMapping(value = PREFIX + "/indexSaleCategoryTotal")
    CommonResult<ColonelBranchSaleCategoryTotalRespVO> indexSaleCategoryTotal(@RequestBody RptColonelBranchReqVO rptColonelBranchReqVO);

    /**
     * 获取门店销售标签业务员列表
     */
    @PostMapping(value = PREFIX + "/branchLevelColonelList")
    CommonResult<ColonelLevelTotalRespVO> branchLevelColonelList(@RequestBody ColonelBranchTagPageReqVO reqVO);

    /**
     * 获取业务员标签门店列表
     */
    @PostMapping(value = PREFIX + "/tagLevelBranchList")
    CommonResult<PageResult<ColonelTagBranchRespVO>> tagLevelBranchList(@RequestBody TagBranchListReqVO reqVO);

    /**
     * 门店数据分析
     */
    @PostMapping(value = PREFIX + "/branchAnalyseInfo")
    CommonResult<BranchAnalyseInfoRespVO> branchAnalyseInfo(@RequestBody BranchAnalyseInfoReqVO reqVO);

    /**
     * 24小时下单分布
     */
    @PostMapping(value = PREFIX + "/getHoursSaleCount")
    CommonResult<ColonelHoursSaleRespVO> getHoursSaleCount(@RequestBody RptColonelBranchReqVO reqVO);
}
