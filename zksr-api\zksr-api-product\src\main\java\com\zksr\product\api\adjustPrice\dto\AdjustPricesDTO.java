package com.zksr.product.api.adjustPrice.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
* 商品调价单主表DTO
* @date 2024/11/8 9:36
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdjustPricesDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 单据ID */
    private Long adjustPricesId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 运营商ID */
    @Excel(name = "运营商ID")
    private Long dcId;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    private Long supplierId;

    /** 单据编号 */
    @Excel(name = "单据编号")
    private String adjustPricesNo;

    /** 审核状态：0-待审核,1-已审核,2-已作废 */
    @Excel(name = "审核状态：0-待审核,1-已审核,2-已作废")
    private Long approveState;

    /** 审核人Id */
    @Excel(name = "审核人Id")
    private Long approveUserId;

    /** 审核人 */
    @Excel(name = "审核人")
    private String approveBy;

    /** 审核时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "审核时间", width = 30, dateFormat = YYYY_MM_DD)
    private Date approveTime;

    /** 生效类型：0-定时生效，1-立即生效 */
    @Excel(name = "生效类型：0-定时生效，1-立即生效")
    private Long validType;

    /** 生效时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效时间", width = 30, dateFormat = YYYY_MM_DD)
    private Date validTime;

    /** SKU数 */
    @Excel(name = "SKU数")
    private Long skuNum;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔 */
    @Excel(name = "价格类型选项：标准价：markPrice, 成本价：costPrice，已，号分隔")
    private String priceTypeStr;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "运营商名称")
    private String dcName;

    @ApiModelProperty(value = "制单人")
    private String createBy;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "制单日期")
    private Date createTime;

    @ApiModelProperty(value = "定时调价执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;


    @ApiModelProperty(value = "单据详情集合")
    private List<AdjustPricesDtlDTO> dtlList;

}
