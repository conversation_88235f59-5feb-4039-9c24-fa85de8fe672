package com.zksr.account.api.platformMerchant.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件注册
 * @date 2024/7/12 11:13
 */
@ApiModel(description = "商户进件返回结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatformMerchantRegisterSaveRespVO {

    /**
     * {@link com.zksr.common.core.enums.MerchantRegisterStateEnum}
     */
    @ApiModelProperty("进件状态: INIT-待审核, OVERRULE-审核失败/驳回, AUDITED-审核通过")
    private String auditStatus;

    @ApiModelProperty("进件商户批次单号")
    private String orderNo;

    @ApiModelProperty("进件商户号")
    private String merchantNo;

    @ApiModelProperty("错误信息")
    private String msg;
}
