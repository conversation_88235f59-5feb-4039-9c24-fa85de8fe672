package com.zksr.promotion.api.coupon.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2025/2/26 16:18
 * @注释
 */
@Data
@ApiModel("优惠券领取记录 req VO")
public class CouponExportVO extends PageParam {

    private static final long serialVersionUID = 1L;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @ApiModelProperty(value = "优惠券模板id")
    private Long couponTemplateId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 门店id */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 领取的用户id */
    @Excel(name = "领取的用户id")
    @ApiModelProperty(value = "领取的用户id")
    private Long receiveMemberId;

    @Excel(name = "状态", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "状态,0-正常,2-已使用,3-已过期,4-已作废")
    private Integer state;

    /** 有效期开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效期开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效期开始时间")
    private Date expirationDateStart;

    /** 有效期结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "有效期结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "有效期结束时间")
    private Date expirationDateEnd;

    /** 使用时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "使用时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "使用时间")
    private Date useTime;

    /** 对应订单号 */
    @Excel(name = "对应订单号")
    @ApiModelProperty(value = "对应订单号")
    private String relateOrderNo;

    /** 优惠类型(数据字典);0-满减券  1-折扣券 */
    @Excel(name = "优惠类型(数据字典);0-满减券  1-折扣券")
    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券", required = true)
    private Integer discountType;

    /** 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（需要讨论返券规则，返券规则用扩展表存 如购买指定商品返券，支付满多少返券等等） */
    @Excel(name = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", readConverterExp = "需=要讨论返券规则，返券规则用扩展表存,如=购买指定商品返券，支付满多少返券等等")
    @ApiModelProperty(value = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券", required = true)
    private Integer receiveType;

    /** 领取开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "领取开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "领取开始时间")
    private Date createTime;

    /** 领取结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "领取结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "领取结束时间")
    private Date createTimeEnd;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    @ApiModelProperty(value = "全国或者本地(数据字典);1-全国商品可用")
    private Integer funcScope;

    @Excel(name = "运营商ID")
    @ApiModelProperty(value = "运营商ID")
    private Long dcId;

}
