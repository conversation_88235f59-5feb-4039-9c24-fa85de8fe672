package com.zksr.product.api.model.event;

import com.zksr.product.api.model.AbstractProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架事件
 * @date 2024/2/29 19:56
 */
@Data
public class EsReleaseProductEvent extends AbstractProductEvent {

    @ApiModelProperty("上架发布商品ID, 非商品ID")
    private List<Long> itemIds;

    public EsReleaseProductEvent(List<Long> itemIds) {
        this.itemIds = itemIds;
    }
}
