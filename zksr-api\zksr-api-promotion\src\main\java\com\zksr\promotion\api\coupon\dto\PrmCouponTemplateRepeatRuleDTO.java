package com.zksr.promotion.api.coupon.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券重复规则
 * @date 2024/5/7 17:57
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrmCouponTemplateRepeatRuleDTO {

    /** 优惠券模板重复规则 */
    @TableId
    private Long couponTemplateRepeatRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 重复类型 */
    @Excel(name = "重复类型")
    @ApiModelProperty(value = "重复类型(数据字典);repeat_flag=重复时设定 1-每天(每天可再次领取),2-每周(每周一可再次领取),3-每月(每月1号可再次领取),4-自定义")
    private Integer repeatType;

    /** 重复频率单位 */
    @Excel(name = "重复频率单位")
    @ApiModelProperty(value = "重复频率单位(数据字典);repeat_type=自定义时设定 0-天(每过几天可以再次领取)，1-周(每周的周几可以再次领取)，2-月(每个月的第几天可以再次领取)，3-年 (每年的几月可以重复领取)")
    private Integer repeatFrequencyUnit;

    /** 重复周期*/
    @Excel(name = "重复周期")
    @ApiModelProperty(value = "重复周期(数据字典);repeat_type=自定义时设定 0：重复频率单位为天时，可选范围为0~99，即表示每几天重复；1：重复频率单位为周时，可选范围为1-7；2：重复频率单位为月时，可选范围为1-31, 3: 每年的第几个月可以领取, 可选范围为1-12")
    private String repeatFrequencyPeriod;

    /** 总重复次数 */
    @Excel(name = "总重复次数")
    private Integer repaeatTimes;
}
