package com.zksr.system.api.partnerPolicy;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        contextId = "PartnerPolicyApi",
        value = ApiConstants.NAME
)
public interface PartnerPolicyApi {
    String PREFIX = ApiConstants.PREFIX + "/partnerPolicy";

    /**
     * @Description: 获取小程序配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getAppletAgreementPolicy")
    CommonResult<AppletAgreementPolicyDTO> getAppletAgreementPolicy(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取基础设置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getBasicSettingPolicy")
    CommonResult<BasicSettingPolicyDTO> getBasicSettingPolicy(@RequestParam("dcId") Long dcId);

    /**
     * @Description: 获取订单参数配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getOrderSettingPolicy")
    CommonResult<OrderSettingPolicyDTO> getOrderSettingPolicy(@RequestParam("dcId") Long dcId);

    /**
     * @Description: 获取提现配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/22 8:45
     */
    @GetMapping(PREFIX + "/getWithdrawalSettingPolicy")
    CommonResult<WithdrawalSettingPolicyDTO> getWithdrawalSettingPolicy(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取芯烨配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/22 8:45
     */
    @GetMapping(PREFIX + "/getXpYunSettingPolicy")
    CommonResult<XpYunSettingPolicyDTO> getXpYunSettingPolicy(@RequestParam("supplierId") Long supplierId);

    /**
     * @Description: 获取入驻商售后配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/22 8:45
     */
    @GetMapping(PREFIX + "/getAfterSaleSettingPolicy")
    CommonResult<AfterSaleSettingPolicyDTO> getAfterSaleSettingPolicy(@RequestParam("supplierId") Long supplierId);

    /**
     * @Description: 获取飞鹅配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/22 8:45
     */
    @GetMapping(PREFIX + "/getFeieYunSettingPolicy")
    CommonResult<FeieYunSettingPolicyDTO> getFeieYunSettingPolicy(@RequestParam("supplierId") Long supplierId);

    /**
     * @Description: 获取业务员配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/22 8:45
     */
    @GetMapping(PREFIX + "/getColonelSettingPolicy")
    CommonResult<ColonelSettingPolicyDTO> getColonelSettingPolicy(@RequestParam("dcId") Long dcId);

    /**
     * @Description: 获取入驻商对外系统配置
     * @Author: liuxingyu
     * @Date: 2024/5/28 8:45
     */
    @GetMapping(PREFIX + "/getOpenSettingPolicy")
    CommonResult<OpenSettingPolicyDTO> getOpenSettingPolicy(@RequestParam("supplierId") Long supplierId);

    /**
     * 获取平台商小程序配置信息
     * @param sysCode
     * @return
     */
    @GetMapping(PREFIX + "/getPartnerMiniSettingPolicy")
    CommonResult<PartnerMiniSettingPolicyDTO> getPartnerMiniSettingPolicy(@RequestParam("sysCode") Long sysCode);

    /**
     * 获取入驻商其他配置信息
     * @param supplierId
     * @return
     */
    @GetMapping(PREFIX + "/getPartnerSupplierOtherSettingPolicy")
    CommonResult<SupplierOtherSettingPolicyDTO> getPartnerSupplierOtherSettingPolicy(@RequestParam("supplierId") Long supplierId);

    /**
     * 根据区域城市ID获取搜索配置
     * @param areaId
     * @return
     */
    @GetMapping(PREFIX + "/getSearchConfig")
    CommonResult<SearchConfigDTO> getSearchConfig(@RequestParam("areaId") Long areaId);

    /**
     * 获取运营商其他配置
     * @param dcId  运营商ID
     * @return  运营商其他配置
     */
    @GetMapping(PREFIX + "/getDcOtherSettingDTO")
    CommonResult<DcOtherSettingPolicyDTO> getDcOtherSettingDTO(@RequestParam("dcId") Long dcId);


    /**
     * 获取运营商 门店生命周期配置信息
     * @param dcId
     * @return
     */
    @GetMapping(PREFIX + "/getBranchLifecycleSettingPolicy")
    CommonResult<BranchLifecycleSettingPolicyDTO> getBranchLifecycleSettingPolicy(@RequestParam("dcId") Long dcId);
}
