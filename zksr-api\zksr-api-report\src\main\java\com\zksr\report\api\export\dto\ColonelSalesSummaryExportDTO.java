package com.zksr.report.api.export.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 业务员月销售汇总
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("业务员月销售汇总导出 -  Response DTO")
public class ColonelSalesSummaryExportDTO extends BaseEntity {
    @ApiModelProperty(value = "业务员编号")
    @Excel(name = "业务员编号", cellType = Excel.ColumnType.STRING)
    private String colonelId; // 业务员编号

    @ApiModelProperty(value = "业务员名称")
    @Excel(name = "业务员名称", cellType = Excel.ColumnType.STRING)
    private String colonelName; // 业务员名称

    @ApiModelProperty(value = "管理门店")
    @Excel(name = "管理门店", cellType = Excel.ColumnType.NUMERIC)
    private Integer managedBranchCount; // 管理门店

    @ApiModelProperty(value = "动销门店数")
    @Excel(name = "动销门店数", cellType = Excel.ColumnType.NUMERIC)
    private Integer activeBranchCount; // 动销门店数

    @ApiModelProperty(value = "动销门店率%")
    @Excel(name = "动销门店率%", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal activeBranchRate; // 动销门店率%

    @ApiModelProperty(value = "动销SKU数")
    @Excel(name = "动销SKU数", cellType = Excel.ColumnType.NUMERIC)
    private Integer activeSkuCount; // 动销SKU数

    @ApiModelProperty(value = "代客下单金额")
    @Excel(name = "代客下单金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal agentOrderAmount; // 代客下单金额

    @ApiModelProperty(value = "代客下单SKU数量")
    @Excel(name = "代客下单SKU数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer agentOrderSkuCount; // 代客下单SKU数量

    @ApiModelProperty(value = "自主下单金额")
    @Excel(name = "自主下单金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal selfOrderAmount; // 自主下单金额

    @ApiModelProperty(value = "自主下单SKU数量")
    @Excel(name = "自主下单SKU数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer selfOrderSkuCount; // 自主下单SKU数量

    @ApiModelProperty(value = "销售金额")
    @Excel(name = "销售金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalSalesAmount; // 销售金额

    @ApiModelProperty(value = "销售数量")
    @Excel(name = "销售数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer totalSalesOrderCount; // 销售数量

    @ApiModelProperty(value = "退单金额")
    @Excel(name = "退单金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalReturnAmount; // 退单金额

    @ApiModelProperty(value = "退单数量")
    @Excel(name = "退单数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer totalReturnOrderCount; // 退单数量

    @ApiModelProperty(value = "总销售成本")
    @Excel(name = "总销售成本", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalSalesCost; // 总销售成本

    @ApiModelProperty(value = "总退货成本")
    @Excel(name = "总退货成本", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalReturnCost; // 总退货成本

    @ApiModelProperty(value = "毛利金额")
    @Excel(name = "毛利金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal grossProfitAmount; // 毛利金额

    @ApiModelProperty(value = "毛利率%")
    @Excel(name = "毛利率%", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal grossProfitRate; // 毛利率%

    @ApiModelProperty(value = "优惠金额")
    @Excel(name = "优惠金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalDiscountAmount; // 优惠金额

    @ApiModelProperty(value = "上月动销门店")
    @Excel(name = "上月动销门店", cellType = Excel.ColumnType.NUMERIC)
    private Integer lastMonthActiveBranchCount; // 上月动销门店

    @ApiModelProperty(value = "动销门店成长比")
    @Excel(name = "动销门店成长比", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal activeBranchGrowthRate; // 动销门店成长比

    @ApiModelProperty(value = "上月销售总金额")
    @Excel(name = "上月销售总金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal lastMonthSalesAmount; // 上月销售总金额

    @ApiModelProperty(value = "销售金额成长比")
    @Excel(name = "销售金额成长比", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal salesGrowthRate; // 销售金额成长比

    @ApiModelProperty(value = "上月动销SKU数")
    @Excel(name = "上月动销SKU数", cellType = Excel.ColumnType.NUMERIC)
    private Integer lastMonthActiveSkuCount; // 上月动销SKU数

    @ApiModelProperty(value = "动销SKU成长比")
    @Excel(name = "动销SKU成长比", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal activeSkuGrowthRate; // 动销SKU成长比

    @ApiModelProperty(value = "待处理退单金额")
    @Excel(name = "待处理退单金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal pendingRefundAmount = BigDecimal.ZERO; // 待处理退单金额
}