package com.zksr.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入记录
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysFileImport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 导入记录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long fileImportId;

    /**
     * 平台编号
     */
    private Long sysCode;

    /**
     * 导入文件名
     */
    private String fileName;

    /**
     * 导入文件下载地址
     */
    private String fileUrl;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 导入总数
     */
    private Integer totalNum;

    /**
     * mq发送数量
     */
    private Integer mqSendNum;

    /**
     * mq接收数量
     */
    private Integer mqReceiveNum;

    /**
     * 成功条数
     */
    private Integer successNum;

    /**
     * 失败条数
     */
    private Integer failNum;

    /**
     * 是否更新已存在的数据;是否更新已存在的数据
     */
    private Integer updateSupport;

    /**
     * 导入类型
     */
    private String importType;

    /**
     * 导入状态 0成功 1失败 2进行中 3等待中
     */
    private Integer importStatus;

    private String remark;
}
