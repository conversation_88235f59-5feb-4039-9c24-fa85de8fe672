package com.zksr.product.api.brand.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 平台品牌对象 prdt_brand
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BrandDTO {
    private static final long serialVersionUID = 1L;


    /**
     * 品牌编号
     */
    @ApiModelProperty(value = "品牌编号",required = true)
    private String brandNo;

    /**
     * 商品品牌名称
     */
    @ApiModelProperty(value = "商品品牌名称",required = true)
    private String brandName;

    /**
     * 平台商品牌id
     */
    @ApiModelProperty(value = "平台商品牌id")
    private Long brandId;

    /**
     * 平台商id
     */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private Long dcId;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 0 正常, 1 停用
     */
    @ApiModelProperty(value = "0 正常, 1 停用")
    private String status;



}
