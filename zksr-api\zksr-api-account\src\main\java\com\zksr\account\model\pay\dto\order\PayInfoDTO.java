package com.zksr.account.model.pay.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/5 14:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayInfoDTO {

    @ApiModelProperty("发起支付商户号")
    private String merchantNo;

    @ApiModelProperty("发起商户ID")
    private Long merchantId;

    @ApiModelProperty("发起支付openid")
    private String openid;
}
