---
description: 
globs: 
alwaysApply: false
---
SaasBizB2B
├── zksr-auth                # 统一认证与授权中心，负责OAuth2、单点登录、Token管理等
├── zksr-gateway             # API网关，统一入口，路由转发、权限校验、限流、熔断等
├── zksr-portal              # 门户服务，B2B门户前台、开放接口、用户自助等
├── zksr-visual
│   └── zksr-monitor         # 监控服务，服务健康监控、性能监控、告警等
├── zksr-api                 # API聚合模块，定义各业务领域的接口规范
│   ├── zksr-api-account     # 账户相关API，账户、支付、结算等接口
│   ├── zksr-api-demo        # 演示API，示例接口、测试用
│   ├── zksr-api-file        # 文件API，文件上传、下载、导入导出等接口
│   ├── zksr-api-member      # 会员API，会员、门店、业务员等接口
│   ├── zksr-api-portal      # 门户API，门户相关接口
│   ├── zksr-api-product     # 商品API，商品、SKU、分类、品牌等接口
│   ├── zksr-api-promotion   # 促销API，活动、优惠券、促销规则等接口
│   ├── zksr-api-report      # 报表API，数据报表、统计分析等接口
│   ├── zksr-api-system      # 系统API，组织、权限、配置等接口
│   ├── zksr-api-supplier    # 供应商API，供应商相关接口
│   └── zksr-api-trade       # 交易API，订单、支付、结算等接口
├── zksr-common              # 通用基础模块，工具、封装、通用中间件
│   ├── zksr-common-core     # 核心工具、基础类
│   ├── zksr-common-datasource # 数据源配置
│   ├── zksr-common-elasticsearch # ES相关
│   ├── zksr-common-log      # 日志封装
│   ├── zksr-common-mp       # MyBatis-Plus扩展
│   ├── zksr-common-redis    # Redis扩展
│   ├── zksr-common-rocketmq # MQ扩展
│   ├── zksr-common-seata    # 分布式事务
│   ├── zksr-common-security # 安全封装
│   ├── zksr-common-swagger  # Swagger文档
│   └── zksr-common-third    # 第三方集成
├── zksr-modules             # 业务主模块
│   ├── zksr-account         # 账户服务，账户、支付、结算、流水、账户日志等
│   │   ├── 主表：account、pay_order、settle_order、account_log
│   │   ├── 辅助表：account_type、pay_channel、settle_type
│   │   └── 日志表：account_log、pay_order_log
│   ├── zksr-demo            # 演示模块，示例业务
│   ├── zksr-file            # 文件服务，文件存储、导入导出、文件日志
│   │   ├── 主表：file_info
│   │   └── 日志表：file_log
│   ├── zksr-gen             # 代码生成服务，表结构元数据
│   │   └── 辅助表：gen_table、gen_table_column
│   ├── zksr-job-executor    # 定时任务调度，任务、任务日志
│   │   ├── 主表：job_info
│   │   └── 日志表：job_log
│   ├── zksr-member          # 会员服务，会员、门店、业务员、会员扩展、会员日志
│   │   ├── 主表：member、store、salesman
│   │   ├── 扩展表：member_ext、store_ext
│   │   └── 日志表：member_log
│   ├── zksr-product         # 商品服务，商品、SKU、SPU、品牌、分类、商品扩展、商品日志
│   │   ├── 主表：product、sku、spu、brand、category
│   │   ├── 扩展表：product_ext、sku_ext
│   │   └── 日志表：product_log
│   ├── zksr-promotion       # 促销服务，活动、优惠券、促销规则、促销日志
│   │   ├── 主表：promotion、coupon、promotion_rule
│   │   └── 日志表：promotion_log
│   ├── zksr-report          # 报表服务，统计分析、报表日志
│   │   ├── 主表：report、report_task
│   │   └── 日志表：report_log
│   ├── zksr-system          # 系统服务，组织、用户、角色、菜单、岗位、字典、参数、日志、扩展
│   │   ├── 主表：sys_user、sys_role、sys_dept、sys_menu、sys_post、sys_dict_type、sys_dict_data、sys_config、sys_partner
│   │   ├── 关联表：sys_user_role、sys_user_post、sys_role_dept、sys_role_menu
│   │   ├── 日志表：sys_logininfor（登录日志）、sys_oper_log（操作日志）、sys_job_log（定时任务日志）、sys_export_job（导出任务日志）
│   │   ├── 扩展表：worker_node（分布式ID）、sys_notice（通知公告）
│   │   └── 其他辅助表：sys_job（定时任务）、sys_dict_type、sys_dict_data
│   └── zksr-trade           # 交易服务，订单、支付、结算、交易日志
│       ├── 主表：trade_order、trade_pay、trade_settle
│       └── 日志表：trade_log

# 主要表关系举例
# - sys_user 与 sys_role 通过 sys_user_role 关联
# - sys_role 与 sys_menu 通过 sys_role_menu 关联
# - sys_user 与 sys_post 通过 sys_user_post 关联
# - 商品、SKU、SPU、品牌、分类等通过外键或ID字段关联
# - 账户、支付、结算等通过订单号、账户ID等关联
# - 促销、优惠券、活动等通过促销规则ID、用户ID等关联

# 详细表结构、实体、初始化SQL见 sql/init/ 各模块 .init.sql 文件

名词解析：
​​平台名称​​	平台叫“美团”或“大众点评”
​​联系人/电话​​	美团的城市经理或商务合作负责人
​​城市数量​​	美团覆盖了北京、上海、广州等100个城市
​​运营商数量​​	每个城市有本地运营团队（如“北京运营部”“上海运营部”）物流、物业、基础服务、服务器等
​​入驻商数量​​	平台上入驻了100万家餐厅、酒店、影院等商家
​​租户编码​​	每个商家有自己的独立后台（如“海底捞-北京店”有一个独立账号）
​​状态（启用）​​	商家可以随时被激活或下线（比如某家店违规被美团关停）



二、数据库表、实体及关系
1. 系统/组织/权限（zksr-cloud/zksr-system）
gen_table（GenTable）：代码生成表（代码生成器主表，记录表结构元数据）
gen_table_column（GenTableColumn）：代码生成字段表（代码生成器字段元数据）
sys_area（SysArea）：区域表（记录行政区域信息）
sys_area_city（SysAreaCity）：城市区域表（城市与区域的对应关系）
sys_area_supplier_zip（SysAreaSupplierZip）：区域供应商分组表（区域与供应商分组关系）
sys_bank_channel（SysBankChannel）：联行号表（银行渠道信息）
sys_brand_member（SysBrandMember）：品牌商子账户表（品牌商员工账号）
sys_brand_merchant（SysBrandMerchant）：品牌商表（品牌公司信息）
sys_channel（SysChannel）：渠道表（业务渠道信息）
sys_config（SysConfig）：参数配置表（系统参数配置）
sys_dc（SysDc）：配送中心表（配送中心基础信息）
sys_dc_area（SysDcArea）：配送中心区域表（配送中心与区域的对应关系）
sys_dc_area_zip（SysDcAreaZip）：配送中心区域分组表（配送中心与区域分组关系）
sys_dept（SysDept）：部门表（组织结构、部门信息）
sys_dict_data（SysDictData）：数据字典表（字典项数据）
sys_dict_type（SysDictType）：字典类型表（字典项类型）
sys_dlq（SysDlq）：死信队列表（消息队列死信管理）
sys_export_job（SysExportJob）：导出任务表（数据导出任务记录）
sys_file_import（SysFileImport）：文件导入表（文件导入任务主表）
sys_file_import_dtl（SysFileImportDtl）：文件导入明细表（文件导入的明细记录）
sys_group（SysGroup）：分组表（系统分组、组织分组等）
sys_interface_log（SysInterfaceLog）：接口日志表（外部接口调用日志）
sys_job（SysJob）：定时任务表（定时任务主表）
sys_job_log（SysJobLog）：定时任务日志表（定时任务执行日志）
sys_logininfor（SysLogininfor）：登录日志表（用户登录日志）
sys_menu（SysMenu）：菜单权限表（系统菜单、权限点）
sys_message_log（SysMessageLog）：消息日志表（系统消息发送日志）
sys_message_template（SysMessageTemplate）：消息模板表（系统消息模板）
sys_notice（SysNotice）：通知公告表（系统公告、通知）
sys_openability（SysOpenability）：开放能力表（系统开放能力、API能力）
sys_opensource（SysOpensource）：开源组件表（系统集成的开源组件信息）
sys_oper_log（SysOperLog）：操作日志表（系统操作日志）
sys_pages_config（SysPagesConfig）：页面配置表（系统页面配置）
sys_pages_config_template（SysPagesConfigTemplate）：页面配置模板表（页面配置模板信息）
sys_partner（SysPartner）：平台商表（平台商户、合作方信息）
sys_partner_config（SysPartnerConfig）：平台商配置表（平台商户参数配置）
sys_partner_dict_data（SysPartnerDictData）：平台商字典数据表（平台商自定义字典项）
sys_partner_dict_type（SysPartnerDictType）：平台商字典类型表（平台商自定义字典类型）
sys_partner_policy（SysPartnerPolicy）：平台商策略表（平台商户业务策略）
sys_post（SysPost）：岗位表（系统岗位信息）
sys_print_settings（SysPrintSettings）：打印设置表（系统打印参数配置）
sys_print_template（SysPrintTemplate）：打印模板表（系统打印模板）
sys_role（SysRole）：角色表（系统角色信息）
sys_role_dept（SysRoleDept）：角色-部门关联表（角色与部门多对多关系）
sys_role_menu（SysRoleMenu）：角色-菜单关联表（角色与菜单多对多关系）
sys_sms（SysSms）：短信发送表（短信发送记录）
sys_sms_template（SysSmsTemplate）：短信模板表（短信模板信息）
sys_software（SysSoftware）：软件商信息表（收银机等软件商信息）
sys_supplier（SysSupplier）：供应商表（供应商基础信息）
sys_supplier_area（SysSupplierArea）：供应商区域表（供应商与区域的对应关系）
sys_user（SysUser）：用户表（系统用户信息）
sys_user_column（SysUserColumn）：用户自定义列表（用户自定义页面列配置）
sys_user_post（SysUserPost）：用户-岗位关联表（用户与岗位多对多关系）
sys_user_role（SysUserRole）：用户-角色关联表（用户与角色多对多关系）
visual_setting_detail（VisualSettingDetail）：可视化配置明细表（可视化配置的明细项）
visual_setting_master（VisualSettingMaster）：可视化配置主表（可视化配置主档）
visual_setting_template（VisualSettingTemplate）：可视化配置模板表（可视化配置模板信息）
worker_node（WorkerNode）：分布式ID节点表（分布式ID生成节点信息）
wx_qr_data（WxQrData）：微信二维码数据表（微信二维码相关数据）

主要关系：
- sys_user 1---n sys_user_post（一个用户可有多个岗位）
- sys_user 1---n sys_user_role（一个用户可有多个角色）
- sys_user 1---n sys_logininfor（一个用户有多条登录日志）
- sys_user 1---n sys_oper_log（一个用户有多条操作日志）
- sys_user 1---n sys_user_column（一个用户有多条自定义列配置）
- sys_role 1---n sys_role_menu（一个角色可有多个菜单权限）
- sys_role 1---n sys_role_dept（一个角色可有多个部门权限）
- sys_dept 1---n sys_user（一个部门可有多个用户）
- sys_dept 1---n sys_role_dept（一个部门可有多个角色）
- sys_menu 1---n sys_role_menu（一个菜单可被多个角色关联）
- sys_post 1---n sys_user_post（一个岗位可有多个用户）
- sys_dict_type 1---n sys_dict_data（一个字典类型有多条字典数据）
- sys_partner 1---n sys_partner_config（一个平台商有多条配置）
- sys_partner 1---n sys_partner_policy（一个平台商有多条策略）
- sys_partner 1---n sys_partner_dict_type（一个平台商有多条自定义字典类型）
- sys_partner_dict_type 1---n sys_partner_dict_data（一个自定义字典类型有多条字典数据）
- sys_brand_merchant 1---n sys_brand_member（一个品牌商有多个子账户）
- sys_supplier 1---n sys_supplier_area（一个供应商有多个区域）
- sys_dc 1---n sys_dc_area（一个配送中心有多个区域）
- sys_dc 1---n sys_dc_area_zip（一个配送中心有多个区域分组）
- sys_area 1---n sys_area_city（一个区域有多个城市）
- sys_area 1---n sys_area_supplier_zip（一个区域有多个供应商分组）
- sys_job 1---n sys_job_log（一个定时任务有多条执行日志）
- sys_file_import 1---n sys_file_import_dtl（一个文件导入有多条明细）
- visual_setting_master 1---n visual_setting_detail（一个可视化配置主表有多条明细）
- visual_setting_template 1---n visual_setting_master（一个模板可有多个主表配置）

表-实体-关系举例：
数据库表	实体类	主要外键/关系说明
sys_user	SysUser	dept_id, 关联 sys_dept, 1对多 sys_user_post, 1对多 sys_user_role, 1对多 sys_logininfor, 1对多 sys_oper_log, 1对多 sys_user_column
sys_user_role	SysUserRole	user_id, role_id, 多对多
sys_role_menu	SysRoleMenu	role_id, menu_id, 多对多
sys_dept	SysDept	dept_id, 1对多 sys_user, 1对多 sys_role_dept
sys_role_dept	SysRoleDept	role_id, dept_id, 多对多
sys_menu	SysMenu	menu_id, 1对多 sys_role_menu
sys_post	SysPost	post_id, 1对多 sys_user_post
sys_user_post	SysUserPost	user_id, post_id, 多对多
sys_dict_type	SysDictType	type_id, 1对多 sys_dict_data
sys_dict_data	SysDictData	type_id, 关联 sys_dict_type
sys_partner	SysPartner	partner_id, 1对多 sys_partner_config, 1对多 sys_partner_policy, 1对多 sys_partner_dict_type
sys_partner_config	SysPartnerConfig	partner_id, 关联 sys_partner
sys_partner_policy	SysPartnerPolicy	partner_id, 关联 sys_partner
sys_partner_dict_type	SysPartnerDictType	partner_id, 关联 sys_partner, 1对多 sys_partner_dict_data
sys_partner_dict_data	SysPartnerDictData	dict_type_id, 关联 sys_partner_dict_type
sys_brand_merchant	SysBrandMerchant	brand_id, 1对多 sys_brand_member
sys_brand_member	SysBrandMember	brand_id, 关联 sys_brand_merchant
sys_supplier	SysSupplier	supplier_id, 1对多 sys_supplier_area
sys_supplier_area	SysSupplierArea	supplier_id, 关联 sys_supplier
sys_dc	SysDc	dc_id, 1对多 sys_dc_area, 1对多 sys_dc_area_zip
sys_dc_area	SysDcArea	dc_id, 关联 sys_dc
sys_dc_area_zip	SysDcAreaZip	dc_id, 关联 sys_dc
sys_area	SysArea	area_id, 1对多 sys_area_city, 1对多 sys_area_supplier_zip
sys_area_city	SysAreaCity	area_id, 关联 sys_area
sys_area_supplier_zip	SysAreaSupplierZip	area_id, 关联 sys_area
sys_job	SysJob	job_id, 1对多 sys_job_log
sys_job_log	SysJobLog	job_id, 关联 sys_job
sys_file_import	SysFileImport	import_id, 1对多 sys_file_import_dtl
sys_file_import_dtl	SysFileImportDtl	import_id, 关联 sys_file_import
visual_setting_master	VisualSettingMaster	master_id, 1对多 visual_setting_detail
visual_setting_detail	VisualSettingDetail	master_id, 关联 visual_setting_master
visual_setting_template	VisualSettingTemplate	template_id, 1对多 visual_setting_master
worker_node	WorkerNode	节点信息
wx_qr_data	WxQrData	二维码数据


3. 会员/门店/业务员（zksr-member）
mem_member（MemMember）：会员表（存储会员的基础信息，如手机号、姓名、等级、注册时间等）
mem_member_open_auth（MemMemberOpenAuth）：会员第三方授权表（记录会员与第三方平台的授权信息）
mem_member_register（MemMemberRegister）：会员注册表（会员注册申请、审核等流程记录）
mem_branch（MemBranch）：门店表（记录会员下属门店的信息，支持多门店管理）
mem_branch_lifecycle_zip（MemBranchLifecycleZip）：门店生命周期分组表（门店生命周期分组与归类）
mem_branch_register（MemBranchRegister）：门店注册表（门店注册申请、审核等流程记录）
mem_branch_supplier（MemBranchSupplier）：门店供应商表（门店与供应商的关联信息）
mem_branch_user（MemBranchUser）：门店用户表（门店与用户的关联信息）
mem_colonel（MemColonel）：业务员表（记录业务员信息，支持门店与业务员的关联）
mem_colonel_branch_target（MemColonelBranchTarget）：业务员门店目标表（业务员在门店的业绩目标）
mem_colonel_branch_zip（MemColonelBranchZip）：业务员门店分组表（业务员负责门店的分组管理）
mem_colonel_day_settle（MemColonelDaySettle）：业务员日结算表（记录业务员每日的业绩结算）
mem_colonel_hierarchy_zip（MemColonelHierarchyZip）：业务员层级分组表（业务员的层级、上下级关系管理）
mem_colonel_month_settle（MemColonelMonthSettle）：业务员月结算表（记录业务员每月的业绩结算）
mem_colonel_monty_settle（MemColonelMontySettle）：业务员月结算表（如为拼写错误可合并到mem_colonel_month_settle）
mem_colonel_relation（MemColonelRelation）：业务员关系表（记录业务员与门店、会员的关系）
mem_colonel_target（MemColonelTarget）：业务员目标表（设定业务员的业绩目标）
mem_colonel_tidy（MemColonelTidy）：业务员整理表（业务员数据整理、归档等）
mem_colonel_visit_log（MemColonelVisitLog）：业务员拜访日志表（记录业务员拜访门店、客户的情况）
mem_command（MemCommand）：指令表（系统下发给会员或门店的指令记录）
mem_complain（MemComplain）：投诉表（会员投诉建议的记录与处理）
mem_login_his（MemLoginHis）：登录历史表（记录会员的登录历史、设备、IP等信息）
mem_search_his（MemSearchHis）：会员搜索历史表（记录会员的搜索行为）

主要关系：
- mem_member 1---n mem_branch（一个会员可有多个门店）
- mem_member 1---n mem_member_register（一个会员可有多条注册记录）
- mem_member 1---n mem_login_his（一个会员可有多条登录历史）
- mem_member 1---n mem_member_open_auth（一个会员可有多条第三方授权）
- mem_branch 1---n mem_branch_register（一个门店可有多条注册记录）
- mem_branch 1---n mem_branch_supplier（一个门店可有多个供应商）
- mem_branch 1---n mem_branch_user（一个门店可有多个用户）
- mem_branch 1---n mem_colonel（一个门店可有多个业务员）
- mem_branch 1---n mem_branch_lifecycle_zip（一个门店可有多个生命周期分组）
- mem_colonel 1---n mem_colonel_day_settle（一个业务员有多条日结算）
- mem_colonel 1---n mem_colonel_month_settle（一个业务员有多条月结算）
- mem_colonel 1---n mem_colonel_target（一个业务员有多个目标）
- mem_colonel 1---n mem_colonel_visit_log（一个业务员有多条拜访日志）
- mem_colonel 1---n mem_colonel_relation（一个业务员有多条关系）
- mem_colonel 1---n mem_colonel_branch_target（一个业务员有多个门店目标）
- mem_colonel 1---n mem_colonel_branch_zip（一个业务员有多个门店分组）
- mem_colonel 1---n mem_colonel_hierarchy_zip（一个业务员有多个层级分组）
- mem_colonel 1---n mem_colonel_tidy（一个业务员有多条整理记录）
- mem_command 1---n mem_member（一个指令可下发给多个会员）
- mem_command 1---n mem_branch（一个指令可下发给多个门店）

表-实体-关系举例：
数据库表	实体类	主要外键/关系说明
mem_member	MemMember	member_id, 1对多 mem_branch, 1对多 mem_member_register, 1对多 mem_login_his, 1对多 mem_member_open_auth
mem_member_open_auth	MemMemberOpenAuth	member_id, 关联 mem_member
mem_member_register	MemMemberRegister	member_id, 关联 mem_member
mem_branch	MemBranch	member_id, 关联 mem_member, 1对多 mem_branch_register, 1对多 mem_branch_supplier, 1对多 mem_branch_user, 1对多 mem_colonel, 1对多 mem_branch_lifecycle_zip
mem_branch_lifecycle_zip	MemBranchLifecycleZip	branch_id, 关联 mem_branch
mem_branch_register	MemBranchRegister	branch_id, 关联 mem_branch
mem_branch_supplier	MemBranchSupplier	branch_id, 关联 mem_branch
mem_branch_user	MemBranchUser	branch_id, 关联 mem_branch
mem_colonel	MemColonel	branch_id, 关联 mem_branch, 1对多 mem_colonel_day_settle, 1对多 mem_colonel_month_settle, 1对多 mem_colonel_target, 1对多 mem_colonel_visit_log, 1对多 mem_colonel_relation, 1对多 mem_colonel_branch_target, 1对多 mem_colonel_branch_zip, 1对多 mem_colonel_hierarchy_zip, 1对多 mem_colonel_tidy
mem_colonel_branch_target	MemColonelBranchTarget	colonel_id, 关联 mem_colonel
mem_colonel_branch_zip	MemColonelBranchZip	colonel_id, 关联 mem_colonel
mem_colonel_day_settle	MemColonelDaySettle	colonel_id, 关联 mem_colonel
mem_colonel_hierarchy_zip	MemColonelHierarchyZip	colonel_id, 关联 mem_colonel
mem_colonel_month_settle	MemColonelMonthSettle	colonel_id, 关联 mem_colonel
mem_colonel_monty_settle	MemColonelMontySettle	colonel_id, 关联 mem_colonel
mem_colonel_relation	MemColonelRelation	colonel_id, 关联 mem_colonel
mem_colonel_target	MemColonelTarget	colonel_id, 关联 mem_colonel
mem_colonel_tidy	MemColonelTidy	colonel_id, 关联 mem_colonel
mem_colonel_visit_log	MemColonelVisitLog	colonel_id, 关联 mem_colonel
mem_command	MemCommand	command_id, 1对多 mem_member, 1对多 mem_branch
mem_complain	MemComplain	member_id, 关联 mem_member
mem_login_his	MemLoginHis	member_id, 关联 mem_member
mem_search_his	MemSearchHis	member_id, 关联 mem_member


4. 交易/订单/结算/售后（zksr-trade）
order_tbl（OrderTbl）：订单主表（平台所有订单的基础信息，包括买家、卖家、金额、状态等）
trd_order（TrdOrder）：订单表（订单主档，记录订单基础信息）
trd_order_express（TrdOrderExpress）：订单物流表（订单物流信息、快递单号等）
trd_order_share（TrdOrderShare）：订单分摊表（订单分摊、分账等信息）
trd_order_log（TrdOrderLog）：订单日志表（记录订单的操作、状态变更等日志信息）
trd_order_discount_dtl（TrdOrderDiscountDtl）：订单优惠明细表（订单享受的优惠明细）
trd_after（TrdAfter）：售后主表（售后单据主档，记录售后类型、状态等）
trd_after_log（TrdAfterLog）：售后日志表（记录售后单的操作、状态变更等日志信息）
trd_after_discount_dtl（TrdAfterDiscountDtl）：售后优惠明细表（售后订单的优惠明细）
trd_car（TrdCar）：购物车（小程序购物车）
trd_driver（TrdDriver）：司机表（配送司机信息管理）
trd_driver_rating（TrdDriverRating）：司机评分表（司机服务评分记录）
trd_express_import（TrdExpressImport）：物流导入表（批量导入物流信息）
trd_express_import_dtl（TrdExpressImportDtl）：物流导入明细表（物流导入的明细记录）
trd_express_status（TrdExpressStatus）：物流状态表（订单物流状态跟踪）
trd_hdfk_pay（TrdHdfkPay）：货到付款支付表（货到付款的支付记录）
trd_hdfk_pay_dtl（TrdHdfkPayDtl）：货到付款支付明细表（货到付款的明细记录）
trd_hdfk_settle（TrdHdfkSettle）：货到付款结算表（货到付款相关的结算信息）
trd_settle（TrdSettle）：结算表（平台与商家、供应商的结算信息）
trd_supplier_order（TrdSupplierOrder）：供应商订单主表（供应商订单基础信息）
trd_supplier_order_settle（TrdSupplierOrderSettle）：供应商订单结算表（记录供应商订单的结算信息）
trd_supplier_order_dtl（TrdSupplierOrderDtl）：供应商订单明细表（供应商订单的商品明细）
trd_supplier_after（TrdSupplierAfter）：供应商售后主表（供应商售后单据主档）
trd_supplier_after_settle（TrdSupplierAfterSettle）：供应商售后结算表（供应商售后订单的结算信息）
trd_supplier_after_dtl（TrdSupplierAfterDtl）：供应商售后明细表（供应商售后订单的商品明细）

主要关系：
- order_tbl 1---n trd_order（一个平台订单可拆分为多个业务订单）
- trd_order 1---n trd_order_express（一个订单可有多条物流信息）
- trd_order 1---n trd_order_share（一个订单可有多条分摊信息）
- trd_order 1---n trd_order_log（一个订单有多条操作日志）
- trd_order 1---n trd_order_discount_dtl（一个订单有多条优惠明细）
- trd_order 1---n trd_supplier_order（一个订单可有多个供应商订单）
- trd_supplier_order 1---n trd_supplier_order_settle（一个供应商订单有多条结算信息）
- trd_supplier_order_settle 1---n trd_supplier_order_dtl（一个结算单有多条明细）
- trd_supplier_order_dtl 1---n trd_supplier_after（一个供应商订单明细可有多个售后单）
- trd_supplier_after 1---n trd_supplier_after_settle（一个售后单有多条结算信息）
- trd_supplier_after_settle 1---n trd_supplier_after_dtl（一个售后结算单有多条明细）
- trd_order 1---n trd_after（一个订单可有多个售后单）
- trd_after 1---n trd_after_log（一个售后单有多条操作日志）
- trd_after 1---n trd_after_discount_dtl（一个售后单有多条优惠明细）
- trd_order 1---n trd_settle（一个订单可有多条结算信息）
- trd_hdfk_pay 1---n trd_hdfk_pay_dtl（一个货到付款支付有多条明细）
- trd_hdfk_settle 1---n trd_hdfk_pay（一个货到付款结算有多条支付记录）
- trd_express_import 1---n trd_express_import_dtl（一个物流导入有多条明细）
- trd_driver 1---n trd_driver_rating（一个司机有多条评分）

表-实体-关系举例：
数据库表	实体类	主要外键/关系说明
order_tbl	OrderTbl	order_id, 1对多 trd_order
trd_order	TrdOrder	order_id, 关联 order_tbl, 1对多 trd_order_express, 1对多 trd_order_share, 1对多 trd_order_log, 1对多 trd_order_discount_dtl, 1对多 trd_supplier_order, 1对多 trd_after, 1对多 trd_settle
trd_order_express	TrdOrderExpress	order_id, 关联 trd_order
trd_order_share	TrdOrderShare	order_id, 关联 trd_order
trd_order_log	TrdOrderLog	order_id, 关联 trd_order
trd_order_discount_dtl	TrdOrderDiscountDtl	order_id, 关联 trd_order
trd_supplier_order	TrdSupplierOrder	order_id, 关联 trd_order, 1对多 trd_supplier_order_settle
trd_supplier_order_settle	TrdSupplierOrderSettle	supplier_order_id, 关联 trd_supplier_order, 1对多 trd_supplier_order_dtl
trd_supplier_order_dtl	TrdSupplierOrderDtl	order_settle_id, 关联 trd_supplier_order_settle, 1对多 trd_supplier_after
trd_supplier_after	TrdSupplierAfter	order_dtl_id, 关联 trd_supplier_order_dtl, 1对多 trd_supplier_after_settle
trd_supplier_after_settle	TrdSupplierAfterSettle	after_id, 关联 trd_supplier_after, 1对多 trd_supplier_after_dtl
trd_supplier_after_dtl	TrdSupplierAfterDtl
after_settle_id, 关联 trd_supplier_after_settle
trd_after	TrdAfter	order_id, 关联 trd_order, 1对多 trd_after_log, 1对多 trd_after_discount_dtl
trd_after_log	TrdAfterLog
after_id, 关联 trd_after
trd_after_discount_dtl	TrdAfterDiscountDtl
after_id, 关联 trd_after
trd_settle	TrdSettle	order_id, 关联 trd_order
trd_hdfk_pay	TrdHdfkPay	hdfk_settle_id, 关联 trd_hdfk_settle, 1对多 trd_hdfk_pay_dtl
trd_hdfk_pay_dtl	TrdHdfkPayDtl	pay_id, 关联 trd_hdfk_pay
trd_hdfk_settle	TrdHdfkSettle	order_id, 关联 trd_order, 1对多 trd_hdfk_pay
trd_express_import	TrdExpressImport	import_id, 1对多 trd_express_import_dtl
trd_express_import_dtl	TrdExpressImportDtl	import_id, 关联 trd_express_import
trd_express_status	TrdExpressStatus	order_id, 关联 trd_order
trd_driver	TrdDriver	driver_id, 1对多 trd_driver_rating
trd_driver_rating	TrdDriverRating	driver_id, 关联 trd_driver
trd_car	TrdCar	car_id, 车辆信息


5. 账户/支付/结算（zksr-account）
acc_account（AccAccount）：账户表（记录平台、商家、会员等各类账户的基础信息）
acc_account_flow（AccAccountFlow）：账户流水表（账户的资金变动明细）
acc_bill_file（AccBillFile）：账单文件表（记录账单相关的文件信息，如对账单、结算单等）
acc_divide_dtl（AccDivideDtl）：分账明细表（分账业务的明细记录，记录每笔分账的对象、金额等）
acc_divide_flow（AccDivideFlow）：分账流水表（分账资金流转明细，记录分账资金的实际流转过程）
acc_offline_divide（AccOfflineDivide）：线下分账表（线下分账业务记录，支持线下结算场景）
acc_pay（AccPay）：支付表（支付订单的主表，记录支付请求、状态等信息）
acc_pay_flow（AccPayFlow）：支付流水表（支付订单的资金流转明细）
acc_platform_merchant（AccPlatformMerchant）：平台商户表（平台商户的基础信息）
acc_platform_merchant_wxb2b（AccPlatformMerchantWxb2b）：平台商户微信B2B表（微信B2B商户信息）
acc_recharge（AccRecharge）：充值表（账户充值的主表）
acc_recharge_import（AccRechargeImport）：充值导入表（批量导入充值数据）
acc_recharge_import_dtl（AccRechargeImportDtl）：充值导入明细表（充值导入的明细记录）
acc_recharge_scheme（AccRechargeScheme）：充值方案表（定义不同的充值优惠、规则等）
acc_recharge_scheme_area（AccRechargeSchemeArea）：充值方案区域表（定义充值方案适用的区域范围）
acc_transfer（AccTransfer）：转账表（账户间转账的主表）
acc_transfer_bill（AccTransferBill）：转账单表（转账的单据记录）
acc_transfer_bill_order（AccTransferBillOrder）：转账单订单表（转账单与订单的关联）
acc_transfer_flow（AccTransferFlow）：转账流水表（转账资金流转明细）
acc_withdraw（AccWithdraw）：提现表（账户提现的主表）
acc_withdraw_bill（AccWithdrawBill）：提现单表（提现的单据记录）
acc_withdraw_flow（AccWithdrawFlow）：提现流水表（提现的资金流转明细）

主要关系：
- acc_account 1---n acc_account_flow（一个账户有多条流水）
- acc_account 1---n acc_pay（一个账户可发起多笔支付）
- acc_account 1---n acc_recharge（一个账户可有多笔充值）
- acc_account 1---n acc_withdraw（一个账户可有多笔提现）
- acc_pay 1---n acc_pay_flow（一个支付订单有多条支付流水）
- acc_recharge 1---n acc_recharge_import（一个充值可有多条导入记录）
- acc_recharge_import 1---n acc_recharge_import_dtl（一个充值导入有多条明细）
- acc_withdraw 1---n acc_withdraw_bill（一个提现有多条提现单）
- acc_withdraw_bill 1---n acc_withdraw_flow（一个提现单有多条提现流水）
- acc_divide_dtl 1---n acc_divide_flow（一个分账明细有多条分账流水）
- acc_transfer 1---n acc_transfer_bill（一个转账有多条转账单）
- acc_transfer_bill 1---n acc_transfer_bill_order（一个转账单可关联多条订单）
- acc_transfer_bill 1---n acc_transfer_flow（一个转账单有多条转账流水）
- acc_platform_merchant 1---n acc_platform_merchant_wxb2b（一个平台商户可有多个微信B2B信息）
- acc_recharge_scheme 1---n acc_recharge_scheme_area（一个充值方案可适用于多个区域）

表-实体-关系举例：
数据库表	实体类	主要外键/关系说明
acc_account	AccAccount	account_id, 1对多 acc_account_flow, 1对多 acc_pay, 1对多 acc_recharge, 1对多 acc_withdraw
acc_account_flow	AccAccountFlow	account_id, 关联 acc_account
acc_pay	AccPay	account_id, 关联 acc_account, 1对多 acc_pay_flow
acc_pay_flow	AccPayFlow	pay_id, 关联 acc_pay
acc_recharge	AccRecharge	account_id, 关联 acc_account, 1对多 acc_recharge_import
acc_recharge_import	AccRechargeImport	recharge_id, 关联 acc_recharge, 1对多 acc_recharge_import_dtl
acc_recharge_import_dtl	AccRechargeImportDtl	import_id, 关联 acc_recharge_import
acc_withdraw	AccWithdraw	account_id, 关联 acc_account, 1对多 acc_withdraw_bill
acc_withdraw_bill	AccWithdrawBill	withdraw_id, 关联 acc_withdraw, 1对多 acc_withdraw_flow
acc_withdraw_flow	AccWithdrawFlow	withdraw_bill_id, 关联 acc_withdraw_bill
acc_divide_dtl	AccDivideDtl	divide_id, 1对多 acc_divide_flow
acc_divide_flow	AccDivideFlow	divide_dtl_id, 关联 acc_divide_dtl
acc_offline_divide	AccOfflineDivide	无直接外键
acc_transfer	AccTransfer	account_id, 关联 acc_account, 1对多 acc_transfer_bill
acc_transfer_bill	AccTransferBill	transfer_id, 关联 acc_transfer, 1对多 acc_transfer_bill_order, 1对多 acc_transfer_flow
acc_transfer_bill_order	AccTransferBillOrder	transfer_bill_id, 关联 acc_transfer_bill
acc_transfer_flow	AccTransferFlow	transfer_bill_id, 关联 acc_transfer_bill
acc_platform_merchant	AccPlatformMerchant	merchant_id, 1对多 acc_platform_merchant_wxb2b
acc_platform_merchant_wxb2b	AccPlatformMerchantWxb2b	merchant_id, 关联 acc_platform_merchant
acc_bill_file	AccBillFile	bill_id, 关联账单、结算等业务表
acc_recharge_scheme	AccRechargeScheme	scheme_id, 1对多 acc_recharge_scheme_area
acc_recharge_scheme_area	AccRechargeSchemeArea	scheme_id, 关联 acc_recharge_scheme


6. 促销/活动/优惠券（zksr-promotion）
prm_activity（PrmActivity）：活动表（促销活动的主表，记录活动信息）
prm_activity_branch_scope（PrmActivityBranchScope）：活动门店范围表（活动适用的门店范围）
prm_activity_channel_scope（PrmActivityChannelScope）：活动渠道范围表（活动适用的渠道范围）
prm_activity_city_scope（PrmActivityCityScope）：活动城市范围表（活动适用的城市范围）
prm_activity_spu_scope（PrmActivitySpuScope）：活动SPU范围表（活动适用的商品SPU范围）
prm_activity_supplier_scope（PrmActivitySupplierScope）：活动供应商范围表（活动适用的供应商范围）
prm_bg_rule（PrmBgRule）：买赠规则表（促销活动买赠规则）
prm_cb_rule（PrmCbRule）：组合购规则表（促销活动组合购规则）
prm_coupon（PrmCoupon）：优惠券表（记录平台发放的优惠券信息）
prm_coupon_batch（PrmCouponBatch）：优惠券批次表（优惠券的批量生成、发放管理）
prm_coupon_batch_dtl（PrmCouponBatchDtl）：优惠券批次明细表（批次下的具体优惠券明细）
prm_coupon_colonel_quota（PrmCouponColonelQuota）：业务员优惠券额度表（记录业务员可发放的优惠券额度）
prm_coupon_log（PrmCouponLog）：优惠券日志表（记录优惠券的发放、使用、作废等操作日志）
prm_coupon_scope_apply（PrmCouponScopeApply）：优惠券适用范围申请表（优惠券适用范围的申请、审批记录）
prm_coupon_template（PrmCouponTemplate）：优惠券模板表（优惠券的模板定义）
prm_coupon_template_extend（PrmCouponTemplateExtend）：优惠券模板扩展表（模板的扩展属性）
prm_coupon_template_repeat_rule（PrmCouponTemplateRepeatRule）：优惠券模板重复规则表（优惠券可重复领取、使用规则）
prm_fd_rule（PrmFdRule）：满减规则表（促销活动满减规则）
prm_fg_rule（PrmFgRule）：满赠规则表（促销活动满赠规则）
prm_live_order（PrmLiveOrder）：直播订单表（直播活动的订单信息）
prm_live_product（PrmLiveProduct）：直播商品表（直播活动的商品信息）
prm_live_room（PrmLiveRoom）：直播间表（直播活动的直播间信息）
prm_sk_rule（PrmSkRule）：秒杀规则表（促销活动秒杀规则）
prm_sp_rule（PrmSpRule）：特价规则表（促销活动特价规则）

主要关系：
- prm_activity 1---n prm_coupon（一个活动可发放多张优惠券）
- prm_activity 1---n prm_activity_branch_scope（一个活动可适用于多个门店）
- prm_activity 1---n prm_activity_channel_scope（一个活动可适用于多个渠道）
- prm_activity 1---n prm_activity_city_scope（一个活动可适用于多个城市）
- prm_activity 1---n prm_activity_spu_scope（一个活动可适用于多个商品SPU）
- prm_activity 1---n prm_activity_supplier_scope（一个活动可适用于多个供应商）
- prm_coupon 1---n prm_coupon_batch（一个优惠券可有多个批次）
- prm_coupon_batch 1---n prm_coupon_batch_dtl（一个批次下有多条明细）
- prm_coupon 1---n prm_coupon_log（一个优惠券有多条操作日志）
- prm_coupon 1---n prm_coupon_scope_apply（一个优惠券有多条适用范围申请）
- prm_coupon 1---n prm_coupon_colonel_quota（一个优惠券有多个业务员额度）
- prm_coupon 1---n prm_coupon_template（一个优惠券有一个模板）
- prm_coupon_template 1---n prm_coupon_template_extend（一个模板有多个扩展属性）
- prm_coupon_template 1---n prm_coupon_template_repeat_rule（一个模板有多条重复规则）
- prm_bg_rule、prm_cb_rule、prm_fd_rule、prm_fg_rule、prm_sp_rule、prm_sk_rule等规则表与prm_activity通过外键activity_id关联
- prm_live_room 1---n prm_live_product（一个直播间有多个商品）
- prm_live_room 1---n prm_live_order（一个直播间有多个订单）

表-实体-关系举例：
数据库表	实体类	主要外键/关系说明
prm_activity	PrmActivity	activity_id, 1对多 prm_coupon, 1对多 prm_activity_branch_scope, 1对多 prm_activity_channel_scope, 1对多 prm_activity_city_scope, 1对多 prm_activity_spu_scope, 1对多 prm_activity_supplier_scope
prm_activity_branch_scope	PrmActivityBranchScope	activity_id, 关联 prm_activity
prm_activity_channel_scope	PrmActivityChannelScope	activity_id, 关联 prm_activity
prm_activity_city_scope	PrmActivityCityScope	activity_id, 关联 prm_activity
prm_activity_spu_scope	PrmActivitySpuScope	activity_id, 关联 prm_activity
prm_activity_supplier_scope	PrmActivitySupplierScope	activity_id, 关联 prm_activity
prm_coupon	PrmCoupon	activity_id, 关联 prm_activity, 1对多 prm_coupon_batch, 1对多 prm_coupon_log, 1对多 prm_coupon_scope_apply, 1对多 prm_coupon_colonel_quota, 1对1 prm_coupon_template
prm_coupon_batch	PrmCouponBatch	coupon_id, 关联 prm_coupon, 1对多 prm_coupon_batch_dtl
prm_coupon_batch_dtl	PrmCouponBatchDtl	batch_id, 关联 prm_coupon_batch
prm_coupon_colonel_quota	PrmCouponColonelQuota	coupon_id, 关联 prm_coupon
prm_coupon_log	PrmCouponLog	coupon_id, 关联 prm_coupon
prm_coupon_scope_apply	PrmCouponScopeApply	coupon_id, 关联 prm_coupon
prm_coupon_template	PrmCouponTemplate	coupon_id, 关联 prm_coupon, 1对多 prm_coupon_template_extend, 1对多 prm_coupon_template_repeat_rule
prm_coupon_template_extend	PrmCouponTemplateExtend	template_id, 关联 prm_coupon_template
prm_coupon_template_repeat_rule	PrmCouponTemplateRepeatRule	template_id, 关联 prm_coupon_template
prm_bg_rule	PrmBgRule	activity_id, 关联 prm_activity
prm_cb_rule	PrmCbRule	activity_id, 关联 prm_activity
prm_fd_rule	PrmFdRule	activity_id, 关联 prm_activity
prm_fg_rule	PrmFgRule	activity_id, 关联 prm_activity
prm_sp_rule	PrmSpRule	activity_id, 关联 prm_activity
prm_sk_rule	PrmSkRule	activity_id, 关联 prm_activity
prm_live_room	PrmLiveRoom	room_id, 1对多 prm_live_product, 1对多 prm_live_order
prm_live_product	PrmLiveProduct	room_id, 关联 prm_live_room
prm_live_order	PrmLiveOrder	room_id, 关联 prm_live_room


7. 报表（zksr-report）
ads_area_branch_stats_stats（AdsAreaBranchStatsStats）：区域门店统计表（按区域统计门店相关数据）
ads_area_cat1_stats_month（AdsAreaCat1StatsMonth）：区域一级品类月度统计表（区域一级品类销售等月度统计）
ads_area_trade_weekday_distribution（AdsAreaTradeWeekdayDistribution）：区域交易周分布表（区域订单在一周内的分布情况）
ads_branch_cat1_orderamt_stats_month（AdsBranchCat1OrderamtStatsMonth）：门店一级品类订单金额月报表（门店一级品类订单金额的月度统计）
ads_branch_stats_month（AdsBranchStatsMonth）：门店月度统计表（门店各项业务月度统计）
ads_branch_tag_month（AdsBranchTagMonth）：门店标签月报表（门店标签相关的月度统计）
ads_branch_trade_weekday_distribution（AdsBranchTradeWeekdayDistribution）：门店周分布表（统计门店订单在一周内的分布情况）
dim_area（DimArea）：区域维度表（区域基础信息）
dim_branch（DimBranch）：门店维度表（门店基础信息）
dim_catgory（DimCatgory）：品类维度表（商品品类基础信息）
dim_colonel（DimColonel）：业务员维度表（业务员基础信息）
dim_coupon_template_full（DimCouponTemplateFull）：优惠券模板全量维度表（优惠券模板基础信息）
dim_date（DimDate）：日期维度表（日期基础信息）
dim_dc（DimDc）：配送中心维度表（配送中心基础信息）
dim_dict_data（DimDictData）：字典数据维度表（字典项基础信息）
dim_member（DimMember）：会员维度表（会员基础信息）
dim_month（DimMonth）：月份维度表（月度基础信息）
dim_partner（DimPartner）：合作方维度表（合作方基础信息）
dim_sku（DimSku）：SKU维度表（商品SKU基础信息）
dim_supplier（DimSupplier）：供应商维度表（供应商基础信息）
dwd_colonel_visit_inc（DwdColonelVisitInc）：业务员拜访明细表（业务员拜访行为明细）
dwd_login_his_inc（DwdLoginHisInc）：会员登录明细表（会员登录行为明细）
dwd_trd_cancel_inc（DwdTrdCancelInc）：订单取消明细表（订单取消行为明细）
dwd_trd_order_dtl_inc（DwdTrdOrderDtlInc）：订单明细表（订单商品明细）
dwd_trd_return_inc（DwdTrdReturnInc）：订单退货明细表（订单退货行为明细）
dws_trd_area_category_sales_month（DwsTrdAreaCategorySalesMonth）：区域品类月销售表（区域品类销售月度统计）
dws_trd_area_hours_sales_month（DwsTrdAreaHoursSalesMonth）：区域小时销售月度表（区域小时维度销售统计）
dws_trd_area_sales_day（DwsTrdAreaSalesDay）：区域销售日报表（区域每日销售统计）
dws_trd_area_sales_month（DwsTrdAreaSalesMonth）：区域销售月报表（区域月度销售统计）
dws_trd_branch_category_sales_month（DwsTrdBranchCategorySalesMonth）：门店品类月销售表（门店品类销售月度统计）
dws_trd_branch_hours_sales_month（DwsTrdBranchHoursSalesMonth）：门店小时销售月度表（门店小时维度销售统计）
dws_trd_branch_sales_day（DwsTrdBranchSalesDay）：门店销售日报表（按天统计门店销售数据）
dws_trd_branch_sales_month（DwsTrdBranchSalesMonth）：门店销售月报表（按月统计门店销售数据）
dws_trd_colonel_sales_day（DwsTrdColonelSalesDay）：业务员销售日报表（按天统计业务员销售数据）
dws_trd_colonel_sales_month（DwsTrdColonelSalesMonth）：业务员销售月报表（按月统计业务员销售数据）
dws_trd_sku_sales_day（DwsTrdSkuSalesDay）：SKU销售日报表（按天统计SKU销售数据）
dws_trd_sku_sales_month（DwsTrdSkuSalesMonth）：SKU销售月报表（按月统计SKU销售数据）
dws_trd_supplier_sales_day（DwsTrdSupplierSalesDay）：供应商销售日报表（按天统计供应商销售数据）
dws_trd_supplier_sales_month（DwsTrdSupplierSalesMonth）：供应商销售月报表（按月统计供应商销售数据）
mem_colonel_branch_zip（MemColonelBranchZip）：业务员门店分组表（业务员负责门店的分组管理，辅助分析）
mem_colonel_hierarchy_zip（MemColonelHierarchyZip）：业务员层级分组表（业务员的层级、上下级关系管理，辅助分析）
prdt_area_item_zip（PrdtAreaItemZip）：城市上架商品分组表（商品上架分组，辅助分析）
prdt_supplier_item_zip（PrdtSupplierItemZip）：供应商商品分组表（供应商商品分组，辅助分析）
rpt_tag_def（RptTagDef）：标签定义表（报表标签的定义和管理）
sys_area_supplier_zip（SysAreaSupplierZip）：区域供应商分组表（区域与供应商分组关系，辅助分析）
sys_dc_area_zip（SysDcAreaZip）：配送中心区域分组表（配送中心与区域分组关系，辅助分析）

主要关系：
- 各dws/dwd/ads表与dim_维度表通过外键（如area_id、branch_id、sku_id等）关联，实现多维分析
- mem_colonel_branch_zip、mem_colonel_hierarchy_zip、prdt_area_item_zip、prdt_supplier_item_zip、sys_area_supplier_zip、sys_dc_area_zip等分组表为分析提供分组、标签等辅助信息
- rpt_tag_def为报表标签定义表，可与各分析表通过tag_id等字段关联

表-实体-关系举例：
数据库表	实体类	主要外键/关系说明
dws_trd_supplier_sales_day	DwsTrdSupplierSalesDay	supplier_id, 关联 dim_supplier, date_id 关联 dim_date
dws_trd_supplier_sales_month	DwsTrdSupplierSalesMonth	supplier_id, 关联 dim_supplier, month_id 关联 dim_month
dws_trd_branch_sales_day	DwsTrdBranchSalesDay	branch_id, 关联 dim_branch, date_id 关联 dim_date
dws_trd_branch_sales_month	DwsTrdBranchSalesMonth	branch_id, 关联 dim_branch, month_id 关联 dim_month
dws_trd_area_sales_month	DwsTrdAreaSalesMonth	area_id, 关联 dim_area, month_id 关联 dim_month
dws_trd_area_sales_day	DwsTrdAreaSalesDay	area_id, 关联 dim_area, date_id 关联 dim_date
dws_trd_colonel_sales_day	DwsTrdColonelSalesDay	colonel_id, 关联 dim_colonel, date_id 关联 dim_date
dws_trd_colonel_sales_month	DwsTrdColonelSalesMonth	colonel_id, 关联 dim_colonel, month_id 关联 dim_month
dws_trd_sku_sales_day	DwsTrdSkuSalesDay	sku_id, 关联 dim_sku, date_id 关联 dim_date
dws_trd_sku_sales_month	DwsTrdSkuSalesMonth	sku_id, 关联 dim_sku, month_id 关联 dim_month
dws_trd_area_category_sales_month	DwsTrdAreaCategorySalesMonth	area_id, catgory_id, 关联 dim_area, dim_catgory, month_id 关联 dim_month
dws_trd_branch_category_sales_month	DwsTrdBranchCategorySalesMonth	branch_id, catgory_id, 关联 dim_branch, dim_catgory, month_id 关联 dim_month
dws_trd_area_hours_sales_month	DwsTrdAreaHoursSalesMonth	area_id, hour, 关联 dim_area, month_id 关联 dim_month
dws_trd_branch_hours_sales_month	DwsTrdBranchHoursSalesMonth	branch_id, hour, 关联 dim_branch, month_id 关联 dim_month
ads_branch_trade_weekday_distribution	AdsBranchTradeWeekdayDistribution	branch_id, 关联 dim_branch
ads_branch_tag_month	AdsBranchTagMonth	branch_id, 关联 dim_branch, tag_id 关联 rpt_tag_def
ads_branch_cat1_orderamt_stats_month	AdsBranchCat1OrderamtStatsMonth	branch_id, catgory_id, 关联 dim_branch, dim_catgory, month_id 关联 dim_month
rpt_tag_def	RptTagDef	tag_id, 报表标签定义


四、说明
详细表结构请参考 sql/init/、sql/release/、sql/version/ 目录下 SQL 文件。
实体类位于各模块 domain 目录下，命名与表基本一一对应。
复杂业务表建议结合实体类和 Mapper 进一步梳理字段和外键关系。
以上为主干表和关系，部分辅助表、日志表、扩展表未全部列出。








