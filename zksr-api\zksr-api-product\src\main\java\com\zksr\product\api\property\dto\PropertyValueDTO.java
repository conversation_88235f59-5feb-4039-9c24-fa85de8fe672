package com.zksr.product.api.property.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 规格属性KV
 * @date 2024/5/30 19:27
 */
@Data
@ApiModel(description = "商品属性值")
@AllArgsConstructor
@NoArgsConstructor
public class PropertyValueDTO {

    @ApiModelProperty("属性值ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long propertyValId;

    @ApiModelProperty("属性名值")
    private String propertyValName;
}
