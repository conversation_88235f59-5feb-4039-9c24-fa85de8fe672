package com.zksr.report.api.branch.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 门店周下单分布月对象 ads_branch_trade_weekday_distribution
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@ApiModel("门店周下单分布月 - ads_branch_trade_weekday_distribution Response VO")
public class AdsBranchTradeWeekdayDistributionRespVO {
    private static final long serialVersionUID = 1L;

    /** 月份ID */
    @ApiModelProperty(value = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long monthId;

    /** 平台商id */
    @ApiModelProperty(value = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 区域城市id */
    @ApiModelProperty(value = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 门店id */
    @ApiModelProperty(value = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /** 周一动销门店数 */
    @Excel(name = "周一动销门店数")
    @ApiModelProperty(value = "周一动销门店数")
    private Integer mondayDxBranchQty;

    /** 周一订单数量 */
    @Excel(name = "周一订单数量")
    @ApiModelProperty(value = "周一订单数量")
    private Integer mondayOrderQty;

    /** 周一订单金额 */
    @Excel(name = "周一订单金额")
    @ApiModelProperty(value = "周一订单金额")
    private BigDecimal mondayOrderAmt;

    /** 周二动销门店数 */
    @Excel(name = "周二动销门店数")
    @ApiModelProperty(value = "周二动销门店数")
    private Integer tuesdayDxBranchQty;

    /** 周二订单数量 */
    @Excel(name = "周二订单数量")
    @ApiModelProperty(value = "周二订单数量")
    private Integer tuesdayOrderQty;

    /** 周二订单金额 */
    @Excel(name = "周二订单金额")
    @ApiModelProperty(value = "周二订单金额")
    private BigDecimal tuesdayOrderAmt;

    /** 周三动销门店数 */
    @Excel(name = "周三动销门店数")
    @ApiModelProperty(value = "周三动销门店数")
    private Integer wednesdayDxBranchQty;

    /** 周三订单数量 */
    @Excel(name = "周三订单数量")
    @ApiModelProperty(value = "周三订单数量")
    private Integer wednesdayOrderQty;

    /** 周三订单金额 */
    @Excel(name = "周三订单金额")
    @ApiModelProperty(value = "周三订单金额")
    private BigDecimal wednesdayOrderAmt;

    /** 周四动销门店数 */
    @Excel(name = "周四动销门店数")
    @ApiModelProperty(value = "周四动销门店数")
    private Integer thursdayDxBranchQty;

    /** 周四订单数量 */
    @Excel(name = "周四订单数量")
    @ApiModelProperty(value = "周四订单数量")
    private Integer thursdayOrderQty;

    /** 周四订单金额 */
    @Excel(name = "周四订单金额")
    @ApiModelProperty(value = "周四订单金额")
    private BigDecimal thursdayOrderAmt;

    /** 周五动销门店数 */
    @Excel(name = "周五动销门店数")
    @ApiModelProperty(value = "周五动销门店数")
    private Integer fridayDxBranchQty;

    /** 周五订单数量 */
    @Excel(name = "周五订单数量")
    @ApiModelProperty(value = "周五订单数量")
    private Integer fridayOrderQty;

    /** 周五订单金额 */
    @Excel(name = "周五订单金额")
    @ApiModelProperty(value = "周五订单金额")
    private BigDecimal fridayOrderAmt;

    /** 周六动销门店数 */
    @Excel(name = "周六动销门店数")
    @ApiModelProperty(value = "周六动销门店数")
    private Integer saturdayDxBranchQty;

    /** 周六订单数量 */
    @Excel(name = "周六订单数量")
    @ApiModelProperty(value = "周六订单数量")
    private Integer saturdayOrderQty;

    /** 周六订单金额 */
    @Excel(name = "周六订单金额")
    @ApiModelProperty(value = "周六订单金额")
    private BigDecimal saturdayOrderAmt;

    /** 周日动销门店数 */
    @Excel(name = "周日动销门店数")
    @ApiModelProperty(value = "周日动销门店数")
    private Integer sundayDxBranchQty;

    /** 周日订单数量 */
    @Excel(name = "周日订单数量")
    @ApiModelProperty(value = "周日订单数量")
    private Integer sundayOrderQty;

    /** 周日订单金额 */
    @Excel(name = "周日订单金额")
    @ApiModelProperty(value = "周日订单金额")
    private BigDecimal sundayOrderAmt;

    /** 插入时间id */
    @Excel(name = "插入时间id")
    @ApiModelProperty(value = "插入时间id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long insertDateId;

}
