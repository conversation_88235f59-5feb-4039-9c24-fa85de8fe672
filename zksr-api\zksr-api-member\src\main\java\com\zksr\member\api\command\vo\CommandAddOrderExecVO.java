package com.zksr.member.api.command.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("操作指令 - 业务员加单执行结果 CommandAddOrderExecVO")
public class CommandAddOrderExecVO {

    @ApiModelProperty(value = "推送加单金额")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long commandId;

    @ApiModelProperty(value = "推送加单金额")
    private BigDecimal pushAmt;

    @ApiModelProperty(value = "推送加单sku数量")
    private Long pushSkuQty;

    @ApiModelProperty(value = "订单下单金额")
    private BigDecimal orderAmt;

    @ApiModelProperty(value = "订单下单sku数量")
    private Long orderSkuQty;

    @ApiModelProperty(value = "达成率")
    private BigDecimal achievementRate;

    @ApiModelProperty(value = "完成时间")
    private Date completeTime;


}
