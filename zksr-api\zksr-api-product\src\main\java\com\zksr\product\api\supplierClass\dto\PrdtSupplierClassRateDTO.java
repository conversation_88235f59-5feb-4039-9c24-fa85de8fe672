package com.zksr.product.api.supplierClass.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 入驻商-平台商管理分类 关联关系对象 prdt_supplier_class_rate
 * 入驻商 与 平台管理一级分类 的销售分润占比
 * <AUTHOR>
 * @date 2024-03-04
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSupplierClassRateDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 平台商管理分类id;平台商管理分类id
     */
    @Excel(name = "平台商管理分类id;平台商管理分类id")
    @ApiModelProperty("平台商管理分类id;平台商管理分类id")
    private Long catgoryId;

    /**
     * 入驻商id;入驻商id
     */
    @Excel(name = "入驻商id;入驻商id")
    @ApiModelProperty("入驻商id;入驻商id")
    private Long supplierId;

    /**
     * 平台商id;平台商id
     */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty("平台商id;平台商id")
    private Long sysCode;

    /**
     * 销售分润占比, 最大0.29 = 29%
     */
    @Excel(name = "销售分润占比, 最大0.29 = 29%")
    @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
    private BigDecimal saleTotalRate;
}
