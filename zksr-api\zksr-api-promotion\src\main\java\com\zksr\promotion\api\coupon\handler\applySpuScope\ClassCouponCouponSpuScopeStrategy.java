package com.zksr.promotion.api.coupon.handler.applySpuScope;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.promotion.api.coupon.handler.CouponSpuScopeStrategy;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 类别券验证策略
 * @date 2024/4/3 16:03
 */
@SuppressWarnings("all")
public class ClassCouponCouponSpuScopeStrategy implements CouponSpuScopeStrategy {
    @Override
    public void productFilter(List<OrderValidItemDTO> items, Set<CouponDTO> coupons, Map<Long, CouponTemplateDTO> couponTemplateMap, Set<CouponDTO> availableSet) {
        // 优惠券按照入驻商分组
        Map<Long, List<OrderValidItemDTO>> supplierMap = items.stream().collect(Collectors.groupingBy(OrderValidItemDTO::getSupplierId));
        // 开始筛选符合要求的优惠券
        coupons.stream().filter(item -> CouponSpuScopeEnum.CLASS.getScope().equals(item.getSpuScope()) && Objects.nonNull(item.getSupplierId())).forEach(coupon -> {
            CouponTemplateDTO couponTemplate = couponTemplateMap.get(coupon.getCouponId());
            // 商品是否有包含当前优惠券入驻商
            if (!supplierMap.containsKey(couponTemplate.getSupplierId())) {
                return;
            }
            // 获取范围ID
            List<Long> applyIds = coupon.getSpuScopeAs().stream().map(CouponSpuScopeDTO::getApplyId).collect(Collectors.toList());
            // 具体规则
            Map<Long, List<OrderValidItemDTO>> scopeMap = supplierMap.get(couponTemplate.getSupplierId()).stream().collect(Collectors.groupingBy(OrderValidItemDTO::getCategoryId));
            BigDecimal scopeAmt = BigDecimal.ZERO;
            // 获取交集
            for (Long scopeId : CollUtil.intersection(applyIds, scopeMap.keySet()).stream().collect(Collectors.toList())) {
                List<OrderValidItemDTO> scopeItems = scopeMap.get(scopeId);
                scopeAmt = scopeItems.stream().map(OrderValidItemDTO::getAmt).reduce(scopeAmt, BigDecimal::add);
            }
            if (NumberUtil.isGreaterOrEqual(scopeAmt, couponTemplate.getTriggerAmt())) {
                // 达到启用金额
                availableSet.add(coupon);
            }
        });
        coupons.removeAll(availableSet);
    }
}
