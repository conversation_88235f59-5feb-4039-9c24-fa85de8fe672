package com.zksr.promotion.api.coupon.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券领取结果
 * @date 2024/4/2 9:19
 */
@Data
@ApiModel(description = "优惠券领取结果")
public class CouponReceiveResultDTO {

    @ApiModelProperty("优惠券模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty("优惠券ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponId;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("0-领取成功,other=失败")
    private Integer code = 0;

    @ApiModelProperty("失败原因, code!=0 时返回")
    private String msg;

    public CouponReceiveResultDTO() {
    }

    public CouponReceiveResultDTO(Long couponTemplateId) {
        this.couponTemplateId = couponTemplateId;
    }

    public void setErr(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.msg = errorCode.getMsg();
    }
}
