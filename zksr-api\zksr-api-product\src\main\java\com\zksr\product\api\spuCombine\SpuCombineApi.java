package com.zksr.product.api.spuCombine;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @time 2024/12/31
 * @desc
 */

@FeignClient( contextId = "spuCombineApi" , value = ApiConstants.NAME)
public interface SpuCombineApi {

    String PREFIX = ApiConstants.PREFIX + "/spuCombineApi";

    /**
     * 获取组合商品
     * @param spuCombineId  组合商品ID
     * @return  组合商品
     */
    @GetMapping(PREFIX + "/getSpuCombine")
    CommonResult<SpuCombineDTO> getSpuCombine(@RequestParam("spuCombineId") Long spuCombineId);
}
