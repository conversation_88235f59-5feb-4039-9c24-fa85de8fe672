package com.zksr.report.api.branch.vo;

import com.zksr.common.core.enums.branch.BranchTagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员首页决策数据统计请求
 * @date 2024/11/23 9:07
 */
@Data
@ApiModel(description = "业务员首页决策数据标签统计请求")
public class ColonelBranchTagPageReqVO extends ColonelDecisionTotalReqVO {

    private static final Integer PAGE_NO = 1;
    private static final Integer PAGE_SIZE = 10;

    @ApiModelProperty(value = "业务员ID集合", hidden = true)
    private List<Long> colonelIdList;

    @ApiModelProperty(value = "标签类型")
    private BranchTagEnum branchTag;

    /**
     * 每页条数 - 不分页
     *
     * 例如说，导出接口，可以设置 {@link #pageSize} 为 -1 不分页，查询所有数据。
     */
    public static final Integer PAGE_SIZE_NONE = -1;

    @ApiModelProperty(value = "页码，从 1 开始", required = true, example = "1")
    private Integer pageNo = PAGE_NO;

    @ApiModelProperty(value = "每页条数，最大值为 100, -1 为查询全部数据", required = true, example = "10")
    private Integer pageSize = PAGE_SIZE;

}
