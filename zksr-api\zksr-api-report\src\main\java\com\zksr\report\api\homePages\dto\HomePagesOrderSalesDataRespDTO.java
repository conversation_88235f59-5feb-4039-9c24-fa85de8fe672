package com.zksr.report.api.homePages.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("PC首页获取订单销售数据返回 - HomePagesOrderSalesDataRespDTO Response VO")
public class HomePagesOrderSalesDataRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID */
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 入驻商ID */
    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 订单销售总金额 */
    @ApiModelProperty(value = "销售订单金额")
    private BigDecimal orderAmt;

    /** 销售订单数量 */
    @ApiModelProperty(value = "销售订单数量")
    private Long orderQty;

    /** 销售订单门店数量 */
    @ApiModelProperty(value = "销售订单门店数量")
    private Long orderBranchQty;

    /** 销售订单门店平均金额 =（销售订单总金额 / 销售订单门店数量） */
    @ApiModelProperty(value = "销售订单门店平均金额")
    private BigDecimal orderBranchAvgAmt;

    /** 欠款总金额（待回款） */
    @ApiModelProperty(value = "欠款金额")
    private BigDecimal debtOrderAmt;

    /** 欠款订单数量（待回款）  */
    @ApiModelProperty(value = "欠款订单数量")
    private Long debtOrderQty;

    /** 欠款门店数量（待回款）  */
    @ApiModelProperty(value = "欠款门店数量")
    private Long debtOrderBranchQty;

    /** 上次销售订单金额 */
    @ApiModelProperty(value = "上次销售订单金额")
    private BigDecimal beforeOrderAmt;

    /** 上次销售订单数量  */
    @ApiModelProperty(value = "上次销售订单数量")
    private Long beforeOrderQty;

    /** 上次销售订单门店数量  */
    @ApiModelProperty(value = "上次销售订单门店数量")
    private Long beforeOrderBranchQty;

    /** 上次销售订单门店数量  */
    @ApiModelProperty(value = "上次销售订单门店平均金额")
    private BigDecimal beforeOrderBranchAvgAmt;

    /** 订单数量同比上升/下降率 */
    @ApiModelProperty(value = "订单数量同比上升/下降率")
    private BigDecimal orderQtyRate;

    /** 销售订单门店数量同比上升/下降率  */
    @ApiModelProperty(value = "销售订单门店数量同比上升/下降率")
    private BigDecimal orderBranchQtyRate;

    /** 销售订单门店平均金额同比上升/下降率  */
    @ApiModelProperty(value = "销售订单门店平均金额同比上升/下降率")
    private BigDecimal orderBranchAmtAvgRate;

    /** 动销SKU数量 */
    @ApiModelProperty(value = "动销SKU数量")
    private Long orderSkuQty;

    /** 上次动销SKU数量 */
    @ApiModelProperty(value = "上次动销SKU数量")
    private Long beforeOrderSkuQty;

    /** 动销SKU数量同比上升/下降率  */
    @ApiModelProperty(value = "动销SKU数量同比上升/下降率")
    private BigDecimal orderSkuRate;

    /** 订单金额同比上升/下降率 */
    @ApiModelProperty(value = "订单金额同比上升/下降率")
    private BigDecimal orderAmtRate;
}
