package com.zksr.product.api.sku.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sku 销售占比数据
 * @date 2024/9/14 9:40
 */
@Data
@ApiModel(description = "sku 销售占比数据")
public class PrdtSkuSaleTotalRateVO {

    /**
     * Map<SkuId, rate>
     * skuid 和 比例
     */
    private Map<Long, RateConfig> rateMap = new HashMap<>();

    public RateConfig getSkuRate(Long skuId) {
        return rateMap.get(skuId);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RateConfig {

        @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
        private BigDecimal rate;

    }
}
