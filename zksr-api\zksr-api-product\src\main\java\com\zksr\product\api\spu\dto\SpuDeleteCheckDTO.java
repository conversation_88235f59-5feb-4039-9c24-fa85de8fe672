package com.zksr.product.api.spu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
* 商品SPU删除 前置校验 DTO
* @date 2025/1/21 17:25
* <AUTHOR>
*/
@ApiModel("商品SPU删除 前置校验 DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpuDeleteCheckDTO {
    private static final long serialVersionUID = 1L;

    /** 商品SPU_id */
    @ApiModelProperty(value = "商品SPU_id")
    private Long spuId;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;
}
