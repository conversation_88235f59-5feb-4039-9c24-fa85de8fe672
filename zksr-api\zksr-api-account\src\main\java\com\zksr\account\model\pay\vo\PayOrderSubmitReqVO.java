package com.zksr.account.model.pay.vo;

import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/7 10:42
 */
@Data
public class PayOrderSubmitReqVO {
    /** 请求IP */
    @ApiModelProperty(value = "来源IP", hidden = true)
    private String ipAddr;

    @ApiModelProperty(value = "订单编号", required = true)
    @NotEmpty(message = "订单编号[orderNo]必填")
    @NotNull(message = "订单编号[orderNo]必填")
    private String orderNo;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    @ApiModelProperty(value = "支付openid", required = true)
    @NotEmpty(message = "openid 必填")
    @NotNull(message = "openid 必填")
    private String openid;

    @ApiModelProperty(value = "sessionKey, 微信b2b支付时需要")
    private String sessionKey;
    
    /**
     * {@link com.zksr.common.core.constant.OrderTypeConstants}
     */
    @ApiModelProperty(value = "订单类型, 0-商城订单 1-入驻商充值 2-门店充值 3-货到付款", required = true, notes = "0-商城订单 1-入驻商充值 2-门店充值")
    @NotNull(message = "订单类型[orderType]必填")
    private Integer orderType;

    /**
     * {@link com.zksr.common.core.enums.PayWayEnum}
     */
    @ApiModelProperty(value = "支付方式, 0-在线支付 1-储值支付", required = true, notes = "0-在线支付 1-储值支付")
    private String payWay;

    /**
     * {@link com.zksr.common.core.enums.PayMethodEnum}
     */
    @ApiModelProperty(value = "wx-微信支付, alipay-支付宝支付")
    private String method = "wx";

    /**
     * 结构参考
     * {
     *     {@linkplain PayOrderSubmitExtras#MERCHANT_ID } : "门店ID",
     *     {@linkplain PayOrderSubmitExtras#SUPPLIER_ID } : "入驻商ID",
     * }
     */
    @ApiModelProperty(value = "额外参数", hidden = true)
    private Map<String, String> extras;

    @ApiModelProperty(value = "支付金额", notes = "可以直接传入, 可以实现流程控制在流程控制中核算金额", hidden = true)
    private BigDecimal payAmt;

    @ApiModelProperty(value = "分账信息", notes = "如果有分账则需要传入", hidden = true)
    private List<OrderSettlementDTO> settlements;

    @ApiModelProperty(value = "appid", hidden = true, notes = "应用appid, 小程序说不传入, 那就只能通过平台 + 订单类型去加载小程序appid")
    private String appid;

}
