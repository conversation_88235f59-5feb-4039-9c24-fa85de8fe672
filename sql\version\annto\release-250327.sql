-- PC 后台储值导入
CREATE TABLE `zksr_account`.`acc_recharge_import` (
                                                      `recharge_import_id` bigint(20) NOT NULL,
                                                      `sys_code` bigint(20) NOT NULL COMMENT '平台商id',
                                                      `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                      `deleted` tinyint(1) DEFAULT '0' COMMENT '0-未删除,1-已删除',
                                                      `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
                                                      `recharge_import_state` tinyint(2) DEFAULT NULL COMMENT '0-初始化, 1-审核成功, 2-作废',
                                                      `remark` varchar(255) DEFAULT NULL COMMENT '后台充值备注',
                                                      `voucher` varchar(255) DEFAULT NULL COMMENT '凭证',
                                                      `counter` bigint(20) DEFAULT NULL COMMENT '计数',
                                                      `recharge_amt` decimal(16,2) DEFAULT NULL COMMENT '总金额',
                                                      PRIMARY KEY (`recharge_import_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='后台导入充值表';

CREATE TABLE `zksr_account`.`acc_recharge_import_dtl` (
                                                          `recharge_import_dtl_id` bigint(20) NOT NULL,
                                                          `sys_code` bigint(20) DEFAULT NULL COMMENT '平台商id',
                                                          `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                          `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                          `recharge_import_id` bigint(20) DEFAULT NULL,
                                                          `branch_id` bigint(20) NOT NULL COMMENT '门店ID',
                                                          `recharge_amt` decimal(16,2) NOT NULL COMMENT '充值金额',
                                                          `recharge_id` bigint(20) DEFAULT NULL COMMENT '关联的充值单ID',
                                                          PRIMARY KEY (`recharge_import_dtl_id`),
                                                          UNIQUE KEY `unique_key` (`recharge_import_id`,`branch_id`),
                                                          KEY `idx_recharge_import_id` (`recharge_import_id`),
                                                          KEY `idx_branch_id` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PC后台导入充值详情';


INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('导入解析', 2860, 7, 'edVZaFbOyEKBNQCxCC', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:import', '#', 'zksr', '2025-03-25 14:23:47', '', NULL, '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('审核批次', 2860, 6, 'DiAHcuSHVDHKANXXIE', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:enable', '#', 'zksr', '2025-03-25 14:23:26', '', NULL, '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('作废批次', 2860, 5, 'hZELNOTsaazeXPfhUw', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:disable', '#', 'zksr', '2025-03-25 14:23:12', '', NULL, '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('获取审核验证码', 2860, 4, 'NdNeqJJKItcOIpcOUn', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:sms', '#', 'zksr', '2025-03-25 14:22:51', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批次详情', 2860, 3, 'nGsizFRJFBJmynZeGs', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:query', '#', 'zksr', '2025-03-25 14:22:31', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('编辑充值批次', 2860, 2, 'SHhJUJtmnXoWzELcEU', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:edit', '#', 'zksr', '2025-03-25 14:22:18', '', NULL, '', 'partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('新增充值批次', 2860, 1, 'vHXpfrtBNKujIwMcjP', 'UyWNOaXJckpimNhayc', '', NULL, NULL, 1, 0, 'F', '0', '0', 'recharge:rechargeImport:add', '#', 'zksr', '2025-03-25 14:21:34', '', NULL, '', 'dc,partner');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('PC门店储值', 2031, 12, 'UyWNOaXJckpimNhayc', '2031', 'menberStored', 'operation/menberStored/index', NULL, 1, 0, 'C', '0', '0', 'recharge:rechargeImport:list', '#', 'zksr', '2025-03-25 08:56:04', 'zksr', '2025-03-25 14:21:05', '', 'dc,partner');

-- 增加充值来源
ALTER TABLE `zksr_account`.`acc_recharge`
    ADD COLUMN `source` varchar(6) NULL DEFAULT 'app' COMMENT 'app-用户端, pc-后台' AFTER `give_amt`
;

-- sku 所以
ALTER TABLE `zksr_trade`.`trd_supplier_after_dtl`
    ADD INDEX `idx_sku_id`(`sku_id`) USING BTREE;

ALTER TABLE `zksr_trade`.`trd_supplier_order_dtl`
    ADD INDEX `idx_sku_id`(`sku_id`) USING BTREE;


-- 入驻商订单表增加字段
ALTER TABLE `zksr_trade`.`trd_supplier_order`
    ADD COLUMN `sub_refund_amt` DECIMAL(12,2) DEFAULT 0 COMMENT '入驻商已退款金额（不包括优惠）' AFTER `sub_pay_fee`,
    ADD COLUMN `sub_refund_fee` decimal(12,2) DEFAULT 0 COMMENT '入驻商已退款手续费金额' AFTER `sub_refund_amt`
;

-- 修复历史入驻商订单 已退款金额和手续费数据 SQL
UPDATE `zksr_trade`.`trd_supplier_order` tso
    JOIN (
        SELECT
            tsa.supplier_order_no,
            SUM(tsa.sub_refund_amt) AS sub_refund_amt,
            SUM(tsa.sub_refund_fee) AS sub_refund_fee
        FROM
            `zksr_trade`.`trd_supplier_after` tsa
            LEFT JOIN `zksr_trade`.`trd_supplier_after_dtl` tsad ON tsa.supplier_after_id = tsad.supplier_after_id
        WHERE
            tsad.is_cancel != 1
            AND approve_state = 1
        GROUP BY
            tsa.supplier_order_no
    ) updateTable ON tso.supplier_order_no = updateTable.supplier_order_no
    SET
        tso.sub_refund_amt = updateTable.sub_refund_amt,
        tso.sub_refund_fee = updateTable.sub_refund_fee
;


-- 同步日志 日志重发功能配置权限至平台商
INSERT INTO `zksr_cloud`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('同步日志重发', 2454, 1, 'IlSPfUlFhmwdyXHmgR', '2454', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:log:magRetry', '#', 'zksr', '2025-03-25 15:11:33', '', NULL, '', 'partner');

-- 入驻商 - 开放接口设置  接口配置显示异常 权限配置
INSERT INTO `zksr_cloud`.`sys_menu`(`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('可视化主表列表查询', 2532, 0, 'pFuIQxcWgXySLHMEJR', '2532', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:visualSettingMaster:list', '#', 'zksr', '2025-03-25 15:21:45', '', NULL, '', 'partner');


INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ('批量上下架操作', 2131, 9, 'czOutxKVKRyXeBCKCm', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:batchShelf', '#', 'zksr', '2025-03-24 10:50:43', '', NULL, '', 'software,partner,dc');
INSERT INTO `zksr_cloud`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `menu_code`, `menu_pcode`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `func_scop`) VALUES ( '上下架操作', 2131, 9, 'FiCpYBBvwvLAPygwvV', '2131', '', NULL, NULL, 1, 0, 'F', '0', '0', 'product:spu:editShelf', '#', 'zksr', '2025-03-24 10:51:34', '', NULL, '', 'software,partner,dc');

ALTER TABLE `zksr_cloud`.`wx_qr_data`
    MODIFY COLUMN `qr_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '二维码value' AFTER `qr_key`;
---业务员目标设置新增月下单量目标 客单价目标 首次动销数量
ALTER TABLE `zksr_member`.`mem_colonel_target`
ADD COLUMN `month_order_count` INT DEFAULT NULL COMMENT '月下单量',
ADD COLUMN `month_avg_order_value` DECIMAL(19,6) DEFAULT NULL COMMENT '月客单价',
ADD COLUMN `month_first_sale_count` INT DEFAULT NULL COMMENT '月首次动销数量';