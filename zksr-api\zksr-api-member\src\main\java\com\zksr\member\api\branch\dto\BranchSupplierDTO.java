package com.zksr.member.api.branch.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchSupplierDTO implements Serializable {

    private static final long serialVersionUID = -5002543633371942671L;

    /**
     * 门店ID
     */
    private Long branchId;
    /**
     * 入驻商ID 集合
     */
    private List<Long> supplierIds;

    /**
     * 门店ID集合
     */
    private List<Long> branchIds;
    /**
     * 入驻商ID
     */
    private Long supplierId;

    /**
     * 平台编号
     */
    private Long sysCode;

    public BranchSupplierDTO(List<Long> branchIds, Long supplierId, Long sysCode) {
        this.branchIds = branchIds;
        this.supplierId = supplierId;
        this.sysCode = sysCode;
    }

    public BranchSupplierDTO(Long branchId, List<Long> supplierIds, Long sysCode) {
        this.branchId = branchId;
        this.supplierIds = supplierIds;
        this.sysCode = sysCode;
    }
}
